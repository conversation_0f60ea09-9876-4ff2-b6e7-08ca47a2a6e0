<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>da-bom</artifactId>
        <groupId>com.aisino.da</groupId>
        <version>2.3-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.aisino.da</groupId>
    <artifactId>da-api-service</artifactId>
    <version>2.3-SNAPSHOT</version>
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.aisino.aps</groupId>
            <artifactId>aps-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aisino.aps</groupId>
            <artifactId>aps-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aisino.aps</groupId>
            <artifactId>aps-log</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aisino.da</groupId>
            <artifactId>da-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aisino.da</groupId>
            <artifactId>da-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aisino.da</groupId>
            <artifactId>da-api</artifactId>
        </dependency>
    </dependencies>
</project>
