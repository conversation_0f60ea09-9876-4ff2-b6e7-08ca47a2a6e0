default:
  image: ************:31104/aps/maven:aisino-3.8

mvn-package:
  only:
    - master
    - develop
    # - /^release-.*$/i
    
  stage: build
  
  tags:
    - hb-docker
    
  script:
    - mvn clean package sonar:sonar -Dsonar.projectKey=da-fc -Dsonar.host.url=http://************:9008 -Dsonar.login=**************************************** -Dsonar.language=java -Dsonar.sourceEncoding=UTF-8 -Dsonar.projectName=电子档案_文件收集
    - mvn clean deploy -U -P no-config -Dmaven.test.skip=true

