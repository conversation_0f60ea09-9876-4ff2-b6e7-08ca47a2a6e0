default:
  image: ************:31104/aps/maven:aisino-3.8

mvn-package:
  only:
    - develop
    - master
    - /^release-.*$/i
    
  stage: build
  
  tags:
    - hb-docker
    
  script:
    - mvn clean package sonar:sonar -Dsonar.projectKey=da-afk -Dsonar.host.url=http://************:9008 -Dsonar.login=**************************************** -Dsonar.language=java -Dsonar.sourceEncoding=UTF-8 -Dsonar.projectName=电子档案_档案整理
    - mvn clean deploy -U -P no-config -Dmaven.test.skip=true

