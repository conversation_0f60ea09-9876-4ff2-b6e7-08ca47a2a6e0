<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.aisino.aps</groupId>
    <artifactId>aps-boot</artifactId>
    <version>2.0.0</version>
  </parent>
  <groupId>com.aisino.aps</groupId>
  <artifactId>da-boot</artifactId>
  <version>2.0.0</version>
  <name>da-boot</name>
  <properties>
    <aps.bom.version>2.1.18</aps.bom.version>
    <aps.lock.version>1.3.0</aps.lock.version>
    <da.version>2.3-SNAPSHOT</da.version>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.aisino.aps</groupId>
        <artifactId>aps-bom</artifactId>
        <version>${aps.bom.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.aisino.da</groupId>
        <artifactId>da-bom</artifactId>
        <version>${da.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <dependencies>
    <dependency>
      <groupId>com.aisino.aps</groupId>
      <artifactId>aps-bi-data</artifactId>
      <version>2.1.2</version>
    </dependency>
    <dependency>
      <groupId>com.aisino.aps</groupId>
      <artifactId>aps-bi-design</artifactId>
      <version>2.1.2</version>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-compress</artifactId>
    </dependency>
    <dependency>
      <groupId>com.twelvemonkeys.imageio</groupId>
      <artifactId>imageio-jpeg</artifactId>
    </dependency>
    <dependency>
      <groupId>org.ofdrw</groupId>
      <artifactId>ofdrw-tool</artifactId>
    </dependency>
    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
    </dependency>
    <dependency>
      <groupId>org.ofdrw</groupId>
      <artifactId>ofdrw-full</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-slf4j-impl</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.aisino.aps</groupId>
      <artifactId>aps-lock-api</artifactId>
      <version>${aps.lock.version}</version>
    </dependency>
    <dependency>
      <groupId>com.aisino.aps</groupId>
      <artifactId>aps-lock-redis</artifactId>
      <version>${aps.lock.version}</version>
    </dependency>
    <dependency>
      <groupId>com.aisino.aps</groupId>
      <artifactId>aps-lock-default</artifactId>
      <version>${aps.lock.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.aisino.aosplus</groupId>
      <artifactId>aosplus-mis_log</artifactId>
      <version>3.0.11</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.aisino.aps</groupId>
      <artifactId>aps-starter</artifactId>
      <version>${aps.bom.version}</version>
      <type>pom</type>
    </dependency>
    <dependency>
      <groupId>com.aisino.aps</groupId>
      <artifactId>aps-imm</artifactId>
      <version>1.18.87</version>
      <exclusions>
        <exclusion>
          <groupId>org.apache.pdfbox</groupId>
          <artifactId>pdfbox</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.aisino.aps</groupId>
      <artifactId>invoice-manage</artifactId>
      <version>1.18.50</version>
    </dependency>
    <dependency>
      <groupId>com.aisino.da</groupId>
      <artifactId>da-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.aisino.da</groupId>
      <artifactId>da-common</artifactId>
    </dependency>
    <dependency>
      <groupId>com.aisino.da</groupId>
      <artifactId>da-bbs</artifactId>
    </dependency>
    <dependency>
      <groupId>com.aisino.da</groupId>
      <artifactId>da-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.aisino.da</groupId>
      <artifactId>da-stat</artifactId>
    </dependency>
    <dependency>
      <groupId>com.aisino.da</groupId>
      <artifactId>da-afk</artifactId>
    </dependency>
    <dependency>
      <groupId>com.aisino.da</groupId>
      <artifactId>da-fc</artifactId>
    </dependency>
    <dependency>
      <groupId>com.aisino.da</groupId>
      <artifactId>da-aua</artifactId>
    </dependency>
    <dependency>
      <groupId>com.aisino.da</groupId>
      <artifactId>da-ea</artifactId>
    </dependency>
    <dependency>
      <groupId>com.aisino.da</groupId>
      <artifactId>da-easd</artifactId>
    </dependency>
    <dependency>
      <groupId>com.aisino.da</groupId>
      <artifactId>da-suai</artifactId>
    </dependency>
    <dependency>
      <groupId>com.aisino.da</groupId>
      <artifactId>da-sign</artifactId>
    </dependency>
    <dependency>
      <groupId>com.aisino.da</groupId>
      <artifactId>da-es</artifactId>
    </dependency>
    <dependency>
      <groupId>com.aisino.da</groupId>
      <artifactId>da-kingdee</artifactId>
      <version>2.2-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.aisino.da</groupId>
      <artifactId>da-jdy</artifactId>
      <version>2.2-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
  </dependencies>
  <build>
    <finalName>server</finalName>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <layers>
            <enabled>true</enabled>
            <configuration>${project.basedir}/docker/layers.xml</configuration>
          </layers>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
