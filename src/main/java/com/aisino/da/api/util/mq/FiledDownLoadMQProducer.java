package com.aisino.da.api.util.mq;

import com.aisino.aosplus.core.ConfigHelper;
import com.aisino.aosplus.core.util.JsonUtil;
import com.aisino.aps.mq.Producer;
import com.aisino.aps.mq.Queue;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 创建收集文件mq生产者
 */
public class FiledDownLoadMQProducer
{
    private static Map<String, Producer> producerMap = new HashMap<>();
    private static Map<String, Producer> producercollectionMap = new HashMap<>();

    private static Map<String, Producer> billtpapicollectionMap = new HashMap<>();

    private static Map<String, Producer> bankstatementtpapicollectionMap = new HashMap<>();

    private static Map<String, Producer> tickettpapicollectionMap = new HashMap<>();

    private static Map<String, Producer> banksreceipttpapicollectionMap = new HashMap<>();

    private static Log log = LogFactory.getLog(FiledDownLoadMQProducer.class);
    static
    {
        // 创建队列和producer
        try
        {
            //声明下载文件队列
            creatQueuesAndProducer(ConfigHelper.getString("DAfile-queue"), "DAfile-queue", producerMap);
            //声明mq收集文件队列
            creatQueuesAndProducer(ConfigHelper.getString("DAfile-queue-collection"), "DAfile-queue-collection",producercollectionMap);
            //声明异步收集单据队列
            creatQueuesAndProducer(ConfigHelper.getString("DAfile-queue-bill-tpapi"), "DAfile-queue-bill-tpapi",billtpapicollectionMap);
            //声明异步收集银行回单队列
            creatQueuesAndProducer(ConfigHelper.getString("DAfile-queue-banksreceipt-tpapi"), "DAfile-queue-banksreceipt-tpapi",banksreceipttpapicollectionMap);
            //声明异步收集银行流水队列
            creatQueuesAndProducer(ConfigHelper.getString("DAfile-queue-bankstatement-tpapi"), "DAfile-queue-bankstatement-tpapi",bankstatementtpapicollectionMap);
            //声明异步收集票据资料队列
            creatQueuesAndProducer(ConfigHelper.getString("DAfile-queue-ticket-tpapi"), "DAfile-queue-ticket-tpapi",tickettpapicollectionMap);

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static void creatQueuesAndProducer(String eaQueueName,String configkey, Map producerMap){
        //create 生产者,下载文件
        Queue eaQueue = DAMQFactory.mq.queue(eaQueueName);
        eaQueue.declare();
        //create producer
        Producer eaProducer = DAMQFactory.mq.producer(eaQueueName);
        producerMap.put(configkey,eaProducer);
    }

    /**
     * 发送下载文件请求
     * @param data
     */
    public static void sendMsg(Map data,String configkey){
        try{
            // 发送消息
            Producer producer = producerMap.get(configkey);
             
            producer.send(JsonUtil.toJSON(data).getBytes(StandardCharsets.UTF_8));
        } catch (Exception e)
        {
            log.error("单据或凭证发送消息时发生错误============================>" + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void sendBillMsg(Map data, String configkey)
    {
        try{
            // 发送消息
            Producer producer = billtpapicollectionMap.get(configkey);
             
            producer.send(JsonUtil.toJSON(data).getBytes(StandardCharsets.UTF_8));
        } catch (Exception e)
        {
            log.error("单据或凭证发送消息时发生错误============================>" + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void sendBankstatementMsg(Map data, String configkey)
    {
        try{
            // 发送消息
            Producer producer = bankstatementtpapicollectionMap.get(configkey);
             
            producer.send(JsonUtil.toJSON(data).getBytes(StandardCharsets.UTF_8));
        } catch (Exception e)
        {
            log.error("银行流水发送消息时发生错误============================>" + e.getMessage());
            e.printStackTrace();
        }
    }
    public static void sendTicketMsg(Map data, String configkey)
    {
        try{
            // 发送消息
            Producer producer = tickettpapicollectionMap.get(configkey);
             
            producer.send(JsonUtil.toJSON(data).getBytes(StandardCharsets.UTF_8));
        } catch (Exception e)
        {
            log.error("票据资料发送消息时发生错误============================>" + e.getMessage());
            e.printStackTrace();
        }
    }
    public static void sendBanksReceiptMsg(Map data, String configkey)
    {
        try{
            // 发送消息
            Producer producer = banksreceipttpapicollectionMap.get(configkey);
             
            producer.send(JsonUtil.toJSON(data).getBytes(StandardCharsets.UTF_8));
        } catch (Exception e)
        {
            log.error("单据或凭证发送消息时发生错误============================>" + e.getMessage());
            e.printStackTrace();
        }
    }
}
