package com.aisino.da.api.util.mq;

import java.util.Map;

/**
 * @description:
 * @date: 2023/5/27 17:38
 * @author: wanglk
 */
public class DAMQSendUtil {

    public static <T> void fileDownloadSendMsg(Map msg){
        FiledDownLoadMQProducer.sendMsg(msg,"DAfile-queue");
    }

    public static <T> void collectionDtaSendMsg(Map msg){
        FiledDownLoadMQProducer.sendMsg(msg, "DAfile-queue-collection");
    }

    public static <T> void collectionTpapiSendMsg(Map msg){
        FiledDownLoadMQProducer.sendBillMsg(msg,"DAfile-queue-bill-tpapi");
    }

    public static <T> void collectionTpapiBankstatementSendMsg(Map msg){
        FiledDownLoadMQProducer.sendBankstatementMsg(msg,"DAfile-queue-bankstatement-tpapi");
    }
    public static <T> void collectionTpapiTicketSendMsg(Map msg){
        FiledDownLoadMQProducer.sendTicketMsg(msg,"DAfile-queue-ticket-tpapi");
    }

    public static <T> void collectionTpapiBanksReceiptSendMsg(Map msg){
        FiledDownLoadMQProducer.sendBanksReceiptMsg(msg,"DAfile-queue-banksreceipt-tpapi");
    }
}
