package com.aisino.da.api.action.outapi.http;

import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.annotation.Action;
import com.aisino.aosplus.core.mvc.annotation.NoNeedLogin;
import com.aisino.aosplus.core.mvc.annotation.Request;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.da.api.service.outapi.http.DaCollectionInterface;
import com.aisino.da.api.service.outapi.http.DaPushCollectionInterface;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * 通过开放平台接收采集数据时候，需要根据参数转发不同接口，
 * 因此需要在“资料收集标准接口-接口文档”基础上补充参数？？？
 */
@NoNeedLogin
@Action("/da/api/interfacetype")
public class DaInterfaceTypeCollectionAction
{
    @Inject
    DaPushCollectionInterface daPushCollectionInterface;
    @Inject
    DaCollectionInterface daCollectionInterface;
    private static final Logger LOG = LoggerFactory.getLogger(DaInterfaceTypeCollectionAction.class);
    @Request.Post("collectionData")
    public Map collectionData(Params params)
    {
        //String interfacetype = params.getString("interfacetype");
        LOG.info("/da/api/interfacetype/collectionData参数:"+params);
        Map<String, Object> fieldMap = params.getFieldMap();
        Params par = new Params();
        if(fieldMap.containsKey("body")){
            Map body = params.getMap("body");
            par = new Params(body);
        }else {
            par = params;
        }
        /**
         * 为1  ，档案请求后，第三方调用的采集数据接口
         * 为2  ，第三方主动推送采集数据。
         * 1或者2	位置为最外层
         * 为1  ，档案请求后，第三方调用的采集数据接口（URL为"/da/api/collection/collectionData"的接口报文），
         * 为2  ，第三方主动推送采集数据（URL为“/da/api/pushcollection/collectionData”的接口报文）。
         */
        String interfacetype = par.getString("interfacetype");
        if("2".equals(interfacetype)){
           return daPushCollectionInterface.collectionData(params);
        }else {
            return daCollectionInterface.collectionData(params);
        }
    }
}
