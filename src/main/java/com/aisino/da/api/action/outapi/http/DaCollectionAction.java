package com.aisino.da.api.action.outapi.http;

import com.aisino.aosplus.core.dao.DbHelper;
import com.aisino.aosplus.core.dao.DbService;
import com.aisino.aosplus.core.ioc.BeanHelper;
import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.annotation.Action;
import com.aisino.aosplus.core.mvc.annotation.NoNeedLogin;
import com.aisino.aosplus.core.mvc.annotation.Request;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.aosplus.core.util.JsonUtil;
import com.aisino.aosplus.core.util.StringUtil;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import com.aisino.aps.apsdb.dbhelper.ApsContextDbHelper;
import com.aisino.aps.common.ls.LoginUtil;
import com.aisino.aps.common.utils.LogUtil;
import com.aisino.da.api.constant.CollectConstant;
import com.aisino.da.api.dao.CollectLogDAOInterface;
import com.aisino.da.api.handler.AccountBookCollectDataServiceInterface;
import com.aisino.da.api.handler.AccountReportCollectDataServiceInterface;
import com.aisino.da.api.handler.OtherAccountReportCollectDataServiceInterface;
import com.aisino.da.api.handler.VoucherCollectDataServiceInterface;
import com.aisino.da.api.service.publicother.OtherCollectDataService;
import com.aisino.da.api.service.publicother.SaveAndCheckOtherDataDetialService;
import com.aisino.da.api.util.ENComParametersUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

@NoNeedLogin
@Action("da/api/collection")
public class DaCollectionAction
{
    @Inject
    private CollectLogDAOInterface collectLogDAOInterface;
    @Inject
    VoucherCollectDataServiceInterface voucherCollectDataServiceInterface;
    @Inject
    AccountBookCollectDataServiceInterface accountBookCollectDataServiceInterface;
    @Inject
    AccountReportCollectDataServiceInterface accountReportCollectDataServiceInterface;
    @Inject
    OtherAccountReportCollectDataServiceInterface otherAccountReportCollectDataServiceInterface;

    private static final Logger LOG = LoggerFactory.getLogger(DaCollectionAction.class);
    @Request.Post("collectionData")
    public Map collectionData(Params params)
    {
        Map<String, Object> fieldMap = params.getFieldMap();
        Params par = new Params();
        if(fieldMap.containsKey("body")){
            Map body = params.getMap("body");
            par = new Params(body);
        }else {
            par = params;
        }
        //String type = par.getString("资料类型");
        ENComParametersUtil enComParametersUtil = new ENComParametersUtil();
        String type = enComParametersUtil.getCloglistflag(par,"资料类型");//par.getString("资料类型");
        String tenantDsId = par.getString("tenantdsid");
        if (StringUtils.isBlank(tenantDsId))
        {
            tenantDsId = params.getString("数据源");
            params.put("tenantdsid", tenantDsId);
        }else
        {
            params.put("数据源", tenantDsId);
        }
        if(LoginUtil.isSaas()){
            /*Map<String, Map> allDsMap = ConfigHelper.getAllDsMap();
            if(!allDsMap.containsKey(tenantDsId)){
                return makeRtnMapByErrorRtn("tenantdsid不存在"+allDsMap,params);
            }*/
            if (StringUtils.isBlank(tenantDsId))
            {
                tenantDsId = params.getString("数据源");
            }

            try {
                DbHelper.setCurrentDsID(tenantDsId);
            }catch (Exception e){
                return makeRtnMapByErrorRtn("tenantdsid不存在"+e.getLocalizedMessage(),par);
            }
        }
        //String collectType = par.getString(CollectConstant.COLLECTTYPEKEY);//"0代表文件1代表json
        String collectType = enComParametersUtil.getCollecttype(par,CollectConstant.COLLECTTYPEKEY);
        Map msgmap = new HashMap<>();
        //公共校验
        if (StringUtil.isNotEmpty(type) && CollectConstant.BILL_NAME.equalsIgnoreCase(type))
        {
            try
            {
                OtherCollectDataService bean = BeanHelper.getBean(OtherCollectDataService.class);
                SaveAndCheckOtherDataDetialService adapt = bean.getAdapt(CollectConstant.BILL_NAME);
                Map map = adapt.collectOtherDetial(par);
             //   collectionDataService.collectInformation(par);


                String msg = CollectionUtil.getStringFromMap(map, "msg");
                if (!CollectionUtil.getBoolean(map, "isSuccess", true)) {
                    return makeRtnMapByErrorRtn(msg,par);
                }else {
                    return makeRtnMapBySuccess(msg,par);
                }
            }catch (Exception e)
            {
                String stringByException = LogUtil.getStringByException(e);
                stringByException = stringByException.replace("'","“");
                collectLogDAOInterface.insertWebInterfaceErrorLog(stringByException,par,e,"单据收集报错");
                LOG.error("收集单据数据时发生异常参数为【" + par + "】错误详情为【" + e.getMessage() + "】");
                ApsContextDb apsContext = new ApsContextDb();
                DbService db = apsContext.getDb(tenantDsId);
                db.commit();
                return makeRtnMapByException("单据数据处理发生异常" + e.getMessage(),par,e);
            }
        }else if (StringUtil.isNotEmpty(type) && CollectConstant.VOUCHER_NAME.equalsIgnoreCase(type)){
            try
            {
                LOG.info("HTTP,记账凭证开始接收数据----参数为{}",par);
                if("1".equals(collectType)){
                    Map rtn = voucherCollectDataServiceInterface.voucherInterfaceSyncCollectDataFromHTTP(par);
                    String msg = CollectionUtil.getStringFromMap(rtn, "msg");
                    if (!CollectionUtil.getBoolean(rtn, "isSuccess", true)) {
                        return makeRtnMapByErrorRtn(msg,par);
                    }else {
                        return makeRtnMapBySuccess(msg,par);
                    }
                }else if("0".equals(collectType)){
                    collectLogDAOInterface.insertWebInterfaceErrorLog("记录文件包信息",par,null,"凭证文件包");
                    Map rtn = voucherCollectDataServiceInterface.voucherInterfaceSyncCollectDataFromHTTP(par);
                    String msg = CollectionUtil.getStringFromMap(rtn, "msg");
                    if (!CollectionUtil.getBoolean(rtn, "isSuccess", true)) {
                        return makeRtnMapByErrorRtn(msg,par);
                    }else {
                        return makeRtnMapBySuccess(msg,par);
                    }
                }else {
                    return makeRtnMapByErrorParam(CollectConstant.COLLECTTYPEKEY+"，参数不匹配0代表文件1代表json",par,CollectConstant.COLLECTTYPEKEY);
                }
            }catch (Exception e)
            {
                String stringByException = LogUtil.getStringByException(e);
                stringByException = stringByException.replace("'","“");
                collectLogDAOInterface.insertWebInterfaceErrorLog(stringByException,par,e,"凭证报错");
                return makeRtnMapByException("记账凭证数据处理发生异常" + e.getMessage(),par,e);
            }
        }else if (StringUtil.isNotEmpty(type) && type.equalsIgnoreCase(CollectConstant.ACCOUNTBOOK_NAME)){
            try
            {
                if("0".equals(collectType)){
                    collectLogDAOInterface.insertWebInterfaceErrorLog("记录文件包信息",par,null,"账簿文件包");
                }
                Map map = accountBookCollectDataServiceInterface.accountBookInterfaceSyncCollectDataFromHTTP(par);
                String msg = CollectionUtil.getStringFromMap(map, "msg");
                if (!CollectionUtil.getBoolean(map, "isSuccess", true)) {
                    return makeRtnMapByErrorRtn(msg,par);
                }else {
                    return makeRtnMapBySuccess(msg,par);
                }
            }catch (Exception e)
            {
                String stringByException = LogUtil.getStringByException(e);
                stringByException = stringByException.replace("'","“");
                collectLogDAOInterface.insertWebInterfaceErrorLog(stringByException,par,e,"账簿报错");
                return makeRtnMapByException("会计账簿数据处理发生异常" + e.getMessage(),par,e);
            }
        }else if (StringUtil.isNotEmpty(type) && type.equalsIgnoreCase(CollectConstant.ACCOUNTREPORT_NAME)){
            try
            {
                if("0".equals(collectType)){
                    collectLogDAOInterface.insertWebInterfaceErrorLog("记录文件包信息",par,null,"报告文件包");
                }
                Map map = accountReportCollectDataServiceInterface.accountReportInterfaceSyncCollectDataFromHTTP(par);
                String msg = CollectionUtil.getStringFromMap(map, "msg");
                if (!CollectionUtil.getBoolean(map, "isSuccess", true)) {
                    return makeRtnMapByErrorRtn(msg,par);
                }else {
                    return makeRtnMapBySuccess(msg,par);
                }
            }catch (Exception e)
            {
                String stringByException = LogUtil.getStringByException(e);
                stringByException = stringByException.replace("'","“");
                collectLogDAOInterface.insertWebInterfaceErrorLog(stringByException,par,e,"报告报错");
                return makeRtnMapByException("会计报告数据处理发生异常" + e.getMessage(),par,e);
            }
        }else if (StringUtil.isNotEmpty(type) && type.equalsIgnoreCase(CollectConstant.OTHERACCOUNTREPORT_NAME)){
            try
            {
                /*if("0".equals(collectType)){
                    collectLogDAOInterface.insertWebInterfaceErrorLog("记录文件包信息",par,null,"其他会计资料");
                }*/
                Map map = otherAccountReportCollectDataServiceInterface.otherAccountReportInterfaceSyncCollectDataFromHTTP(par);
                String msg = CollectionUtil.getStringFromMap(map, "msg");
                if (!CollectionUtil.getBoolean(map, "isSuccess", true)) {
                    return makeRtnMapByErrorRtn(msg,par);
                }else {
                    return makeRtnMapBySuccess(msg,par);
                }
            }catch (Exception e)
            {
                String stringByException = LogUtil.getStringByException(e);
                stringByException = stringByException.replace("'","“");
                collectLogDAOInterface.insertWebInterfaceErrorLog(stringByException,par,e,"其他会计资料报错");
                return makeRtnMapByException("其他会计资料数据处理发生异常" + e.getMessage(),par,e);
            }
        }
        else if (StringUtil.isNotEmpty(type) && type.equalsIgnoreCase(CollectConstant.BANKRECEIPT_NAME))
        {
            try
            {
                if("0".equals(collectType))
                {
                    return makeRtnMapByErrorRtn("银行回单报文解析仅支持报文格式",par);
                }

                OtherCollectDataService bean = BeanHelper.getBean(OtherCollectDataService.class);
                SaveAndCheckOtherDataDetialService adapt = bean.getAdapt(CollectConstant.BANKRECEIPT_NAME);
                Map map = adapt.collectOtherDetial(par);
                String msg = CollectionUtil.getStringFromMap(map, "msg");
                if (!CollectionUtil.getBoolean(map, "isSuccess", true)) {
                    return makeRtnMapByErrorRtn(msg,par);
                }else {
                    return makeRtnMapBySuccess(msg,par);
                }
            }catch (Exception e)
            {
                String stringByException = LogUtil.getStringByException(e);
                stringByException = stringByException.replace("'","“");
                collectLogDAOInterface.insertWebInterfaceErrorLog(stringByException,par,e,"银行回单报错");
                LOG.error("银行回单数据时发生异常参数为【" + par + "】错误详情为【" + e.getMessage() + "】");
                return makeRtnMapByException("银行回单数据处理发生异常" + e.getMessage(),par,e);
            }
        }
        else if (StringUtil.isNotEmpty(type) && type.equalsIgnoreCase(CollectConstant.BANKSTATEMENT_NAME))
        {
            try
            {
                if("0".equals(collectType))
                {
                    return makeRtnMapByErrorRtn("银行流水单报文解析仅支持报文格式",par);
                }
                OtherCollectDataService bean = BeanHelper.getBean(OtherCollectDataService.class);
                SaveAndCheckOtherDataDetialService adapt = bean.getAdapt(CollectConstant.BANKSTATEMENT_NAME);
                Map map = adapt.collectOtherDetial(par);
                String msg = CollectionUtil.getStringFromMap(map, "msg");
                if (!CollectionUtil.getBoolean(map, "isSuccess", true)) {
                    return makeRtnMapByErrorRtn(msg,par);
                }else {
                    return makeRtnMapBySuccess(msg,par);
                }
            }catch (Exception e)
            {
                String stringByException = LogUtil.getStringByException(e);
                stringByException = stringByException.replace("'","“");
                collectLogDAOInterface.insertWebInterfaceErrorLog(stringByException,par,e,"银行流水报错");
                LOG.error("银行流水数据时发生异常参数为【" + par + "】错误详情为【" + e.getMessage() + "】");

                return makeRtnMapByException("银行流水数据处理发生异常" + e.getMessage(),par,e);
            }
        }else if (StringUtil.isNotEmpty(type) && type.equalsIgnoreCase(CollectConstant.TICKET_NAME))
        {
            try
            {
                if("0".equals(collectType))
                {
                    return makeRtnMapByErrorRtn("票据资料报文解析仅支持报文格式",par);
                }
                OtherCollectDataService bean = BeanHelper.getBean(OtherCollectDataService.class);
                SaveAndCheckOtherDataDetialService adapt = bean.getAdapt(CollectConstant.TICKET_NAME);
                Map map = adapt.collectOtherDetial(par);
                String msg = CollectionUtil.getStringFromMap(map, "msg");
                if (!CollectionUtil.getBoolean(map, "isSuccess", true)) {
                    return makeRtnMapByErrorRtn(msg,par);
                }else {
                    return makeRtnMapBySuccess(msg,par);
                }
            }catch (Exception e)
            {
                String stringByException = LogUtil.getStringByException(e);
                stringByException = stringByException.replace("'","“");
                collectLogDAOInterface.insertWebInterfaceErrorLog(stringByException,par,e,"票据资料报错");
                LOG.error("票据资料数据时发生异常参数为【" + par + "】错误详情为【" + e.getMessage() + "】");

                return makeRtnMapByException("票据资料数据处理发生异常" + e.getMessage(),par,e);
            }
        }else if(StringUtil.isNotEmpty(type) && type.equalsIgnoreCase(CollectConstant.PZZLREL_NAME)){//凭证关联关系
            try
            {
                if("0".equals(collectType))
                {
                    return makeRtnMapByErrorRtn("凭证关联关系报文解析仅支持报文格式",par);
                }
                OtherCollectDataService bean = BeanHelper.getBean(OtherCollectDataService.class);
                SaveAndCheckOtherDataDetialService adapt = bean.getAdapt(CollectConstant.PZZLREL_NAME);//凭证关联关系
                Map map = adapt.collectOtherDetial(par);
                String msg = CollectionUtil.getStringFromMap(map, "msg");
                if (!CollectionUtil.getBoolean(map, "isSuccess", true)) {
                    return makeRtnMapByErrorRtn(msg,par);
                }else {
                    return makeRtnMapBySuccess(msg,par);
                }
            }catch (Exception e)
            {
                String stringByException = LogUtil.getStringByException(e);
                stringByException = stringByException.replace("'","“");
                collectLogDAOInterface.insertWebInterfaceErrorLog(stringByException,par,e,"凭证关联关系报错");
                LOG.error("凭证关联关系数据时发生异常参数为【" + par + "】错误详情为【" + e.getMessage() + "】");

                return makeRtnMapByException("凭证关联关系数据处理发生异常" + e.getMessage(),par,e);
            }
        }
        else
        {
            msgmap.put("code","1000");
            msgmap.put("msg","未知的数据处理类型【" + type + "】");
            LOG.info("数据收集失败,接收数据成功-未知的数据处理类型-请查看日志--参数为{}", JsonUtil.toJSON(params));
            return msgmap;
        }
    }
    private Map makeRtnMapBySuccess(String msg,Params params){
        Map msgmap = new HashMap<>();
        msgmap.put("code","0000");
        msgmap.put("是否成功","1");
        msgmap.put("msg",msg);
        LOG.info("HTTP,接收数据成功----参数为{}",params.getFieldMap());
        return msgmap;
    }
    private Map makeRtnMapByErrorParam(String msg,Params params,String key){
        Map msgmap = new HashMap<>();
        msgmap.put("code","1000");
        msgmap.put("是否成功","0");
        msgmap.put("msg",msg);
        LOG.error("HTTP,接收数据失败----"+key+"为{}，异常信息为{},接收参数为",
                params.getString(key),msg,params.getFieldMap());
        return msgmap;
    }
    private Map makeRtnMapByErrorRtn(String msg,Params params){
        Map msgmap = new HashMap<>();
        msgmap.put("code","1000");
        msgmap.put("是否成功","0");
        msgmap.put("msg",msg);
        LOG.error("HTTP,接收数据处理失败----异常信息为{},接收参数为",
                msg,params.getFieldMap());
        return msgmap;
    }
    private Map makeRtnMapByException(String msg,Params params,Exception e){
        Map msgmap = new HashMap<>();
        msgmap.put("code","1000");
        msgmap.put("是否成功","0");
        msgmap.put("msg",msg);
        LOG.error("记账凭证接收数据后，出现异常，参数为{},异常为{}",params, LogUtil.getStringByException(e));
        return msgmap;
    }
}
