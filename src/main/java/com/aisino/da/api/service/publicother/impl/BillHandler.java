package com.aisino.da.api.service.publicother.impl;

import com.aisino.aosplus.core.ConfigHelper;
import com.aisino.aosplus.core.dao.DbService;
import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.annotation.Service;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.aosplus.core.util.Guid;
import com.aisino.aosplus.core.util.JsonUtil;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import com.aisino.da.api.constant.CollectConstant;
import com.aisino.da.api.dao.CollectLogDAOInterface;
import com.aisino.da.api.dao.DaGetBillDetialDao;
import com.aisino.da.api.entity.EsAddViewVO;
import com.aisino.da.api.entity.EsDeleteViewVO;
import com.aisino.da.api.entity.HadlerResultViewVO;
import com.aisino.da.api.handler.abstracts.AbstractApiHandler;
import com.aisino.da.api.handler.factory.ApiHandlerFactory;
import com.aisino.da.api.service.DaGetBillDetialService;
import com.aisino.da.api.service.VerifyJsonService;
import com.aisino.da.api.service.api.ExtDataHandService;
import com.aisino.da.api.service.fkbill.DataConveService;
import com.aisino.da.api.service.publicother.SaveAndCheckOtherDataDetialService;
import com.aisino.da.api.util.ArrayUtil;
import com.aisino.da.api.util.CollectionStringUtil;
import com.aisino.da.api.util.ENComParametersUtil;
import com.aisino.da.api.util.MsUtils;
import com.aisino.da.api.util.callback.ExtCallBcakUtil;
import com.aisino.da.api.util.ext.ESUtil;
import com.aisino.da.api.util.mq.DAMQSendUtil;
import com.aisino.da.core.util.DaConfigHelper;
import com.aisino.da.fc.bean.CollectParamsBean;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.sql.Savepoint;
import java.util.*;

@Service
public class BillHandler implements SaveAndCheckOtherDataDetialService
{
    @Inject
    private CollectLogDAOInterface collectLogDAOInterface;

    @Inject
    private DaGetBillDetialService daGetBillDetialService;

    @Inject
    private ExtDataHandService extDataHandService;

    @Inject
    private VerifyJsonService verifyJsonService;

    @Inject
    private DaGetBillDetialDao daGetBillDetialDao;

    //判断收集单据时是否填充凭证信息
    private static final boolean ISUPDATEBILLANDVOU = ConfigHelper.getBoolean("isupdate_voucherid", false);
    private static final boolean MQCALLBACK = ConfigHelper.getBoolean("mqcallback", false);

    /**
     * 凭证信息是否分割
     */
    private static final boolean  SPLITVOUCHER = DaConfigHelper.getBoolean("splitvoucher", false);

    /**
     * 凭证id是否拼接财年
     */
    private static final boolean  CVIDISSPLIT = DaConfigHelper.getBoolean("cvidissplit", false);

    /**
     * 分割凭证信息的分隔符
     */
    private static final String  SPLITTER = DaConfigHelper.getString("splitter", ",");

    private static final int BATCHNUM = DaConfigHelper.getInt("batchnum", 10);
    /**
     * 凭证id是由财年跟什么字段拼接而成,凭证编号/或者凭证id 默认凭证编号
     */
    private static final String  CVIDISSPLITSTR = DaConfigHelper.getString("cvidissplitstr", "凭证编号");

    /**
     * 不需要四性检测的系统
     */
    private static final String  noSuaiCheckSystem = DaConfigHelper.getString("noSuaiCheckSystem","");
    private static final Logger log = LoggerFactory.getLogger(BillHandler.class);

    @Override
    public Map collectOtherDetial(Params params)
    {

        CollectParamsBean collectParamsBean = new CollectParamsBean();
        String startTime = BankReceiptConveService.getSysTime();
        collectParamsBean.setStartTime(startTime);

        //判断返回报文是否存在问题
        String ccallcreatedate = CollectionStringUtil.getSysDateTime();
        ENComParametersUtil enComParametersUtil = new ENComParametersUtil();
        String code = enComParametersUtil.getCommonStringValue(params, "编码", "code");
        String serialnumber = enComParametersUtil.getCommonStringValue(params, "同步序列号", "serialnumber");
        String extcode = enComParametersUtil.getCommonStringValue(params, "来源系统编码", "extcode");
        String msg = enComParametersUtil.getCommonStringValue(params, "系统错误信息", "syserrormsg");
        String dsid = enComParametersUtil.getCommonStringValue(params, "数据源", "tenantdsid");
        Map msgmap = new HashMap<>();
        //code 为000000 默认为正常返回数据
        if (StringUtils.isBlank(serialnumber) || StringUtils.isBlank(extcode))
        {
            msg = "未获取到第三方系统编码" + extcode + "未获取到" + serialnumber + "";
            msgmap.put("code","1000");
            msgmap.put("msg",msg);
            return msgmap;
        }

        Map sourceBatch = collectLogDAOInterface.getSourceBatch(serialnumber, extcode, dsid);
        if(CollectionUtil.isBlankMap(sourceBatch))
        {
            msg = "未获取到" + serialnumber + "批次收集发起记录收集";
            msgmap.put("code","1000");
            msgmap.put("msg",msg);
            return msgmap;
        }

        int totalNumTemp = enComParametersUtil.getCommonIntValue(params, "总批次","batchtotalnum");
        if (totalNumTemp <= 0)
        {
            //当未告知批次时默认只需要入库一批
            totalNumTemp = 1;
        }
        int ibatchtotalnum = CollectionUtil.getIntFromMap(sourceBatch, "ibatchtotalnum");
        if (ibatchtotalnum < 1)
        {
            sourceBatch.put("ibatchtotalnum",totalNumTemp);
        }

        String csynstatus = CollectionUtil.getStringFromMap(sourceBatch, "csynstatus");
        String sourceBatchId = CollectionUtil.getStringFromMap(sourceBatch, "cguid");
        ApsContextDb apsContext = new ApsContextDb();
        DbService db = apsContext.getDb(dsid);

        //若总批次大于一需要判断数据已入库数量,超量数据不予入库;
        int SourceBatchDetailcount = collectLogDAOInterface.querySourceBatchDetail(sourceBatchId, dsid);
        String billdetialjson = JsonUtil.toJSON(params);
        Map<String, String> updateMap = new HashMap<>();
        updateMap.put("ccalljsondetail",billdetialjson);

        if (totalNumTemp >= 1)
        {
            //查询已入库数据数
            if (SourceBatchDetailcount >= totalNumTemp)
            {
                msg = "已收集" + totalNumTemp + "批数据,请勿重复请求";
                msgmap.put("code","1000");
                msgmap.put("msg",msg);
                Map insertSourceBatchDetail = BankReceiptConveService.makeErrorInsertSourceBatchDetail(msg, params, sourceBatch);
                insertSourceBatchDetail.put("icollectnum",0);
                insertSourceBatchDetail.put("icollectsuccessnum",0);
                //收集批次已完成无需重新计算数据总数
                collectLogDAOInterface.insertSourceBatchDetail(insertSourceBatchDetail, collectParamsBean);
                daGetBillDetialService.updateExtSourceBatchStatus(CollectConstant.COLLECTION_COMPLETED,CollectConstant.COLLECTION_COMPLETED_CODE,sourceBatchId, dsid);
                db.commit();
                return msgmap;
            }
        }
        boolean ischecksave = params.getBoolean("ischecksave", true);
        Map stomedMapByExtCode = daGetBillDetialService.getStomedMapByExtCode(extcode,dsid);
        if (ischecksave && CollectionUtil.isBlankMap(stomedMapByExtCode))
        {
            msgmap.put("code","1000");
            msgmap.put("msg","未获取到系统配置");
            return msgmap;
        }
        List<Map> failList = new ArrayList<>();
        List<Map> succesList = new ArrayList<>();

        int cout = 0;
        int failcount = 0;
        int successcount = 0;

        //判断是否超时
        String ccalljondetail = JsonUtil.toJSON(params.getFieldMap());
       /* Map parambillDetial = new HashMap();
        parambillDetial.putAll(params.getFieldMap());*/
        if (CollectConstant.CANCELLATION_CODE.equalsIgnoreCase(csynstatus))
        {
            msg = "该批次" + CollectConstant.CANCELLATION + "收集";
            msgmap.put("code","1000");
            msgmap.put("msg",msg);
            return msgmap;

        }else
        {
            //开始处理数据更新为收集中,
            daGetBillDetialService.updateExtSourceBatchStatus(CollectConstant.COLLECTING,CollectConstant.COLLECTING_CODE,sourceBatchId, dsid);

            //判断数据编码是否正确
            if (StringUtils.isNotBlank(code) && "000000".equalsIgnoreCase(code))
            {
                Map relatedRelationships = MsUtils.getRelatedRelationships("单据资料", dsid);
                List<Map> billRelated = (List<Map>)relatedRelationships.get("单据资料");
                List<Map> vouRelated = (List<Map>)relatedRelationships.get("记账凭证");

                //获取数据map
                Object billdetial = enComParametersUtil.getCommonObjectValue(params, "单据详情", "billdetail");

                if (billdetial instanceof Map)
                {
                    String param = CollectionUtil.getStringFromMap(sourceBatch, "param");
                    boolean iscover = false;
                    if (StringUtils.isNotBlank(param))
                    {
                        Map paramMap = JsonUtil.fromJSON(param, Map.class);
                        iscover = CollectionUtil.getBoolean(paramMap, "iscover", false);
                    }else
                    {
                        iscover = CollectionUtil.getBoolean(sourceBatch, "icover", false);
                    }
                    String sourcebatchid = CollectionUtil.getStringFromMap(sourceBatch, "cguid");
                    String csourceorgnid = CollectionUtil.getStringFromMap(sourceBatch, "csourceorgnid");
                    String csourceorgncode = CollectionUtil.getStringFromMap(sourceBatch, "csourceorgncode");
                    Map billdetialMap = (Map) billdetial;
                    //获取报文编码。如果为 0 跟空为中文1 为英文
                    Map extSysConfig = daGetBillDetialService.getExtSysConfig(extcode, dsid);
                    boolean isEnglish = false;
                    String cbwLanguage = CollectionUtil.getStringFromMap(extSysConfig, "cbw_language");
                    if("1".equals(cbwLanguage)){
                        isEnglish = true;
                    }

                    String apihandlertype = CollectConstant.BILL;
                    if (isEnglish)
                    {
                        apihandlertype = apihandlertype + CollectConstant.EN;
                    }
                    AbstractApiHandler apiTemplate = ApiHandlerFactory.getApiTemplate(apihandlertype);

                    //数据循环入库billtype 单据类型
                    Map<String, Map> billDefineMap = new HashMap<>();
                    StringBuffer detialmsg = new StringBuffer();

                    //增加计数器
                    int mqcallcount = 0; //mq回调总次数
                    int batchnum = BATCHNUM; //mq分组表示 500单据为一组
                    int billcount = 0; //单据总数
                    int currmqcallcount = 0; //当前mq批次
                    int currbillcount = 0; //当前批次处理单据数量
                    List<Map> filelist = new ArrayList<>();

                    String cgroupnum = Guid.g();
                    for (Object billtype : billdetialMap.keySet())
                    {
                        List<Map> detaillist = getBillDetialList(billtype, billdetialMap);
                        billcount = billcount + detaillist.size();
                    }
                    log.info("【"+serialnumber+"】标准接口接收到数据:"+billcount+"条");

                    //计算需要发送多少批数据请求。。。。。。。
                    if (billcount > 0)
                    {
                        mqcallcount = billcount/batchnum +(billcount % batchnum != 0 ? 1: 0);
                    }
                    //批次中放入cgroupnum 四性批次信息
                    sourceBatch.put("cgroupnum",cgroupnum);

                    for (Object billtype : billdetialMap.keySet())
                    {

                        List<Map> detaillist = getBillDetialList(billtype, billdetialMap);
                        cout = cout + detaillist.size();

                        //若获取的文件list不为空则进行数据入库处理
                        if (CollectionUtil.isNotEmpty(detaillist))
                        {
                            //处理数据之前获取对照配置
                            List<Map> datailBsinessList = verifyJsonService.queryDetailsBusinessList(extcode, db);

                            Map billDefine = new HashMap();
                            if (!CollectionUtil.isBlankMap(billDefineMap) && billDefineMap.containsKey(billtype))
                            {
                                billDefine = billDefineMap.get(billtype);
                            }else
                            {
                                billDefine = verifyJsonService.getBillDefine(extcode, billtype.toString(), csourceorgnid, csourceorgncode);
                                billDefine.put(billtype.toString(), billDefine);
                            }

                            //开始数据处理,
                            for (Map billDetialMap : detaillist)
                            {
                                //进来就加次数，当currmqcallcount = batchnum 发送一次下载请求 数据切割再下一层算数
                                // currbillcount ++;

                                String cvoucherid   = enComParametersUtil.getCommonStringValue(billDetialMap, "凭证id","cvoucherid");
                                String billid       = enComParametersUtil.getCommonStringValue(billDetialMap,"单据id", "billid");
                               // String corgnid_name = enComParametersUtil.getCommonStringValue(billDetialMap, "组织名称","orgname");
                                String sybillid     = enComParametersUtil.getCommonStringValue(billDetialMap, "主单据id","sybillid");
                                String cvoucherdate = enComParametersUtil.getCommonStringValue(billDetialMap, "凭证日期","cvoucherdate");
                                String cbillcode       = enComParametersUtil.getCommonStringValue(billDetialMap,"单据编号", "cbillcode");

                                String corgnid = CollectionUtil.getStringFromMap(sourceBatch, "corgnid");

                                //要拼接凭证id的字段
                                String cvidissplitstring = CollectionUtil.getStringFromMap(billDetialMap, CVIDISSPLITSTR);

                                //新的凭证信息合集
                                List<Map> voucherlist = new ArrayList<>();
                                HadlerResultViewVO cvHadlerResultViewVO = new HadlerResultViewVO();
                                cvHadlerResultViewVO.setCstatus(true);

                                //查询是否有跟据关联关系进来的数据。。。
                                List<Map> voucherZlDetial = daGetBillDetialDao.getVoucherZlDetial(CollectConstant.BILL_NAME, cbillcode, billid, corgnid, dsid, isEnglish);

                                try
                                {
                                   //若需要处理关联关系，且当前单据没有凭证相关信息，从关联关系表获取关联关系
                                    if (ISUPDATEBILLANDVOU && StringUtils.isBlank(cvoucherid))
                                    {
                                        if (StringUtils.isBlank(sybillid) || (!StringUtils.isBlank(billid) &&  billid == sybillid))
                                        {
                                            if (isEnglish)
                                            {
                                                List<Map> vocherDetial = daGetBillDetialService.getVouCherByExtEnCode(billid,corgnid, extcode, dsid);
                                                if (CollectionUtil.isNotEmpty(vocherDetial))
                                                {
                                                    // billDetialMap.putAll(vocherDetial);
                                                    voucherlist.addAll(vocherDetial);
                                                }
                                            }
                                            else
                                            {
                                                List<Map> vocherDetial = daGetBillDetialService.getVouCherByExtCode(billid,corgnid, extcode, dsid);
                                                if (CollectionUtil.isNotEmpty(vocherDetial))
                                                {
                                                    // billDetialMap.putAll(vocherDetial);
                                                    voucherlist.addAll(vocherDetial);
                                                }
                                            }
                                        }else
                                        {
                                            if (isEnglish)
                                            {
                                                List<Map> vocherDetial = daGetBillDetialService.getVouCherByEnParentExtCode(sybillid,corgnid, extcode, dsid);
                                                if (CollectionUtil.isNotEmpty(vocherDetial))
                                                {
                                                    // billDetialMap.putAll(vocherDetial);
                                                    voucherlist.addAll(vocherDetial);
                                                }
                                            }
                                            else
                                            {
                                                List<Map> vocherDetial = daGetBillDetialService.getVouCherByParentExtCode(sybillid,corgnid, extcode, dsid);
                                                if (CollectionUtil.isNotEmpty(vocherDetial))
                                                {
                                                    // billDetialMap.putAll(vocherDetial);
                                                    voucherlist.addAll(vocherDetial);
                                                }
                                            }
                                        }
                                    }else if (SPLITVOUCHER && StringUtils.isNotBlank(SPLITTER))
                                    {
                                        String cvocode = enComParametersUtil.getCommonStringValue(billDetialMap, "凭证编号","cvoucherno");

                                        //凭证id要拼接的字段

                                        String[] cvoucherids = cvoucherid.split(SPLITTER);
                                        String[] cvocodes = cvocode.split(SPLITTER);
                                        String[] cvidissplitstrings = cvidissplitstring.split(SPLITTER);
                                        for (int i = 0; i < cvoucherids.length; i++)
                                        {
                                            Map newvomap = new HashMap<>();
                                            if (CVIDISSPLIT)
                                            {
                                                if (StringUtils.isNotBlank(cvoucherdate) && cvoucherdate.length() > 4 && StringUtils.isNotBlank(cvidissplitstrings[i]))
                                                {
                                                    String cvoucheryear = cvoucherdate.substring(0,4);
                                                    if (isEnglish)
                                                    {
                                                        newvomap.put("cvoucherid",cvoucheryear + cvidissplitstrings[i]);
                                                    }else
                                                    {
                                                        newvomap.put("凭证id",cvoucheryear + cvidissplitstrings[i]);
                                                    }
                                                }
                                            }else
                                            {
                                                if (isEnglish)
                                                {
                                                    newvomap.put("cvoucherid",cvoucherids[i]);
                                                }else
                                                {
                                                    newvomap.put("凭证id",cvoucherids[i]);
                                                }
                                            }

                                            if (isEnglish)
                                            {
                                                newvomap.put("cvoucherno",cvocodes[i]);

                                            }else
                                            {
                                                newvomap.put("凭证编号",cvocodes[i]);
                                            }
                                            voucherlist.add(newvomap);
                                        }
                                    }

                                    //若重新组装的凭证集合为空则把原始的凭证信息放入
                                    if (CollectionUtil.isEmpty(voucherlist))
                                    {
                                        Map newvomap = new HashMap<>();
                                        //重新拼接凭证一汽单据凭证id.......select cvoucode 凭证编号,cvoucherid 凭证id,cvoucherdate 凭证日期
                                        String cvoucode = enComParametersUtil.getCommonStringValue(billDetialMap, "凭证编号","cvoucherno");

                                        if (CVIDISSPLIT)
                                        {
                                            if (StringUtils.isNotBlank(cvoucherdate) && cvoucherdate.length() > 4 && StringUtils.isNotBlank(cvidissplitstring))
                                            {
                                                String cvoucheryear = cvoucherdate.substring(0,4);
                                                if (isEnglish)
                                                {
                                                    newvomap.put("cvoucherid",cvoucheryear + cvidissplitstring);
                                                }
                                                else
                                                {
                                                    newvomap.put("凭证id",cvoucheryear + cvidissplitstring);
                                                }
                                            }
                                        }else
                                        {
                                            if (isEnglish)
                                            {
                                                newvomap.put("cvoucherid",cvoucherid);
                                            }
                                            else
                                            {
                                                newvomap.put("凭证id",cvoucherid);
                                            }
                                        }
                                        if (isEnglish)
                                        {
                                            newvomap.put("cvoucherno",cvoucode);

                                        }else
                                        {
                                            newvomap.put("凭证编号",cvoucode);
                                        }
                                        String vouid   = enComParametersUtil.getCommonStringValue(newvomap, "凭证id", "cvoucherid");
                                        String voucode = enComParametersUtil.getCommonStringValue(newvomap, "凭证编号","cvoucherno");
                                        if (StringUtils.isNotBlank(vouid) && StringUtils.isNotBlank(voucode))
                                        {
                                            voucherlist.add(newvomap);
                                        }
                                    }

                                    //如果上述仍然未生成凭证相关数据且当前资料没有凭证信息则根据关联关系查询凭证。
                                    if(StringUtils.isBlank(cvoucherid) && CollectionUtil.isEmpty(voucherlist) && voucherlist.size() == 0)
                                    {
                                        //提取单据公共字段。。
                                        Map billDetial = getBillDetial(billDetialMap);

                                        if (isEnglish)
                                        {
                                            //查询关联凭证
                                            List<Map> relatedVo = daGetBillDetialService.getEnRelatedVou(vouRelated, corgnid, dsid, billDetial);
                                            if (CollectionUtil.isNotEmpty(relatedVo) && relatedVo.size() > 0)
                                            {
                                                voucherlist.addAll(relatedVo);
                                            }
                                        }else
                                        {
                                            //查询关联凭证
                                            List<Map> relatedVo = daGetBillDetialService.getRelatedVou(vouRelated, corgnid, dsid, billDetial);
                                            if (CollectionUtil.isNotEmpty(relatedVo) && relatedVo.size() > 0)
                                            {
                                                voucherlist.addAll(relatedVo);
                                            }
                                        }
                                    }
                                }catch (Exception e)
                                {
                                    failcount ++ ;
                                    cvHadlerResultViewVO.setCstatus(false);
                                    cvHadlerResultViewVO.setMsg( "转换单据凭证信息异常" + e.getMessage());
                                }

                                if (CollectionUtil.isNotEmpty(voucherlist) && CollectionUtil.isNotEmpty(voucherZlDetial))
                                {
                                    List<Map> nnRelatedLsist = new ArrayList<>();
                                    ArrayUtil.dRemoval(voucherlist, voucherZlDetial, nnRelatedLsist);
                                    voucherlist = nnRelatedLsist;
                                }else if (CollectionUtil.isNotEmpty(voucherZlDetial))
                                {
                                    voucherlist.addAll(voucherZlDetial);
                                }

                                //判断查询到关联关系是否有原有凭证关联关系，如果有不再放入。
                                boolean isDuplicate = false;
                                if (CollectionUtil.isNotEmpty(voucherlist))
                                {
                                    if (isEnglish)
                                    {
                                        isDuplicate = voucherlist.stream()
                                                .anyMatch(e -> e.get("cvoucherid").equals(billDetialMap.get("cvoucherid")));
                                    }else
                                    {
                                        isDuplicate = voucherlist.stream()
                                                .anyMatch(e -> e.get("凭证id").equals(billDetialMap.get("凭证id")));
                                    }
                                }

                                if (!isDuplicate && (StringUtils.isNotBlank(cvoucherid) || (StringUtils.isBlank(cvoucherid) && voucherlist.size() == 0)))
                                {
                                    //中放入原有凭证信息。。
                                    Map vomap = new HashMap<>();
                                    if (isEnglish)
                                    {
                                        vomap.put("cvoucherid", billDetialMap.get("cvoucherid"));
                                        vomap.put("cvoucherno", billDetialMap.get("cvoucherno"));
                                        vomap.put("cvoucherdate", billDetialMap.get("cvoucherdate"));
                                    }
                                    else
                                    {
                                        vomap.put("凭证id", billDetialMap.get("凭证id"));
                                        vomap.put("凭证编号", billDetialMap.get("凭证编号"));
                                        vomap.put("凭证日期", billDetialMap.get("凭证日期"));
                                    }
                                    voucherlist.add(vomap);
                                }

                                int size = voucherlist.size();
                                if (size > 1)
                                {
                                    cout = cout + voucherlist.size() - 1;
                                    //重新计算需要发送请求相关数值
                                    billcount = billcount + voucherlist.size() - 1;
                                    mqcallcount = billcount/batchnum +(billcount % batchnum != 0 ? 1: 0);
                                }

                               /* if (CollectionUtil.isEmpty(voucherlist))
                                {
                                    //中放入原有凭证信息。。
                                    Map vomap = new HashMap<>();
                                    if (isEnglish)
                                    {
                                        vomap.put("cvoucherid", billDetialMap.get("cvoucherid"));
                                        vomap.put("cvoucherno", billDetialMap.get("cvoucherno"));
                                        vomap.put("cvoucherdate", billDetialMap.get("cvoucherdate"));
                                    }
                                    else
                                    {
                                        vomap.put("凭证id", billDetialMap.get("凭证id"));
                                        vomap.put("凭证编号", billDetialMap.get("凭证编号"));
                                        vomap.put("凭证日期", billDetialMap.get("凭证日期"));
                                    }
                                    voucherlist.add(vomap);
                                }*/

                                if (cvHadlerResultViewVO.getCstatus())
                                {
                                    for (Map vomap : voucherlist)
                                    {
                                        currbillcount ++;

                                        billDetialMap.putAll(vomap);
                                        HadlerResultViewVO hadlerResultViewVO = new HadlerResultViewVO();
                                        Savepoint aThis = null;
                                        try
                                        {
                                            aThis = db.savePoint("BZBILL" + System.currentTimeMillis());
                                            hadlerResultViewVO = DataHandle(billDetialMap, sourceBatch, billDefine, dsid, iscover, apiTemplate,billtype.toString(), extSysConfig, isEnglish , datailBsinessList);

                                            if (!hadlerResultViewVO.getCstatus())
                                            {
                                                if (aThis != null)
                                                {
                                                    db.rollback(aThis);
                                                }
                                            }

                                        }catch (Exception e)
                                        {
                                            hadlerResultViewVO.setCstatus(false);
                                            hadlerResultViewVO.setMsg( "数据入库异常：" + e.getMessage());
                                            if (aThis != null)
                                            {
                                                try {
                                                    db.rollback(aThis);
                                                } catch (SQLException ex)
                                                {

                                                }
                                            }
                                        }
                                        //单条数据处理方法。。。。
                                        //  HadlerResultViewVO hadlerResultViewVO = DataHandle(billDetialMap, sourceBatch, billDefine, dsid, iscover, apiTemplate,billtype.toString());
                                        //true为成功false失败
                                        if (!hadlerResultViewVO.getCstatus())
                                        {
                                            failcount ++ ;
                                            //记录失败日志。。。。获取构建日志基本信息
                                            Map headmap = extDataHandService.getPublicVoucherMap(billDetialMap, sourceBatch, enComParametersUtil, isEnglish);
                                            Map logmap = new HashMap<>();

                                            //构建log主资料log相关 sourceBatch 收集记录表
                                            detialmsg.append("\n\r单据编号【").append(CollectionUtil.getStringFromMap(headmap, "cbillcode")).append(hadlerResultViewVO.getMsg()).append(";");
                                            logmap.put("csyncexceptionmsg",hadlerResultViewVO.getMsg());

                                            logmap.put("datasource", CollectionUtil.getStringFromMap(sourceBatch,"csourcesysname"));
                                            logmap.put("cdabilltypeid", CollectionUtil.getStringFromMap(headmap, "sybillid"));
                                            logmap.put("sybillid", CollectionUtil.getStringFromMap(headmap, "sybillid"));
                                            logmap.put("billid", CollectionUtil.getStringFromMap(headmap, "billid"));
                                            logmap.put("cbillcode", CollectionUtil.getStringFromMap(headmap, "cbillcode"));
                                            logmap.put("cvoucherno", CollectionUtil.getStringFromMap(headmap, "cvoucherno"));
                                            logmap.put("ddate", CollectionUtil.getStringFromMap(headmap, "ddate"));
                                            logmap.put("cvoucherid", CollectionUtil.getStringFromMap(headmap, "cvoucherid"));
                                            logmap.put("csourcebatchid", sourceBatch.get("cguid"));
                                            Map failmap = new HashMap<>();
                                            failmap.put("单据编号", CollectionUtil.getStringFromMap(headmap, "cbillcode"));
                                            failmap.put("单据id", CollectionUtil.getStringFromMap(headmap, "billid"));
                                            failmap.put("失败原因",detialmsg);

                                            failList.add(failmap);
                                            Map map = DataConveService.makeExtDefalultValue(logmap, sourceBatch);
                                            List loglist = new ArrayList<>();
                                            map.put("cguid", Guid.g());
                                            loglist.add(map);
                                            DbService dbService =  new ApsContextDb().getDb();
                                            extDataHandService.inserDetialByTableName("da_fc_bill_collect_log", loglist,dbService);

                                        }else
                                        {
                                            Map headmap = extDataHandService.getPublicVoucherMap(billDetialMap, sourceBatch, enComParametersUtil, isEnglish);

                                            Map succesmap = new HashMap<>();
                                            succesmap.put("单据编号", CollectionUtil.getStringFromMap(headmap, "cbillcode"));
                                            succesmap.put("单据id", CollectionUtil.getStringFromMap(headmap, "billid"));
                                            succesList.add(succesmap);
                                            successcount ++;
                                            List<EsAddViewVO> addViewVo = hadlerResultViewVO.getAddViewVo();

                                            if (CollectionUtil.isNotEmpty(addViewVo))
                                            {
                                                for (EsAddViewVO esAddViewVO : addViewVo) {
                                                    filelist.addAll(esAddViewVO.getAddfilelist());
                                                }
                                            }
                                        }

                                        log.info("是否走四性检测系统 daconfig【"+noSuaiCheckSystem+"】");
                                        //通过配置文件判断是否需要发送消息下载文件
                                        String[] noSuaiCheckSystems = noSuaiCheckSystem.split(",");
                                        Set noSuaiCheckSystemSet = new HashSet();
                                        for(int i=0;i<noSuaiCheckSystems.length;i++){
                                            noSuaiCheckSystemSet.add(noSuaiCheckSystems[i]);
                                        }

                                        if(!noSuaiCheckSystemSet.contains(extcode)){
                                            log.info("走四性检测系统 来源系统编码【"+extcode+"】");
                                            //当currmqcallcount = batchnum 发送一次下载请求
                                            //修改mq下载文件逻辑默认处理500个单据下载一次（失败成功均算次数）。。。
                                            //如果等于1 当最后一个单据处理完发送消息或者，，组装够batchnum发送消息 或者 最后一批次则是剩余所有文件处理完再发送消息

                                            log.error("判断单据是否可以发送mq请求下载数据mqcallcount【" + mqcallcount + "】currbillcount【" + currbillcount + "】billcount【" + billcount +
                                                    "】batchnum【" + batchnum + "】MQCALLBACK【" + MQCALLBACK + "】");
                                            if ((mqcallcount == 1 && currbillcount == billcount) ||
                                                    (currbillcount == batchnum) ||
                                                    (currbillcount == (billcount - (batchnum * currmqcallcount))))
                                            {
                                                currmqcallcount ++;
                                                //调用mq

                                                //发送消息调用mq
                                                String cbackurl = CollectionUtil.getStringFromMap(sourceBatch, "cbackurl");
                                                Map batchMap = new HashMap<>();
                                                batchMap.put("iscallback",MQCALLBACK);
                                                batchMap.put("cgrouptotal",String.valueOf(mqcallcount));
                                                batchMap.put("ccurrentgroupnumber",String.valueOf(currmqcallcount));
                                                batchMap.put("cgroupnum", cgroupnum);
                                                log.error("单据开始调用mq======================>" + failList.size());
                                                billsendmq(filelist,serialnumber,stomedMapByExtCode, cbackurl, failList, succesList,batchMap, db.getDsID());

                                                //发送消息后重置为0重新计数
                                                currbillcount= 0;
                                                filelist = new ArrayList<>();
                                                //发送消息后重置批次，并覆盖总批次中的cgroupnum值
                                                cgroupnum = Guid.g();
                                                sourceBatch.put("cgroupnum",cgroupnum);

                                            }
                                        }else{
                                            log.info("不走四性检测系统 来源系统编码【"+extcode+"】");
                                        }

                                        db.commit();
                                        if (hadlerResultViewVO.getCstatus())
                                        {
                                            List<EsDeleteViewVO> deletefileidlist = hadlerResultViewVO.getDeletefileidlist();
                                            if (CollectionUtil.isNotEmpty(deletefileidlist) && deletefileidlist.size() > 0)
                                            {
                                                for (EsDeleteViewVO deleteViewVO : deletefileidlist) {
                                                    ESUtil.deleteSeDetial(deleteViewVO.getFileidlist(), deleteViewVO.getType());
                                                }
                                            }

                                        }
                                    }

                                }
                            }
                        }

                    }
                    msg = "单据资料收集共收集" + cout + "条,\n\r成功" + successcount + "条,\n\r失败" + failcount + "条" + detialmsg;
                }

            }
            else
            {
                msg = "组装数据时发生异常" + msg;
                msgmap.put("code","1000");
                msgmap.put("msg", msg);
            }
        }
        //因要四性检测后回调。。。。
        //同步返回时判断需不需要回调、、、、、、
        //判断是否需要mq回调
        String cbackurl = CollectionUtil.getStringFromMap(sourceBatch, "cbackurl");

        String callbackmsg = "";
        //若mq中进行了回调则不需要再在最外层回调
        if (StringUtils.isNotBlank(cbackurl) && !MQCALLBACK)
        {
            Map callBackMap = new HashMap<>();
            callBackMap.put("本次采集是否成功","是");
            callBackMap.put("收集结果",msg);
            callBackMap.put("同步序列号", sourceBatch.get("cserialnumber"));
            callBackMap.put("资料类型","单据资料");
            callbackmsg = ExtCallBcakUtil.callBackExt(cbackurl, failList, succesList,callBackMap);
        }

        //返回之前更新回调信息
         
        String sysDateTime = CollectionStringUtil.getSysDateTime();
        updateMap.put("cendtime", sysDateTime);
        Map insertSourceBatchDetail = BankReceiptConveService.makeBillErrorInsertSourceBatchDetail(msg, ccalljondetail, sourceBatch);        insertSourceBatchDetail.put("cbackrtnmsg",callbackmsg);
        insertSourceBatchDetail.put("cbackrtnmsg",callbackmsg);

        //明细入库时,计算数据总数，跟成功的数据数。。。。。。。。
        Map<String, Integer> numbermap = daGetBillDetialService.getNumber(sourceBatchId,dsid);
        //查询总数..
        Integer icollectnum = CollectionUtil.getIntFromMap(numbermap,"icollectnum");//记录的数据总数
        Integer icollectsuccessnum = CollectionUtil.getIntFromMap(numbermap,"icollectsuccessnum");//记录的成功数据总数

        //明细存储总数
        insertSourceBatchDetail.put("icollectnum",cout);
        insertSourceBatchDetail.put("icollectsuccessnum",successcount);

        insertSourceBatchDetail.put("syserrormsg", params.getString(enComParametersUtil.getSyserrormsg()));
        insertSourceBatchDetail.put("businesserrormsg", params.getString(enComParametersUtil.getBusinesserrormsg()));
        collectLogDAOInterface.insertSourceBatchDetail(insertSourceBatchDetail, collectParamsBean);
        //数据入库之前判断数据库批次
        SourceBatchDetailcount = collectLogDAOInterface.querySourceBatchDetail(sourceBatchId, dsid);
        //放入总的提示信息
        int ctotal = cout +icollectnum;
        int succestotal = successcount + icollectsuccessnum;
        String countmsg = "单据资料收集共收集" + ctotal + "条,\n\r成功" + succestotal + "条,\n\r失败" + (ctotal - succestotal)  + "条";

        if (!CollectConstant.CANCELLATION_CODE.equalsIgnoreCase(csynstatus) && (totalNumTemp == 1 || SourceBatchDetailcount >= totalNumTemp))
        {
            //单批次数据处理完成认为数据入库处理完成不允许
            updateMap.put("csynstatus", CollectConstant.COLLECTION_COMPLETED_CODE);
            updateMap.put("csynstatus_name", CollectConstant.COLLECTION_COMPLETED);

            //收集完成时回调第三方接口

        }else
        {
            updateMap.put("csynstatus", CollectConstant.COLLECTING_CODE);
            updateMap.put("csynstatus_name",CollectConstant.COLLECTING);
            //多批次情况
        }

        updateMap.put("msg",countmsg);
        updateMap.put("ccallcreatedate",ccallcreatedate);
        updateMap.put("ibatchtotalnum", String.valueOf(totalNumTemp));
        daGetBillDetialService.updateExtSourceBatchByCZY(updateMap, sourceBatchId,dsid);
        //发送消息提示接收完成后续完善
        db.commit();
        msgmap.put("code","0000");
        msgmap.put("msg", msg);
        return msgmap;
    }

    private Map getBillDetial(Map billDetialMap)
    {
        //提取单据公共字段
        return extDataHandService.getBillCommon(billDetialMap);
    }

    /**
     * @param
     * @param billDetialMap     传递来的数据map
     * @param sourceBatch       收集批次相关信息
     * @param billDefine        报文定义
     * @param dsid              数据源id
     * @param iscover
     * @param apiTemplate
     * @param extSysConfig
     * @param isEnglish
     * @param datailBsinessList
     */
    private HadlerResultViewVO DataHandle(Map billDetialMap, Map sourceBatch, Map billDefine, String dsid, boolean iscover, AbstractApiHandler apiTemplate, String billtype, Map extSysConfig, boolean isEnglish, List<Map> datailBsinessList)
    {
        ENComParametersUtil enComParametersUtil = new ENComParametersUtil();
        String cvoucherid = enComParametersUtil.getCommonStringValue(billDetialMap,"凭证id", "cvoucherid");
        String sybillid = enComParametersUtil.getCommonStringValue(billDetialMap,"主单据id", "sybillid");
        String extcode = CollectionUtil.getStringFromMap(sourceBatch, "extcode");
        String billid = enComParametersUtil.getCommonStringValue(billDetialMap, "单据id", "billid");

        //现在校验规则，覆盖时系统的重复单据未发生后业务则允许覆盖，不覆盖时,系统内存在单据则不允许入库。
        HadlerResultViewVO HadlerResultViewVO =  DataCheck(billDetialMap, cvoucherid, sybillid, billDefine, iscover,extcode,sourceBatch, apiTemplate, billid, dsid, extSysConfig, isEnglish);
        if (!HadlerResultViewVO.getCstatus())
        {
            return HadlerResultViewVO;
        }

        //数据入库
        return BillDataSave(billDetialMap, sourceBatch, billDefine, dsid, iscover,apiTemplate,billtype, extSysConfig,isEnglish,datailBsinessList);
    }

    private HadlerResultViewVO BillDataSave(Map billDetialMap, Map sourceBatch, Map billDefine, String dsid, boolean iscover, AbstractApiHandler apiTemplate, String billtype, Map extSysConfig, boolean isEnglish, List<Map> datailBsinessList)
    {
        return extDataHandService.BillDataSave(billDetialMap, sourceBatch, billDefine, dsid, iscover,apiTemplate,billtype,false, extSysConfig, isEnglish,datailBsinessList);
    }

    private HadlerResultViewVO DataCheck(Map billDetialMap, String cvoucherid, String sybillid, Map billDefine, boolean iscover, String extcode, Map sourceBatch, AbstractApiHandler apiTemplate, String billid, String dsid, Map extSysConfig, boolean isEnglish)
    {
        HadlerResultViewVO hadlerResultViewVO = new HadlerResultViewVO();
/*
        String msg = extDataHandService.checkDataIsExist(iscover, sybillid, cvoucherid);
*/
        //检验当前数据是否可以入库
        String corgnid = CollectionUtil.getStringFromMap(sourceBatch, "corgnid");

        String cintegritymsg = extDataHandService.checkBillCintegrity(iscover, sybillid, cvoucherid, billid, dsid, corgnid);
        if (StringUtils.isNotBlank(cintegritymsg))
        {
            //0100 代表基础校验未通过
            hadlerResultViewVO.setMsg(cintegritymsg);
            hadlerResultViewVO.setCstatus(false);
            return hadlerResultViewVO;
        }

        String msg = extDataHandService.checkDataIsExistByAll(iscover, sybillid, cvoucherid, billid, dsid, corgnid);

        if (StringUtils.isNotBlank(msg))
        {
            //0100 代表基础校验未通过
            hadlerResultViewVO.setMsg(msg);
            hadlerResultViewVO.setCstatus(false);
            return hadlerResultViewVO;
        }
        //校验必填项相关
        String check = extDataHandService.publicRequireddataCheck(billDetialMap, extcode, sourceBatch, billDefine, apiTemplate,isEnglish);
        if (StringUtils.isNotBlank(check))
        {
            //0100 代表基础校验未通过
            hadlerResultViewVO.setMsg(check);
            hadlerResultViewVO.setCstatus(false);
            return hadlerResultViewVO;
        }

        //校验文件部分
        String checkfile = checkFile(billDetialMap);
        if (StringUtils.isNotBlank(checkfile))
        {
            //0100 代表基础校验未通过
            hadlerResultViewVO.setMsg(checkfile);
            hadlerResultViewVO.setCstatus(false);
            return hadlerResultViewVO;
        }

        hadlerResultViewVO.setCstatus(true);
        return hadlerResultViewVO;
    }

    private String checkFile(Map billdetial)
    {
        ENComParametersUtil enComParametersUtil = new ENComParametersUtil();
        StringBuffer stringBuffer = new StringBuffer();
        Object banklist = enComParametersUtil.getCommonObjectValue(billdetial, "银行回执详情", "bankdetail");
        if (banklist != null && banklist instanceof List)
        {
            List<Map> oldbanklist= (ArrayList<Map>)banklist;

            for (Map bankmap : oldbanklist) {
                Object filesobj = enComParametersUtil.getCommonObjectValue(bankmap, "文件详情", "filelist");
                if (filesobj != null && filesobj instanceof List) {
                    if (filesobj != null && filesobj instanceof List) {
                        List<Map> oldfilelist = (ArrayList<Map>) filesobj;
                        for (Map map : oldfilelist) {
                            CheckFileName(map, stringBuffer, enComParametersUtil);
                        }
                    }
                }
            }

            if (stringBuffer.toString().length() > 0)
            {
                return "单据下银行回单文件文件名异常" + stringBuffer;
            }
        }

        //发票文件校验
        Object invoicedetiallist = enComParametersUtil.getCommonObjectValue(billdetial, "票据详情", "ticketlist");
        if (invoicedetiallist != null && invoicedetiallist instanceof List)
        {
            List<Map> oinvoicelist= (ArrayList<Map>)invoicedetiallist;
            for (Map invoicemap : oinvoicelist)
            {
                Object filesobj = enComParametersUtil.getCommonObjectValue(invoicemap, "文件详情", "filelist");
                if (filesobj != null && filesobj instanceof List) {
                    List<Map> oldfilelist = (ArrayList<Map>) filesobj;

                    for (Map filemap : oldfilelist) {
                        CheckFileName(filemap, stringBuffer, enComParametersUtil);
                    }
                }
            }

            if (stringBuffer.toString().length() > 0)
            {
                return "单据下票据文件名异常" + stringBuffer;
            }
        }
        //单据校验
        Object filesobj = enComParametersUtil.getCommonObjectValue(billdetial, "文件详情", "filelist");

        if (filesobj != null && filesobj instanceof List)
        {
            List<Map> oldfilelist= (ArrayList<Map>)filesobj;

            for (Map filemap : oldfilelist)
            {
                CheckFileName(filemap, stringBuffer, enComParametersUtil);
            }

            if (stringBuffer.toString().length() > 0)
            {
                return "单据文件名异常" + stringBuffer;
            }
        }

        return stringBuffer.toString();
    }

    private static void CheckFileName(Map filemap, StringBuffer sb, ENComParametersUtil enComParametersUtil)
    {
        String cfilerealname = enComParametersUtil.getCommonStringValue(filemap, "文件实际名称", "cfilerealname");
        String filename = enComParametersUtil.getCommonStringValue(filemap, "存储文件名", "filename");
        if (!ArrayUtil.isValidFileName(cfilerealname))
        {
            sb.append("文件实际名称").append(cfilerealname).append("缺少文件名后缀或文件格式错误!");
        }
        if (!ArrayUtil.isValidFileName(filename))
        {
            sb.append("存储文件名").append(filename).append("缺少文件名后缀或文件格式错误!");
        }
    }
    private List<Map> getBillDetialList(Object billtype, Map billdetialMap)
    {
        Object billDetialo = billdetialMap.get(billtype);
        if (!isObjectEmpty(billDetialo))
        {
            if (billDetialo instanceof List)
            {
                //取出每组数据合集
                List<Map> detaillist = (List) billDetialo;
                return detaillist;
            }else
            {
                //数据类型不为List则跳过处理
            }
        }
        return new ArrayList<>();
    }

    private static Boolean isObjectEmpty(Object o)
    {
        String s = Objects.toString(o, "");
        return StringUtils.isEmpty(s);
    }

    private static void billsendmq(List<Map> filelist, String cserialnumber, Map stomedMapByExtCode, String cbackurl, List<Map> failList, List<Map> succesList, Map batchMap, String dsid)
    {
        sendMq(filelist, cserialnumber, stomedMapByExtCode, "BILL", cbackurl, failList, succesList, batchMap,dsid);
    }

    private static void sendMq(List<Map> filelist, String cserialnumber, Map stomedMapByExtCode, String type, String cbackurl, List<Map> failList, List<Map> succesList, Map batchMap, String dsid)
    {
        //成功 数据处理完成获取文件list发送消息处理 文件合集
        //stomemap 文件存储方式map
        //获取当前第三方组织对应的上出方式配置
        Map mqmap = batchMap;
        mqmap.put("stomedmap", stomedMapByExtCode);
        mqmap.put("filelist", filelist);
        //
        mqmap.put("cserialnumber", cserialnumber);
        mqmap.put("caccountclass",type);

        //构建回调相关参数
        mqmap.put("cbackurl",cbackurl);
        mqmap.put("failList",failList);
        mqmap.put("succesList",succesList);
        mqmap.put("dsid",dsid);
        //调用mq存储文件
        DAMQSendUtil.fileDownloadSendMsg(mqmap);
    }
}
