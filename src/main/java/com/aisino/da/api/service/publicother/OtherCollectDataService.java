package com.aisino.da.api.service.publicother;

import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.annotation.Service;
import com.aisino.da.api.constant.CollectConstant;
import com.aisino.da.api.service.publicother.impl.*;

import java.util.HashMap;
import java.util.Map;

@Service
public class OtherCollectDataService
{
    private static Map<String, SaveAndCheckOtherDataDetialService> map = new HashMap(12);
    @Inject
    private BankReceiptHandler bankReceiptHandler;//回单
    @Inject
    private BankStatementHandler bankStatementHandler;//流水
    @Inject
    private TicketHandler ticketHandler;//发票

    //******** ddy 增加凭证关联关系
    @Inject
    private PzZlRelationHandler pzzlrelationhandler;//凭证关联关系

    @Inject
    private BillHandler billHandler;//发票

    public SaveAndCheckOtherDataDetialService getAdapt(String type) {
        this.init();
        return map.get(type);
    }

    private void init() {
        if (map.size() == 0) {
            synchronized(OtherCollectDataService.class)
            {

                map.put(CollectConstant.BANKRECEIPT_NAME,  bankReceiptHandler);
                map.put(CollectConstant.BANKSTATEMENT_NAME,bankStatementHandler);
                map.put(CollectConstant.TICKET_NAME,       ticketHandler);
                map.put(CollectConstant.BILL_NAME,         billHandler);
                map.put(CollectConstant.PZZLREL_NAME,         pzzlrelationhandler);
            }
        }
    }
}
