package com.aisino.da.api.service.outapi.http.template;

import com.aisino.aosplus.core.dao.DbHelper;
import com.aisino.aosplus.core.dao.DbService;
import com.aisino.aosplus.core.ioc.BeanHelper;
import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.aosplus.core.util.Guid;
import com.aisino.aosplus.core.util.JsonUtil;
import com.aisino.aosplus.core.util.StringUtil;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import com.aisino.aps.apsdb.dbhelper.ApsContextDbHelper;
import com.aisino.aps.common.ls.LoginUtil;
import com.aisino.aps.common.utils.LogUtil;
import com.aisino.da.api.constant.CollectConstant;
import com.aisino.da.api.dao.CollectLogDAOInterface;
import com.aisino.da.api.entity.HadlerResultViewVO;
import com.aisino.da.api.handler.AccountBookCollectDataServiceInterface;
import com.aisino.da.api.handler.AccountReportCollectDataServiceInterface;
import com.aisino.da.api.handler.OtherAccountReportCollectDataServiceInterface;
import com.aisino.da.api.handler.VoucherCollectDataServiceInterface;
import com.aisino.da.api.service.DaGetBillDetialService;
import com.aisino.da.api.service.publicother.OtherCollectDataService;
import com.aisino.da.api.service.publicother.SaveAndCheckOtherDataDetialService;
import com.aisino.da.api.util.CollectionStringUtil;
import com.aisino.da.api.util.ENComParametersUtil;
import com.aisino.da.api.util.message.CrontabMessageUtil;
import com.aisino.da.fc.util.StaticCodeClass;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
public class DaPushCollectionTemplate
{
    @Inject
    private CollectLogDAOInterface collectLogDAOInterface;
    @Inject
    VoucherCollectDataServiceInterface voucherCollectDataServiceInterface;
    @Inject
    AccountBookCollectDataServiceInterface accountBookCollectDataServiceInterface;
    @Inject
    AccountReportCollectDataServiceInterface accountReportCollectDataServiceInterface;
    @Inject
    OtherAccountReportCollectDataServiceInterface otherAccountReportCollectDataServiceInterface;
    @Inject
    DaGetBillDetialService daGetBillDetialService;
    private static final Logger LOG = LoggerFactory.getLogger(DaPushCollectionTemplate.class);
    public Map collectionData(Params params)
    {
        Map<String, Object> fieldMap = params.getFieldMap();
        Params par = new Params();
        if(fieldMap.containsKey("body")){
            Map body = params.getMap("body");
            par = new Params(body);
        }else {
            par = params;
        }
        ENComParametersUtil enComParametersUtil = new ENComParametersUtil();
        String type = enComParametersUtil.getCloglistflag(par,"资料类型");//par.getString("资料类型");
        if (StringUtil.isNotEmpty(type) && CollectConstant.PZZLREL_NAME.equalsIgnoreCase(type)){
            params.put("组织编码",par.get("orgcode"));
            //会计期间取值
            params.put("会计期间开始年月",par.get("startdate"));
            params.put("会计期间结束年月",par.get("enddate"));
        }
        //数据元id
        String tenantDsId = par.getString("tenantdsid");

       /* //租户id
        String tenantuserid = par.getString("tenantuserid");
        //通过租户id获取dsid
        if(StringUtils.isNotBlank(tenantuserid) && StringUtils.isBlank(tenantDsId))
        {
            LoginContext db = SsoHelper.getDb(tenantuserid);
            tenantDsId = db.getTenantDb().getDsID();
            par.put("tenantdsid", tenantDsId);
        }
*/
        if(LoginUtil.isSaas()){

            if (StringUtils.isBlank(tenantDsId))
            {
                tenantDsId = params.getString("数据源");
            }

            try {
                DbHelper.setCurrentDsID(tenantDsId);
            }catch (Exception e){
                return makeRtnMapByErrorRtn("tenantdsid不存在"+e.getLocalizedMessage(),par);
            }
        }
        String collectType = enComParametersUtil.getCollecttype(par,CollectConstant.COLLECTTYPEKEY);//par.getString(CollectConstant.COLLECTTYPEKEY);//"0代表文件1代表json
        Map msgmap = new HashMap<>();

        try
        {
            //构建批次相关信息。。。。。。。。。。。。。。。。。。。。
            HadlerResultViewVO hadlerResultViewVO = generatePushSource(par);
            if (!hadlerResultViewVO.getCstatus())
            {
                return makeRtnMapByErrorRtn(hadlerResultViewVO.getMsg(),par);
            }
        }catch (Exception e)
        {
            String stringByException = LogUtil.getStringByException(e);
            stringByException = stringByException.replace("'","“");
            collectLogDAOInterface.insertWebInterfaceErrorLog(stringByException,par,e,"账簿报错");
            return makeRtnMapByException("构建批次相关信息发生异常" + e.getMessage(),par,e);
        }
        //公共校验
        if (StringUtil.isNotEmpty(type) && CollectConstant.BILL_NAME.equalsIgnoreCase(type))
        {
            try
            {
                OtherCollectDataService bean = BeanHelper.getBean(OtherCollectDataService.class);
                SaveAndCheckOtherDataDetialService adapt = bean.getAdapt(CollectConstant.BILL_NAME);
                par.put("编码","000000");
                Map map = adapt.collectOtherDetial(par);
                //   collectionDataService.collectInformation(par);


                String msg = CollectionUtil.getStringFromMap(map, "msg");
                if (!CollectionUtil.getBoolean(map, "isSuccess", true)) {
                    return makeRtnMapByErrorRtn(msg,par);
                }else {
                    return makeRtnMapBySuccess(msg,par);
                }
            }catch (Exception e)
            {
                String stringByException = LogUtil.getStringByException(e);
                stringByException = stringByException.replace("'","“");
                collectLogDAOInterface.insertWebInterfaceErrorLog(stringByException,par,e,"单据收集报错");
                LOG.error("收集单据数据时发生异常参数为【" + par + "】错误详情为【" + e.getMessage() + "】");
                return makeRtnMapByException("单据数据处理发生异常" + e.getMessage(),par,e);
            }
        }else if (StringUtil.isNotEmpty(type) && CollectConstant.VOUCHER_NAME.equalsIgnoreCase(type)){
            try
            {
                LOG.info("HTTP,记账凭证开始接收数据----参数为{}",par);
                if("1".equals(collectType)){
                    Map rtn = voucherCollectDataServiceInterface.voucherInterfaceSyncCollectDataFromHTTP(par);
                    String msg = CollectionUtil.getStringFromMap(rtn, "msg");
                    if (!CollectionUtil.getBoolean(rtn, "isSuccess", true)) {
                        return makeRtnMapByErrorRtn(msg,par);
                    }else {
                        return makeRtnMapBySuccess(msg,par);
                    }
                }else if("0".equals(collectType)){
                    collectLogDAOInterface.insertWebInterfaceErrorLog("记录文件包信息",par,null,"凭证文件包");
                    Map rtn = voucherCollectDataServiceInterface.voucherInterfaceSyncCollectDataFromHTTP(par);
                    String msg = CollectionUtil.getStringFromMap(rtn, "msg");
                    if (!CollectionUtil.getBoolean(rtn, "isSuccess", true)) {
                        return makeRtnMapByErrorRtn(msg,par);
                    }else {
                        return makeRtnMapBySuccess(msg,par);
                    }
                }else {
                    return makeRtnMapByErrorParam(enComParametersUtil.getCollecttype(par,CollectConstant.COLLECTTYPEKEY)+"，参数不匹配0代表文件1代表json",par,enComParametersUtil.getCollecttype(par,CollectConstant.COLLECTTYPEKEY));
                }
            }catch (Exception e)
            {
                String stringByException = LogUtil.getStringByException(e);
                stringByException = stringByException.replace("'","“");
                collectLogDAOInterface.insertWebInterfaceErrorLog(stringByException,par,e,"凭证报错");
                return makeRtnMapByException("记账凭证数据处理发生异常" + e.getMessage(),par,e);
            }
        }else if (StringUtil.isNotEmpty(type) && type.equalsIgnoreCase(CollectConstant.ACCOUNTBOOK_NAME)){
            try
            {
                if("0".equals(collectType)){
                    collectLogDAOInterface.insertWebInterfaceErrorLog("记录文件包信息",par,null,"账簿文件包");
                }
                Map map = accountBookCollectDataServiceInterface.accountBookInterfaceSyncCollectDataFromHTTP(par);
                String msg = CollectionUtil.getStringFromMap(map, "msg");
                if (!CollectionUtil.getBoolean(map, "isSuccess", true)) {
                    return makeRtnMapByErrorRtn(msg,par);
                }else {
                    return makeRtnMapBySuccess(msg,par);
                }
            }catch (Exception e)
            {
                String stringByException = LogUtil.getStringByException(e);
                stringByException = stringByException.replace("'","“");
                collectLogDAOInterface.insertWebInterfaceErrorLog(stringByException,par,e,"账簿报错");
                return makeRtnMapByException("会计账簿数据处理发生异常" + e.getMessage(),par,e);
            }
        }else if (StringUtil.isNotEmpty(type) && type.equalsIgnoreCase(CollectConstant.ACCOUNTREPORT_NAME)){
            try
            {
                if("0".equals(collectType)){
                    collectLogDAOInterface.insertWebInterfaceErrorLog("记录文件包信息",par,null,"报告文件包");
                }
                Map map = accountReportCollectDataServiceInterface.accountReportInterfaceSyncCollectDataFromHTTP(par);
                String msg = CollectionUtil.getStringFromMap(map, "msg");
                if (!CollectionUtil.getBoolean(map, "isSuccess", true)) {
                    return makeRtnMapByErrorRtn(msg,par);
                }else {
                    return makeRtnMapBySuccess(msg,par);
                }
            }catch (Exception e)
            {
                String stringByException = LogUtil.getStringByException(e);
                stringByException = stringByException.replace("'","“");
                collectLogDAOInterface.insertWebInterfaceErrorLog(stringByException,par,e,"报告报错");
                return makeRtnMapByException("会计报告数据处理发生异常" + e.getMessage(),par,e);
            }
        }else if (StringUtil.isNotEmpty(type) && type.equalsIgnoreCase(CollectConstant.OTHERACCOUNTREPORT_NAME)){
            try
            {
                /*if("0".equals(collectType)){
                    collectLogDAOInterface.insertWebInterfaceErrorLog("记录文件包信息",par,null,"报告文件包");
                }*/
                Map map = otherAccountReportCollectDataServiceInterface.otherAccountReportInterfaceSyncCollectDataFromHTTP(par);
                String msg = CollectionUtil.getStringFromMap(map, "msg");
                if (!CollectionUtil.getBoolean(map, "isSuccess", true)) {
                    return makeRtnMapByErrorRtn(msg,par);
                }else {
                    return makeRtnMapBySuccess(msg,par);
                }
            }catch (Exception e)
            {
                String stringByException = LogUtil.getStringByException(e);
                stringByException = stringByException.replace("'","“");
                collectLogDAOInterface.insertWebInterfaceErrorLog(stringByException,par,e,"其他会计资料报错");
                return makeRtnMapByException("其他会计资料数据处理发生异常" + e.getMessage(),par,e);
            }
        }
        else if (StringUtil.isNotEmpty(type) && type.equalsIgnoreCase(CollectConstant.BANKSTATEMENT_NAME))
        {
            try
            {
                if("0".equals(collectType))
                {
                    return makeRtnMapByErrorRtn("银行流水单报文解析仅支持报文格式",par);
                }

                OtherCollectDataService bean = BeanHelper.getBean(OtherCollectDataService.class);
                SaveAndCheckOtherDataDetialService adapt = bean.getAdapt(CollectConstant.BANKSTATEMENT_NAME);
                Map map = adapt.collectOtherDetial(par);
                String msg = CollectionUtil.getStringFromMap(map, "msg");
                if (!CollectionUtil.getBoolean(map, "isSuccess", true)) {
                    return makeRtnMapByErrorRtn(msg,par);
                }else {
                    return makeRtnMapBySuccess(msg,par);
                }
            }catch (Exception e)
            {
                String stringByException = LogUtil.getStringByException(e);
                stringByException = stringByException.replace("'","“");
                collectLogDAOInterface.insertWebInterfaceErrorLog(stringByException,par,e,"银行流水报错");
                LOG.error("推送银行流水数据时发生异常参数为【" + par + "】错误详情为【" + e.getMessage() + "】");

                return makeRtnMapByException("推送银行流水数据处理发生异常" + e.getMessage(),par,e);
            }
        }else if (StringUtil.isNotEmpty(type) && type.equalsIgnoreCase(CollectConstant.PZZLREL_NAME))
        {
            try
            {
                if("0".equals(collectType))
                {
                    return makeRtnMapByErrorRtn(CollectConstant.PZZLREL_NAME+"报文解析仅支持报文格式",par);
                }

                OtherCollectDataService bean = BeanHelper.getBean(OtherCollectDataService.class);
                SaveAndCheckOtherDataDetialService adapt = bean.getAdapt(CollectConstant.PZZLREL_NAME);
                Map map = adapt.collectOtherDetial(par);
                String msg = CollectionUtil.getStringFromMap(map, "msg");
                if (!CollectionUtil.getBoolean(map, "isSuccess", true)) {
                    return makeRtnMapByErrorRtn(msg,par);
                }else {
                    return makeRtnMapBySuccess(msg,par);
                }
            }catch (Exception e)
            {
                String stringByException = LogUtil.getStringByException(e);
                stringByException = stringByException.replace("'","“");
                collectLogDAOInterface.insertWebInterfaceErrorLog(stringByException,par,e,"凭证与原始资料关系报错");
                LOG.error("推送凭证与原始资料关系数据时发生异常参数为【" + par + "】错误详情为【" + e.getMessage() + "】");

                return makeRtnMapByException("推送凭证与原始资料关系数据处理发生异常" + e.getMessage(),par,e);
            }
        }
        else
        {
            msgmap.put("code","1000");
            msgmap.put("msg","未知的数据处理类型【" + type + "】");

            LOG.info("数据推送失败---接收数据成功-未知的数据处理类型-请查看日志--参数为{}",params);
            return msgmap;
        }
    }

    /**
     * 生成收集批次记录
     * @param params
     */
    private HadlerResultViewVO generatePushSource(Params params)
    {
        ENComParametersUtil enComParametersUtil = new ENComParametersUtil();
        HadlerResultViewVO hadlerResultViewVO = new HadlerResultViewVO();
        String startdate = CollectionStringUtil.getSysDateTime();
        String cserialnumber = enComParametersUtil.getSerialnumber(params,"同步序列号");

        String extcode = enComParametersUtil.getExtcode(params,"来源系统编码");//params.getString("来源系统编码");
        String dsid = "";
        dsid = params.getString("tenantdsid");
        if (StringUtils.isBlank(dsid))
        {
            dsid = params.getString("数据源");
        }
        //查询是否存在第三方系统
        Map batchMap = new HashMap<>();
        //判断组织是否存在
        String currorgcode = params.getString(enComParametersUtil.getOrgncode());
        if(StringUtils.isBlank(currorgcode))
        {
            currorgcode = params.getString(enComParametersUtil.getOrgncode2());
            if(StringUtils.isBlank(currorgcode))
            {
                currorgcode = params.getString(enComParametersUtil.getOrgnCodeOther());
            }
        }
        Map currOrgMap = daGetBillDetialService.getCurrOrgByOrgCode(currorgcode, dsid);
        if (CollectionUtil.isBlankMap(currOrgMap))
        {
            hadlerResultViewVO.setCstatus(false);
            hadlerResultViewVO.setMsg("根据" + currorgcode + "未获取到组织信息");
            return hadlerResultViewVO;
        }else
        {
            batchMap.put("csourceorgnid",CollectionUtil.getStringFromMap(currOrgMap, "cguid")); // 来源组织id
            batchMap.put("csourceorgncode", currorgcode); // 来源组织编码
        }
        Map extSysConfig = daGetBillDetialService.getExtSysConfig(extcode, dsid);

        if (StringUtils.isNotBlank(cserialnumber))
        {

            //查询当前组织是否有参数设置。。。
            String corginid = CollectionUtil.getStringFromMap(currOrgMap, "cguid");
            Map employeMap = CrontabMessageUtil.getParamSetting(new ApsContextDb().getDb(dsid), corginid);
            String employeGuid = CollectionUtil.getStringFromMap(employeMap, "ccollector");
            String employeName = CollectionUtil.getStringFromMap(employeMap, "ccollector_name");

            if (!CollectionUtil.isBlankMap(employeMap) && StringUtils.isNotEmpty(employeGuid) && StringUtils.isNotEmpty(employeName))
            {
                employeMap.put("ccreatorid", employeGuid);
                employeMap.put("ccreatorid_name", employeName);
                batchMap.putAll(employeMap);
            }else
            {
                hadlerResultViewVO.setCstatus(false);
                hadlerResultViewVO.setMsg("根据" + currorgcode + "未获取到当前组织'定时任务执行人'参数未设置,无法处理数据推送业务");
                return hadlerResultViewVO;
            }
            //判断批次是否存在存在则不再推送
            Map sourceBatch = collectLogDAOInterface.getSourceBatch(cserialnumber, extcode, dsid);
            if (!CollectionUtil.isBlankMap(sourceBatch))
            {
                params.put("同步序列号",sourceBatch.get("cserialnumber"));
                hadlerResultViewVO.setCstatus(true);
                return hadlerResultViewVO;
            }
        }


        if (CollectionUtil.isBlankMap(extSysConfig))
        {
            hadlerResultViewVO.setCstatus(false);
            hadlerResultViewVO.setMsg("根据" + extcode + "未获取到相关系统配置");
            return hadlerResultViewVO;
        }else
        {
            batchMap.put("extcode", extcode); // 来源系统名称
            batchMap.put("csourcesysname", CollectionUtil.getStringFromMap(extSysConfig, "csystemcode_name")); // 来源系统名称
        }
        enComParametersUtil.setEnglish(extSysConfig);

        //
        String type = enComParametersUtil.getCloglistflag(params,"资料类型");//params.getString("资料类型");
        if (StringUtil.isNotEmpty(type) && CollectConstant.BILL_NAME.equalsIgnoreCase(type))
        {
            batchMap.put("cloglistflag", CollectConstant.BILL); // 资料类型 02
            batchMap.put("cloglistflag_name", CollectConstant.BILL_NAME); // 资料类型名称 单据资料
            batchMap.put("cPageId", CollectConstant.BILL_TABLE_NAME); //页面id da_data_collect_bill_log 单据
            batchMap.put("cTemplateId", CollectConstant.BILL_TABLE_NAME); // 页面模板id da_data_collect_bill_log 单据
        }else if (StringUtil.isNotEmpty(type) && CollectConstant.BANKSTATEMENT_NAME.equalsIgnoreCase(type))
        {
            batchMap.put("cloglistflag", CollectConstant.BANKSTATEMENT); // 资料类型 07
            batchMap.put("cloglistflag_name", CollectConstant.BANKSTATEMENT_NAME); // 资料类型名称 银行流水
            batchMap.put("cPageId", CollectConstant.BANK_TABLE_NAME); //页面id da_data_collect_bill_log 单据
            batchMap.put("cTemplateId", CollectConstant.BANK_TABLE_NAME); // 页面模板id da_data_collect_bill_log 单据

        }else if (StringUtil.isNotEmpty(type) && CollectConstant.PZZLREL_NAME.equalsIgnoreCase(type))
        {
            batchMap.put("cloglistflag", CollectConstant.PZZLREL); // 凭证关联关系 09
            batchMap.put("cloglistflag_name", CollectConstant.PZZLREL_NAME); // 资料类型名称 凭证与原始资料关系
            batchMap.put("cPageId", "da_fc_voucherinfo_list"); //页面id da_fc_voucherinfo_list 凭证与原始资料关系
            batchMap.put("cTemplateId", "da_fc_voucherinfo_list_template"); // 页面模板id da_fc_voucherinfo_list_template 凭证与原始资料关系

        } else
        {
            if(StringUtil.isEmpty(type)){
                hadlerResultViewVO.setCstatus(false);
                hadlerResultViewVO.setMsg("资料类型未获取到，"+type);
                return hadlerResultViewVO;
            }
            String msg = "查询参数已传输。";
            if(CollectConstant.VOUCHER_NAME.equals(type)){//是凭证
                batchMap.put("cloglistflag","01");
                batchMap.put("cloglistflag_name", StaticCodeClass.vouloglistflag_name);
                batchMap.put("msg",msg);
            }else if(CollectConstant.ACCOUNTBOOK_NAME.equals(type)){//是账簿
                batchMap.put("cloglistflag","03");
                batchMap.put("cloglistflag_name","会计账簿");
                batchMap.put("msg",msg);
            }else if(CollectConstant.ACCOUNTREPORT_NAME.equals(type)){//是报告
                batchMap.put("cloglistflag","04");
                batchMap.put("cloglistflag_name","会计报告");
                batchMap.put("msg",msg);
            }else if(CollectConstant.OTHERACCOUNTREPORT_NAME.equals(type)){//是其他会计资料
                batchMap.put("cloglistflag","05");
                batchMap.put("cloglistflag_name","其他会计资料");
                batchMap.put("msg",msg);
            }

        }
        String cinnercode = CollectionUtil.getStringFromMap(currOrgMap, "cinnercode");
        String cApiDataType = CollectionUtil.getStringFromMap(extSysConfig,"capidatatype");
        String url = CollectionUtil.getStringFromMap(extSysConfig,"curl");
        String csystag = CollectionUtil.getStringFromMap(extSysConfig,"csystag");
        String cbackurl = CollectionUtil.getStringFromMap(extSysConfig,"cbackurl");//回调参数
        String cbw_language = CollectionUtil.getStringFromMap(extSysConfig,"cbw_language");
        batchMap.put("corgninnercode",cinnercode);//组织内置编码
        batchMap.put("capidatatype",cApiDataType);
        batchMap.put("curl",url);
        batchMap.put("csystag",csystag);
        batchMap.put("cbackurl",cbackurl);
        batchMap.put("cbw_language",cbw_language);


        batchMap.put("csynstartperiod",enComParametersUtil.getPeriod(params, "会计期间开始年月", 1));//会计期间开始年月
        batchMap.put("csynendperiod",enComParametersUtil.getPeriod(params, "会计期间结束年月", 0));//会计期间结束年月
        batchMap.put("cstarttime", startdate); // 执行开始时间
        batchMap.put("csynstatus", CollectConstant.COLLECTING_CODE); // 同步状态 01
        batchMap.put("csynstatus_name", CollectConstant.COLLECTING); // 同步状态名称 等待接收
        //默认覆盖
        batchMap.put("icover", 1);
        batchMap.put("icover_name", "是");

        //放入默认覆盖
        params.put("iscover", true);

        String cFileTransferType = enComParametersUtil.getCollecttype(params,"报文传输方式");//params.getString("报文传输方式");
        if("0".equals(cFileTransferType)){
            batchMap.put("cfiletransfertype","filepackage");
            batchMap.put("cfiletransfertype_name","文件包");
        }else if("1".equals(cFileTransferType)){
            batchMap.put("cfiletransfertype","bw");
            batchMap.put("cfiletransfertype_name","报文格式");
        }
        params.put("serialnumber",enComParametersUtil.getSerialnumber(params,"同步序列号"));//params.getString("同步序列号"));
        Map sourceBatch = saveSourceBatch(params.getFieldMap(), "", JsonUtil.toJSON(params.getFieldMap()), batchMap, false, currOrgMap, dsid);
        if (StringUtils.isNotBlank(CollectionUtil.getStringFromMap(sourceBatch,"failmsg")))
        {
            hadlerResultViewVO.setCstatus(false);
            hadlerResultViewVO.setMsg(CollectionUtil.getStringFromMap(sourceBatch,"failmsg"));

            return hadlerResultViewVO;
        }
        params.put("同步序列号",sourceBatch.get("cserialnumber"));
        hadlerResultViewVO.setCstatus(true);
        return hadlerResultViewVO;
    }

    private Map makeRtnMapBySuccess(String msg,Params params){
        Map msgmap = new HashMap<>();
        msgmap.put("code","0000");
        msgmap.put("是否成功","1");
        msgmap.put("issuccess","1");
        msgmap.put("msg",msg);
        LOG.info("HTTP,接收数据成功----参数为{}",params.getFieldMap());
        return msgmap;
    }
    private Map makeRtnMapByErrorParam(String msg,Params params,String key){
        Map msgmap = new HashMap<>();
        msgmap.put("code","0100");
        msgmap.put("是否成功","0");
        msgmap.put("issuccess","0");
        msgmap.put("msg",msg);
        LOG.error("HTTP,接收数据失败----"+key+"为{}，异常信息为{},接收参数为",
                params.getString(key),msg,params.getFieldMap());
        return msgmap;
    }
    private Map makeRtnMapByErrorRtn(String msg,Params params){
        Map msgmap = new HashMap<>();
        msgmap.put("code","0100");
        msgmap.put("是否成功","0");
        msgmap.put("issuccess","0");
        msgmap.put("msg",msg);
        LOG.error("HTTP,接收数据处理失败----异常信息为{},接收参数为{}",
                msg,params.getFieldMap());
        return msgmap;
    }
    private Map makeRtnMapByException(String msg,Params params,Exception e){
        Map msgmap = new HashMap<>();
        msgmap.put("code","0100");
        msgmap.put("是否成功","0");
        msgmap.put("issuccess","0");
        msgmap.put("msg",msg);
        LOG.error("记账凭证接收数据后，出现异常，参数为{},异常为{}",params, LogUtil.getStringByException(e));
        return msgmap;
    }

    private Map saveSourceBatch(Map getFkBillrule, String json, String param, Map batchmap, boolean isrepeat, Map currOrgnIdMap, String dsID)
    {
        Map sourceBatchMap = new HashMap<>();

        String cFileTransferType = CollectionUtil.getStringFromMap(batchmap,"cfiletransfertype");//filepackage bw
        if("filepackage".equals(cFileTransferType)){
            sourceBatchMap.put("cfiletransfertype","filepackage");
            sourceBatchMap.put("cfiletransfertype_name","文件包");
        }else if("bw".equals(cFileTransferType)){
            sourceBatchMap.put("cfiletransfertype","bw");
            sourceBatchMap.put("cfiletransfertype_name","报文格式");
        }else
        {
            sourceBatchMap.put("cfiletransfertype","bw");
            sourceBatchMap.put("cfiletransfertype_name","报文格式");
        }

        String extcode = CollectionUtil.getStringFromMap(getFkBillrule, "extcode");
        String serialnumber = CollectionUtil.getStringFromMap(getFkBillrule, "serialnumber");
        String batchcguid = Guid.g();
        if (StringUtils.isBlank(serialnumber))
        {
            serialnumber = Guid.g();
        }
        sourceBatchMap.put("extcode",extcode);
        sourceBatchMap.put("cserialnumber",serialnumber);//流水号
        //sourceBatchMap.put("csourcesysname","费控");
        sourceBatchMap.put("ibatchmnum","");//来源批次
        sourceBatchMap.put("cjsondetail",json);
        sourceBatchMap.put("param","");
        sourceBatchMap.put("cguid",batchcguid);
        makeDefalultValue(sourceBatchMap,currOrgnIdMap, dsID);
        sourceBatchMap.putAll(batchmap);
        //插入前校验当前组织是否发布了重复或者存在覆盖情况的收集
        String csourceorgncode = CollectionUtil.getStringFromMap(batchmap, "csourceorgncode");//组织编码
        String csourceorgnid = CollectionUtil.getStringFromMap(batchmap, "csourceorgnid");//组织id
        String csynstartperiod = CollectionUtil.getStringFromMap(batchmap, "csynstartperiod");//开始时间
        String csynendperiod = CollectionUtil.getStringFromMap(batchmap, "csynendperiod");//结束时间

        //   isrepeat = daGetBillDetialService.querySourceBatchIsrepeatByAuto(csourceorgnid, csourceorgncode, csynstartperiod, csynendperiod, CollectConstant.BILL, dsID);
        //推送数据默认都是新的批次收集数据
        if (false)
        {
            sourceBatchMap.put("csynstatus", CollectConstant.COLLECTION_COMPLETED_CODE);
            sourceBatchMap.put("csynstatus_name", CollectConstant.COLLECTION_COMPLETED);
            sourceBatchMap.put("msg","存在正在收集的记录,请在数据收集完成后再重复收集");
            sourceBatchMap.put("failmsg","存在正在收集的记录,请在数据收集完成后再重复收集");
            return sourceBatchMap;
        }else
        {
            sourceBatchMap.put("csynstatus", CollectConstant.WAIT_CODE);
            sourceBatchMap.put("csynstatus_name", CollectConstant.WAIT);
        }
        daGetBillDetialService.inserSourceBatchByDsid(sourceBatchMap,dsID);

        DbService dbService = new ApsContextDb().getDb();
        dbService.commit();
        return sourceBatchMap;
    }

    private static Map makeDefalultValue(Map map, Map currOrgnIdMap, String dsID){
        String corgnid = CollectionUtil.getStringFromMap(currOrgnIdMap,"cguid");

        String corgnid_name = CollectionUtil.getStringFromMap(currOrgnIdMap,"cname");
        String cadminorgnid = CollectionUtil.getStringFromMap(currOrgnIdMap,"cadminorgnid");
        String ccreatedate = CollectionStringUtil.getSysDateTime();
/*        String ccreatorid = "1";
        String ccreatorid_name = "admin";*/

        map.put("corgnid", corgnid);
        map.put("corgnid_name", corgnid_name);
        map.put("cadminorgnid", cadminorgnid);
        Params params = new Params();
        params.put("currOid",corgnid);
        DbService db = new ApsContextDb().getDb(dsID);
        Map adminorgmap = db.queryMapById("da_api_bill_auto.queryMe", params);
        map.put("cadminorgnid_name", CollectionUtil.getStringFromMap(adminorgmap,"corganscopename"));
        map.put("ctimestamp", String.valueOf(System.currentTimeMillis()));
        map.put("ccreatedate",ccreatedate);

/*        map.put("ccreatorid", ccreatorid);
        map.put("ccreatorid_name",ccreatorid_name);*/

        map.put("csourceorgnid", corgnid);
        map.put("csourceorgnid_name",corgnid_name);

        return map;
    }
}
