package com.aisino.da.apiservice.service;

import java.util.Map;
/**
*@Description 外部系统接口
*@Param
*@Return
*<AUTHOR>
*@Date 2025-08-06
*@Time 6:06
*/
public interface ExtSystemService {
    String  getType();//类型
    /**
    *@Description Map daParams  档案参数
     *            Object obj    ExtSystemCommon 返回值
    *@Param [daParams, obj]
    *@Return java.util.Map<java.lang.String,java.lang.Object>
    *<AUTHOR>
    *@Date 2025-08-06
    *@Time 9:15
    */

    Map<String,Object> getData(Map daParams,Object obj);
}
