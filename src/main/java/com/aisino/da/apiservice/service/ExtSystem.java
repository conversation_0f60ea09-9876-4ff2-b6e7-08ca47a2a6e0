package com.aisino.da.apiservice.service;

import cn.hutool.json.JSONUtil;
import com.aisino.da.apiservice.constant.ExtSystemConstant;
import com.aisino.da.apiservice.factory.ExtSystemCommonFactory;
import com.aisino.da.apiservice.factory.ExtSystemServiceFactory;
import com.aisino.da.apiservice.util.ExtSystemUtil;
import com.aisino.da.common.util.ListMapOrderUtils;
import com.aisino.da.common.util.MapUtil;
import com.aisino.da.common.util.ArrayUtil;
import com.aisino.da.common.util.SplitListUtil;
import com.aisino.da.core.util.JsonConvertXmlDAUtil;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.checkerframework.checker.units.qual.A;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/**
*@Description 外部系统调用
*@Param
*@Return
*<AUTHOR>
*@Date 2025-08-06
*@Time 6:37
*/

public class ExtSystem {
    //集合分批数
    public static final int default_splitSize = 1000;
    public static final String CODE = "000000";//成功编码
    public static final String suffix = ".json";//文件名后缀
    public static final String connect = "_";//连接符
    //日志
    private static final Log log = LogFactory.getLog(ExtSystem.class);

    public static Map<String,Object> callExtSystem(Map daparams){
        String log_serialnumber= ArrayUtil.getStringFromMap(daparams,"serialnumber");
        String serialnumber="【"+ ArrayUtil.getStringFromMap(daparams,"serialnumber")+"】::";
        String orgcode=ArrayUtil.getStringFromMap(daparams,"orgcode");
        String orgid=ArrayUtil.getStringFromMap(daparams,"orgid");
        String dsid=ArrayUtil.getStringFromMap(daparams,"dsid");
        String extcode=ArrayUtil.getStringFromMap(daparams,"extcode");
        String datatype=ArrayUtil.getStringFromMap(daparams,"datatype");
        String datatypename=ArrayUtil.getStringFromMap(daparams,"datatypename");
        //根据extcode 调用通用外部接口
        long timestart = System.currentTimeMillis();
        ExtSystemCommon extSystemCommon= ExtSystemCommonFactory.getExtSystemCommon(extcode);
        if(null==extSystemCommon){
            String cbusinessException="未获取到【"+extcode+"】的相关实现!";
            Map reMap=ExtSystemUtil.callBillCollectDataService(daparams,CODE,cbusinessException,"","1","1",null);
            return MapUtil.getReturnMap(cbusinessException);

        }
        Object object=extSystemCommon.dealCommon(daparams);
        //根据extcode+datatype 调用外部具体接口 比如 "CellWill_单据资料"
        String type=extcode+"_"+ datatypename;
        ExtSystemService extSystemService= ExtSystemServiceFactory.getExtSystemService(type);
        if(null==extSystemService){
            String cbusinessException="未获取到【"+type+"】的相关实现!";
            Map reMap=ExtSystemUtil.callBillCollectDataService(daparams,CODE,cbusinessException,"","1","1",null);
            return MapUtil.getReturnMap(cbusinessException);

        }
        //根据实现取数据。
        Map<String,Object> extDataMap=extSystemService.getData(daparams,object);
        long timeend = System.currentTimeMillis();
        log.info(serialnumber+"【"+type+"】接口实现获取数据耗时:"+(double)(timeend-timestart)/1000+"秒!");
        if(ArrayUtil.isEmpty(extDataMap)){
            String cbusinessException="【"+type+"】的相关实现返回数据为空!";
            Map reMap=ExtSystemUtil.callBillCollectDataService(daparams,CODE,cbusinessException,"","1","1",null);
            return MapUtil.getReturnMap(cbusinessException);
        }
        String message=ArrayUtil.getStringFromMap(extDataMap,"message");
        //有业务错误信息
        if(!ArrayUtil.isBlank(message)){
            Map reMap=ExtSystemUtil.callBillCollectDataService(daparams,CODE,message,"","1","1",null);
            return MapUtil.getReturnMap(message);
        }
        Map dataMap=(Map)extDataMap.get("data");
        if(ArrayUtil.isEmpty(dataMap)){
            String cbusinessException="【"+type+"】的相关实现返回报文结构data数据为空!";
            Map reMap=ExtSystemUtil.callBillCollectDataService(daparams,CODE,cbusinessException,"","1","1",null);
            return MapUtil.getReturnMap(cbusinessException);
        }
        //根据data转化为标准报文结构
        StringBuffer sb=new StringBuffer();
        Map standardMap=getStandardMap(dataMap,sb,extcode);
        if(!ArrayUtil.isBlank(sb.toString())){
            Map reMap=ExtSystemUtil.callBillCollectDataService(daparams,CODE,sb.toString(),"","1","1",null);
            return MapUtil.getReturnMap(sb.toString());
        }
        //分批次调用标准接口
        String splitsize=ArrayUtil.getStringFromMap(extDataMap,"splitSize");
        //获取分批大小,二开传值splitSize就用splitSize，不传默认1000条一批
        int splitSize=ArrayUtil.isBlank(splitsize)?default_splitSize:Integer.parseInt(splitsize);
        //根据装换后的 standardMap 获取分批信息
        Map splitInfoMap=getSplitInfo(standardMap,splitSize);
        String totalBatchNo=ArrayUtil.getStringFromMap(splitInfoMap,"totalBatchNo");//分批信息//总的批次
        String totalnum=ArrayUtil.getStringFromMap(splitInfoMap,"totalnum");;//总的数量
        String cinfo=ArrayUtil.getStringFromMap(splitInfoMap,"cinfo");//分批信息
        cinfo=cinfo+"  单据总数:"+totalnum+"条,分 "+totalBatchNo+" 批";
        log.info(serialnumber+cinfo);
        List<Map> batchList=(List<Map>)splitInfoMap.get("batchList");//分批信息
        //分批调用档案标准接口
        sendDataToDaStandard(daparams,serialnumber,batchList,totalBatchNo);
        return MapUtil.getReturnMap("");
    }
    /**
    *@Description 标准报文转化
    *@Param [dataMap, daparams, sb, extcode]
    *@Return java.util.Map
    *<AUTHOR>
    *@Date 2025-08-11
    *@Time 9:01
    */

    private static Map  getStandardMap(Map dataMap, StringBuffer sb,String extcode) {
        Map standardMap=new HashMap();
        for(Object key: dataMap.keySet()){
            String keyStr=key.toString();
            String filename=extcode+connect+keyStr+suffix;
            List<Map> list=(List<Map>)dataMap.get(keyStr);
            List<Map> newlist=new ArrayList<>();
            if(!ArrayUtil.isEmpty(list)){
                for(Map m:list){
                    String json= JSONUtil.toJsonStr(m);
                    String standardJson= JsonConvertXmlDAUtil.getXmlFromJson(json, filename);
                    Map tempMap= new HashMap();
                    try{
                        tempMap=JSONUtil.toBean(standardJson,Map.class);
                    }catch (Exception e) {
                        String cbusinessException="【"+keyStr+"】报文转化报错!!";
                        if(ArrayUtil.isBlank(sb.toString())){
                            sb.append(cbusinessException);
                        }else{
                            sb.append(",").append(cbusinessException);
                        }
                    }
                    if(!ArrayUtil.isEmpty(tempMap)){
                        newlist.add(tempMap);
                    }
                }
            }
            standardMap.put(key,newlist);
        }
        return standardMap;
    }

    /**
    *@Description 发送数据到档案标准接口 分批
    *@Param [daparams, serialnumber, batchList, totalBatchNo]
    *@Return void
    *<AUTHOR>
    *@Date 2025-08-09
    *@Time 9:49
    */
    
    private static void sendDataToDaStandard(Map daparams, String serialnumber, List<Map> batchList, String totalBatchNo) {
        String datatypename=ArrayUtil.getStringFromMap(daparams,"datatypename");
        for(int i=0;i<batchList.size();i++){
            Map batchMap=batchList.get(i);
            String key=ArrayUtil.getStringFromMap(batchMap,"key");
            List<Map> data =(List<Map>)batchMap.get("value");
            int currentno=(i +1);
            String currentno_str=currentno+"";
            Map newSendMap=new HashMap();
            newSendMap.put(key,data);
            Map reMap=new HashMap();
            //单据资料传的是Map  其他类型都是 List<Map>
            long time1 = System.currentTimeMillis();
            Object obj="单据资料".equalsIgnoreCase(datatypename)?newSendMap:data;
            reMap=ExtSystemUtil.callBillCollectDataService(daparams,CODE,"","",totalBatchNo,currentno_str,obj);
            long time2 = System.currentTimeMillis();
            String msg=ArrayUtil.getStringFromMap(reMap,"msg");
            log.info(serialnumber+"第"+currentno+"批:"+key+data.size()+"条,调标准接口返回:"+msg+",耗时:"+(double)(time2-time1)/1000+"秒!");
        }
    }

    /**
    *@Description 获取分批信息，并按批次大小排序。
    *@Param [dataMap, splitSize]
    *@Return java.util.Map
    *<AUTHOR>
    *@Date 2025-08-06
    *@Time 9:46
    */
    
    private static Map getSplitInfo(Map dataMap, int splitSize) {
        Map reMap=new HashMap();
        int totalBatchNo=0;//总的批次
        int totalnum=0;//总的数量
        String cinfo="";
        List<Map> batchList=new ArrayList<>();
        for(Object key: dataMap.keySet()){
            List<Map> list=(List<Map>)dataMap.get(key);
            int n= SplitListUtil.geSplitByList(list.size(),splitSize);
            if(ArrayUtil.isBlank(cinfo)){
                cinfo=key+":"+list.size()+"条,分"+n+"批";
            }else{
                cinfo=cinfo+";"+key+":"+list.size()+"条,分"+n+"批";
            }
            totalnum = totalnum + list.size();
            totalBatchNo=totalBatchNo+n;
            List<List<Map>> splitList= SplitListUtil.geSplitByList(list,splitSize);
            for(int i = 0; i < splitList.size(); i++){
                Map tempMap=new HashMap();
                List<Map> tempList=splitList.get(i);
                tempMap.put("key",key);
                tempMap.put("value",tempList);
                tempMap.put("size",tempList.size());
                batchList.add(tempMap);
            }
        }
        //按批次大小排序
        ListMapOrderUtils.sort(batchList,true,"size");
        reMap.put("totalBatchNo",totalBatchNo);
        reMap.put("totalnum",totalnum);
        reMap.put("cinfo",cinfo);
        reMap.put("batchList",batchList);
        return reMap;
    }
}
