package com.aisino.da.apiservice.action;

import cn.hutool.json.JSONUtil;
import com.aisino.aosplus.core.mvc.annotation.Action;
import com.aisino.aosplus.core.mvc.annotation.ContentType;
import com.aisino.aosplus.core.mvc.annotation.NoNeedLogin;
import com.aisino.aosplus.core.mvc.annotation.Request;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aps.common.async.AosAsyncTask;
import com.aisino.aps.common.pojo.vo.APSResponseVo;
import com.aisino.da.apiservice.service.ExtSystem;
import com.aisino.da.core.util.ArrayUtil;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.HashMap;
import java.util.Map;


/**
*@Description 外部系统接口公共入口
*@Param
*@Return
*<AUTHOR>
*@Date 2025-08-06
*@Time 5:58
*/
@NoNeedLogin
@Action("da/extsystem")
public class ExtSystemAction
{
    private static final Log log = LogFactory.getLog(ExtSystemAction.class);
    @ContentType
    @Request.Post("getdata")
    public APSResponseVo getData(Params params) {
        new AosAsyncTask((db, transaction) -> {
            Map<String,Object> data=params.getCloneMap();
            if(!ArrayUtil.isEmpty(data)){
                String jsonParams= JSONUtil.toJsonStr(data);
                log.info("***调用第三方服务成功,jsonParams==="+jsonParams);
                ExtSystem.callExtSystem(data);
            }
        }).start("***调用第三方服务成功,开始执行***");
        Map result= new HashMap();
        result.put("cstatus","1");
        result.put("message","");
        return new APSResponseVo(result);
    }
}
