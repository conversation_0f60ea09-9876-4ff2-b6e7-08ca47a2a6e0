package com.aisino.da.apiservice.factory;

import com.aisino.aosplus.core.ioc.BeanHelper;
import com.aisino.aosplus.core.ioc.annotation.Bean;
import com.aisino.aps.common.bean.BeanInit;
import com.aisino.da.apiservice.service.ExtSystemService;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
*@Description 外部系统接口工厂
*@Param
*@Return
*<AUTHOR>
*@Date 2025-08-06
*@Time 6:12
*/

@Bean
public class ExtSystemServiceFactory implements BeanInit {
    //静态map
    private static  Map<String, ExtSystemService> ExtSystemServiceMap = new ConcurrentHashMap<>();
    @Override
    public void afterInject() {
        ExtSystemServiceMap = BeanHelper.getBeansOfType(ExtSystemService.class).values()
                .stream().collect(Collectors.toMap(ExtSystemService::getType, Function.identity()));
    }
    /**
    *@Description 根据类型获取ExtSystemService具体实现
    *@Param [type]
    *@Return com.aisino.da.apiservice.service.ExtSystemService
    *<AUTHOR>
    *@Date 2025-08-06
    *@Time 6:34
    */
    
    public static ExtSystemService getExtSystemService(String type){
        ExtSystemService extSystemService=ExtSystemServiceMap.get(type);
        if(null!=extSystemService){
            return extSystemService;
        }
        return null;
    }
}
