package com.aisino.da.apiservice.factory;

import com.aisino.aosplus.core.ioc.BeanHelper;
import com.aisino.aosplus.core.ioc.annotation.Bean;
import com.aisino.aps.common.bean.BeanInit;
import com.aisino.da.apiservice.service.ExtSystemCommon;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
*@Description  外部系统通用工厂
*@Param
*@Return
*<AUTHOR>
*@Date 2025-08-06
*@Time 6:09
*/

@Bean
public class ExtSystemCommonFactory implements BeanInit {
    //静态map
    private static  Map<String, ExtSystemCommon> ExtSystemCommonMap = new ConcurrentHashMap<>();
    @Override
    public void afterInject() {
        ExtSystemCommonMap = BeanHelper.getBeansOfType(ExtSystemCommon.class).values()
                .stream().collect(Collectors.toMap(ExtSystemCommon::getSystemCode, Function.identity()));
    }

    /**
    *@Description 根据类型获取ExtSystemCommon具体实现
    *@Param [type]
    *@Return com.aisino.da.apiservice.service.ExtSystemCommon
    *<AUTHOR>
    *@Date 2025-08-06
    *@Time 6:35
    */
    
    public static ExtSystemCommon getExtSystemCommon(String type){
        ExtSystemCommon extSystemCommon=ExtSystemCommonMap.get(type);
        if(null!=extSystemCommon){
            return extSystemCommon;
        }
        return null;
    }
}
