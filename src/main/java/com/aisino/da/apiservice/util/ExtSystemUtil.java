package com.aisino.da.apiservice.util;

import com.aisino.aosplus.core.ioc.BeanHelper;
import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.da.api.constant.CollectConstant;
import com.aisino.da.api.handler.AccountBookCollectDataServiceInterface;
import com.aisino.da.api.handler.AccountReportCollectDataServiceInterface;
import com.aisino.da.api.handler.OtherAccountReportCollectDataServiceInterface;
import com.aisino.da.api.handler.VoucherCollectDataServiceInterface;
import com.aisino.da.api.service.publicother.OtherCollectDataService;
import com.aisino.da.api.service.publicother.SaveAndCheckOtherDataDetialService;
import com.aisino.da.apiservice.constant.ExtSystemConstant;
import com.aisino.da.core.util.ArrayUtil;

import java.util.HashMap;
import java.util.Map;
/**
*@Description 二开工具类
*@Param 
*@Return 
*<AUTHOR>
*@Date 2025-08-06
*@Time 10:04
*/

public class ExtSystemUtil {

    @Inject
    static VoucherCollectDataServiceInterface voucherCollectDataServiceInterface;
    @Inject
    static AccountBookCollectDataServiceInterface accountBookCollectDataServiceInterface;
    @Inject
    static AccountReportCollectDataServiceInterface accountReportCollectDataServiceInterface;
    @Inject
    static OtherAccountReportCollectDataServiceInterface otherAccountReportCollectDataServiceInterface;
    /**
    *@Description 调标准接口方法
    *@Param [params, ccode, cbusinessException, csystemError, ctotalbatch, cbatch]
    *@Return java.util.Map
    *<AUTHOR>
    *@Date 2025-08-06
    *@Time 10:04
    */
    public static Map callBillCollectDataService(Map daparams,
                                                  String ccode,
                                                  String cbusinessException,
                                                  String csystemError,
                                                  String ctotalbatch,
                                                  String cbatch,
                                                  Object obj) {
        String datatypename=ArrayUtil.getStringFromMap(daparams,"datatypename");
        //根据资料类型名称获取报文定义的key
        String key= ExtSystemConstant.dataTypeName.get(datatypename);
        Map bzParamMap=new HashMap();
        bzParamMap.put("编码",ccode);
        bzParamMap.put("同步序列号", ArrayUtil.getStringFromMap(daparams,"serialnumber"));
        bzParamMap.put("来源系统编码",ArrayUtil.getStringFromMap(daparams,"extcode"));
        bzParamMap.put("资料类型",datatypename);
        bzParamMap.put("业务异常信息",cbusinessException);
        bzParamMap.put("系统错误信息",csystemError);
        bzParamMap.put("总批次",ctotalbatch);
        bzParamMap.put("批次号",cbatch);
        bzParamMap.put("报文传输方式","1");
        if(null!=obj){
            bzParamMap.put(key,obj);
        }
        Params p=new Params(bzParamMap);
        Map rtnMap = new HashMap();
        if(CollectConstant.VOUCHER_NAME.equalsIgnoreCase(datatypename)){
            rtnMap = voucherCollectDataServiceInterface.voucherInterfaceSyncCollectDataFromHTTP(p);
        }else if(CollectConstant.ACCOUNTBOOK_NAME.equalsIgnoreCase(datatypename)){
            rtnMap = accountBookCollectDataServiceInterface.accountBookInterfaceSyncCollectDataFromHTTP(p);
        }else if(CollectConstant.ACCOUNTREPORT_NAME.equalsIgnoreCase(datatypename)){
            rtnMap = accountReportCollectDataServiceInterface.accountReportInterfaceSyncCollectDataFromHTTP(p);
        }else if(CollectConstant.OTHERACCOUNTREPORT_NAME.equalsIgnoreCase(datatypename)){
            rtnMap = otherAccountReportCollectDataServiceInterface.otherAccountReportInterfaceSyncCollectDataFromHTTP(p);
        }else{
            OtherCollectDataService bean = BeanHelper.getBean(OtherCollectDataService.class);
            SaveAndCheckOtherDataDetialService adapt = bean.getAdapt(datatypename);
            rtnMap = adapt.collectOtherDetial(p);
        }
        return rtnMap;
    }
}
