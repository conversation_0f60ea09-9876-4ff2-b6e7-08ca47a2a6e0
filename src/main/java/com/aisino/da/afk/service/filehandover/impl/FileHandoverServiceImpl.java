package com.aisino.da.afk.service.filehandover.impl;

import com.aisino.aosplus.core.ConfigHelper;
import com.aisino.aosplus.core.common.fault.BusinessException;
import com.aisino.aosplus.core.dao.sql.Eso;
import com.aisino.aosplus.core.dao.sql.SqlInfo;
import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.annotation.Service;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.aosplus.core.util.Guid;
import com.aisino.aosplus.core.util.MapUtil;
import com.aisino.aosplus.core.util.StringUtil;
import com.aisino.aosplus.plugin.ms.Message;
import com.aisino.aosplus.session.SessionHelper;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import com.aisino.da.afk.dao.archivesaccpect.ArchivesAccpectDao;
import com.aisino.da.afk.dao.filehandover.FileHandoverDao;
import com.aisino.da.afk.dao.instkrecord.InstkrecordDao;
import com.aisino.da.afk.entity.filehandover.DaAfkOutstkrecordEntity;
import com.aisino.da.afk.entity.filehandover.DaAfkOutstkrecordlineEntity;
import com.aisino.da.afk.service.billdetail.BillDetailService;
import com.aisino.da.afk.service.filehandover.FileHandoverService;
import com.aisino.da.afk.util.stkrecord.StkRecordUtil;
import com.aisino.da.common.util.ArrayUtil;
import com.aisino.da.core.service.FileService;
import com.aisino.da.core.util.JsonConvertXmlDAUtil;
import com.google.gson.Gson;
import org.springframework.beans.BeanUtils;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/*
 *
 * @author：NiuShaoBo
 * @date： 2023/3/30
 * @description：
 * @modifiedBy：档案移交相关的业务处理
 * @version: 1.0
 */
@Service
public class FileHandoverServiceImpl implements FileHandoverService {

    @Inject
    private FileHandoverDao fileHandoverDao;

    @Inject
    private ArchivesAccpectDao archivesaccpectdao;

    @Inject
    private InstkrecordDao instkrecordDao;

    @Inject
    private BillDetailService billdetailservice;

    @Inject
    private ApsContextDb apsContextDb;

    private List<Map> insertList = new ArrayList<>();
    /**
     * 获取移交清册信息业务处理
     * @param params
     * @return
     */
    @Override
    public Map queryInventory(Params params) {
        Map inventory_map = fileHandoverDao.queryInventory(params);
        if (MapUtil.isEmpty(inventory_map)){
            return new HashMap();
        }
        // 判断是否是实体档案
        List children = null;
        Params child_params = new Params();
        child_params.put("corgnid", params.getString("corgnid"));
        child_params.put("cheadid", inventory_map.get("cguid"));
        if ("0".equals(inventory_map.get("ccarriertype"))) {
            children = fileHandoverDao.queryInventoryFile(child_params);
        } else if ("1".equals(inventory_map.get("ccarriertype"))) {
            children = fileHandoverDao.queryInventoryEntity(child_params);
        }
        String entityid = params.getString("entityid");
        inventory_map.put(entityid, children);
        return inventory_map;
    }

    /**
     * 移交单获取全宗号
     * @param corgnid
     * @return
     */
    @Override
    public String queryQznum(String corgnid) {
        return fileHandoverDao.queryQznum(corgnid);
    }

    /**
     * 移交单获取移交清册打印次数
     * @param params
     * @return
     */
    @Override
    public Long querycprintnum(Params params) {
        return fileHandoverDao.querycprintnum(params);
    }

    /**
     * 移交信息选择之前的校验
     * @param params
     * @return
     */
    @Override
    public String queryInfoCheck(Params params) {
        List<Map> check_list = null;
        StringBuilder check_msg = new StringBuilder();
        Integer check_flag;
        if ("0".equals(params.getString("ccarriertype"))) {
            check_list = fileHandoverDao.queryInfoCheck(params);
            check_flag = 0;
        } else  {
            check_list = fileHandoverDao.queryInfoCheckEn(params);
            check_flag = 1;
        }
        if (CollectionUtil.isNotEmpty(check_list)){
            check_list.forEach(item -> {
                if (check_flag == 0) {
                    check_msg.append("档号：");
                } else {
                    check_msg.append("盒号：");
                }
//                Map item = check_list.get(0);
                check_msg.append((String) item.get("cnum"));
                if (StringUtil.isNotEmpty((String) item.get("cstatus"))){
                    check_msg.append(item.get("cstatus"));
                } else if (StringUtil.isNotEmpty((String) item.get("cqualityctrl"))) {
                    check_msg.append(item.get("cqualityctrl"));
                } else if (StringUtil.isNotEmpty((String) item.get("ctransfer"))) {
                    check_msg.append(item.get("ctransfer"));
                } else if (StringUtil.isNotEmpty((String) item.get("cborrow"))) {
                    check_msg.append(item.get("cborrow"));
                } else if (StringUtil.isNotEmpty((String) item.get("cdestroy"))) {
                    check_msg.append(item.get("cdestroy"));
                }
                check_msg.append("\n");
            });
        }
        return check_msg.toString();
    }

    /**
     * 单据审核完成后，修改单据的状态
     * @param params
     * @return
     */
    @Override
    public Boolean updateType(Params params) {
        String cguid = params.getString("cguid");
        if (StringUtil.isEmpty(cguid)){
            throw new BusinessException("缺少主键！");
        }
        // ctransfer 移交状态 0 待移交 1 移交中 2 已接收
        if (StringUtil.isEmpty(params.getString("ctransfer"))) {
            params.put("ctransfer", "0");
            params.put("ctransfer_name", "待移交");
        }
        fileHandoverDao.updateTransType(params);
        return true;
    }

    /**
     * 实体出库
     * @param params
     * @return
     */
    @Override
    public String outStkEntity(Params params) {
        if (CollectionUtil.isEmpty(params.getList("cbill_guids")) ||
                StringUtil.isEmpty(params.getString("cmainguid"))
        ) {
            throw new BusinessException("缺少参数！");
        }
        if (!fileHandoverDao.queryCstatus(params)) {
            return "单据未审核";
        }
//        实体出库主表
        DaAfkOutstkrecordEntity daAfkOutstkrecordEntity = new DaAfkOutstkrecordEntity();
        daAfkOutstkrecordEntity.setDefault();
//        出库原因
        daAfkOutstkrecordEntity.setIdeliveryreason("2");
        daAfkOutstkrecordEntity.setIdeliveryreason_name("移交出库");
//        全宗号
        String cqznum = this.queryQznum(SessionHelper.getCurrentOrgnId());
        daAfkOutstkrecordEntity.setCqznum(cqznum);
//        单据编号
        daAfkOutstkrecordEntity.setCbillcode(this.getoutStkEntityCode(cqznum));
//        出库日期
        String date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        daAfkOutstkrecordEntity.setCdate(date);
//        单据id
        daAfkOutstkrecordEntity.setCsourcebillid(params.getString("cmainguid"));
//        实体出库子表
        List<DaAfkOutstkrecordlineEntity> outlineList = fileHandoverDao.queryBoxline2out(params);
        List<Object> outlineObjList = new ArrayList<>();
        outlineObjList = outlineList.stream().map(item -> {
            item.setDefault();
            item.setCheadid(daAfkOutstkrecordEntity.getCguid());
            item.setIdeliveryreason(2);
            DaAfkOutstkrecordlineEntity entity = new DaAfkOutstkrecordlineEntity();
            BeanUtils.copyProperties(item, entity);
            return entity;
        }).collect(Collectors.toList());
        fileHandoverDao.insertEntityOutRecord(daAfkOutstkrecordEntity, outlineObjList);

        // 更新移交档案状态， 因为移交档案状态只有三种，所以更新移交档案状态和装盒档案状态分开
        params.put("cprofile_status", "2");
        params.put("cprofile_status_name", "移交不在库");
        // 更新装盒表档案状态
        params.put("boxl_cprofile_status", "5");
        params.put("boxl_cprofile_status_name", "移交不在库");
        // 更新实体表档案状态
        params.put("entityfile_status", "6");
        params.put("entityfile_status_name", "移交不在库");
        fileHandoverDao.updateProfileStatus(params);
        // 更新移交状态
        Params transformP = new Params();
        transformP.put("cguid", params.getString("cmainguid"));
        if ("1".equals(params.getString("candovertype"))) {
//            对外移交，直接更新为已接收
            transformP.put("ctransfer", "2");
            transformP.put("ctransfer_name", "已接收");
        } else {
            transformP.put("ctransfer", "1");
            transformP.put("ctransfer_name", "移交中");
        }

        this.updateType(transformP);
        // 获取移交子表信息
//        List<Map> handoverLine = fileHandoverDao.queryHandoverLine(transformP);
//        // 更新状态
//        this.updatestate(handoverLine);
        // 清理库位
        try{
            this.clearStk(params);
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
        return "";
    }

    /**
     * 获取实体出库的单据编号
     * @return
     */
    @Override
    public String getoutStkEntityCode(String cqznum) {
        Map outstkrecord = apsContextDb.getDb().queryMap("select cguid from aps_data_resource_table where cTableCode = 'da_afk_outstkrecord'");
        Message msgcode = new Message( "aps.bd.EncodeSettingIssueNumberService");
        Map params = new HashMap();
//        params.put("pageid", "da_afk_outstkreview_box");
        params.put("pageid", "da_afk_outstkrecord_billcode");
        params.put("tableid", CollectionUtil.getStringFromMap(outstkrecord, "cguid"));
        params.put("iArchive", "1");
        Map pagemeta = new HashMap();
        Map parent = new HashMap();
        pagemeta.put("cqznum", cqznum);
        pagemeta.put("cparent", parent);
        params.put("pagemeta", pagemeta);
        Map publish = (Map) msgcode.publish(params);
        return CollectionUtil.getStringFromMap(publish, "cencode");
    }

    /**
     * 单据保存前的校验
     * @param params
     * @return
     */
    @Override
    public String queryBeforeSave(Params params) {
//        判断归档或者装盒ids不能为空，载体类型  cbill_guids, ccarriertype
        if (StringUtil.isEmpty(params.getString("ccarriertype")) ||
                CollectionUtil.isEmpty(params.getList("cbill_guids"))){
            throw new BusinessException("参数不正确");
        }
        // 如果单据是保存状态，判断子表的id不存在的，就删除了
        if ("saved".equals(params.getString("cstatus")) || "revise".equals(params.getString("cstatus"))) {
//            获取不在列表中的子表数据
            List<Map> maps = fileHandoverDao.queryById("da_afk_filehandover.queryFilehandoverlineNotExist", params);
            fileHandoverDao.deleteFilehandoverlineNotExist(maps);
//            清空这些档案的移交状态
            Params tranparams = new Params();
            tranparams.put("ccarriertype", params.getString("ccarriertype"));
            ArrayList<Object> arrayList = new ArrayList<>();
            maps.forEach(item -> {
                arrayList.add(item.get("cbill_guid"));
            });
            tranparams.put("cbill_guids", arrayList);
            if (!arrayList.isEmpty()) {
                this.lineAfterdel(tranparams);
            }
        }

        //  检查归档表的四个状态
        //        如果是实体，检查装盒子表联查的组卷表四个状态
        String infoCheck = this.queryInfoCheck(params);
        if (StringUtil.isNotEmpty(infoCheck)){
            return infoCheck;
        }
        return "";
    }

    /**
     * 单据保存后，更新移交状态为待移交
     * @param params
     * @return
     */
    @Override
    public String queryAfterSave(Params params) {
//        判断归档或者装盒ids不能为空，载体类型  cbill_guids, ccarriertype
        if (StringUtil.isEmpty(params.getString("ccarriertype")) ||
                CollectionUtil.isEmpty(params.getList("cbill_guids")) ||
                StringUtil.isEmpty(params.getString("candovertype"))
        ){
            throw new BusinessException("参数不正确");
        }
//        移交状态修改
        params.put("ctransfer", "0");
        Boolean ctransform = fileHandoverDao.updateTransform(params);
        if (!ctransform) {
            return "移交状态修改失败";
        }
        return "";
    }

    /**
     * 行删除后，将移交状态清空；
     * @param params
     * @return
     */
    @Override
    public String lineAfterdel(Params params) {
        if (StringUtil.isEmpty(params.getString("ccarriertype")) ||
                CollectionUtil.isEmpty(params.getList("cbill_guids"))){
            throw new BusinessException("参数不正确");
        }
        params.put("ctransfer", "");
        Boolean ctransform = fileHandoverDao.updateTransform(params);
        if (!ctransform) {
            return "移交状态修改失败";
        }
        return "";
    }

    /**
     * 移交单执行删除之后将移交状态清空
     * @param params
     * @return
     */
    @Override
    public String billAfterdel(Params params) {
        if (StringUtil.isEmpty(params.getString("cguid"))){
            throw new BusinessException("参数不正确");
        }
        List<Map> mapList = fileHandoverDao.queryHandoverLine(params);
        ArrayList<Object> cbill_guids = new ArrayList<>();
        mapList.forEach(item -> {
            if (!StringUtil.isEmpty(item.get("cbill_guid"))) {
                cbill_guids.add(item.get("cbill_guid"));
            }
            params.put("ccarriertype", item.get("ccarriertype"));
        });
        if (CollectionUtil.isEmpty(cbill_guids)) {
            return "";
        }
        params.put("cbill_guids", cbill_guids);
        return this.lineAfterdel(params);
    }

    /**
     * 更新移交四性检测状态
     * @param params
     * @return
     */
    @Override
    public Boolean updateSuaiTransferStatus(Params params) {
        if (StringUtil.isEmpty(params.getString("cguid"))){
            throw new BusinessException("参数不正确");
        }
        return fileHandoverDao.updateSuaiTransferStatus(params);
    }


    /**
     * 清空库位码， 回写库位容量
     * @param params
     */
    public void clearStk(Params params) {
        String corgnid = SessionHelper.getCurrentOrgnId();
        String corgnname = SessionHelper.getCurrentOrgnName();
        if (CollectionUtil.isEmpty(params.getList("boxids"))) {
            if (CollectionUtil.isEmpty(params.getList("cbill_guids"))) {
                throw new BusinessException("缺少参数！");
            }
            params.put("boxids", params.get("cbill_guids"));
        }
        /*回写所有盒内档案的状态、清除案卷上的库位码*/
        //根据盒号id查盒内案卷ids
        List<Map> entityFileIds=instkrecordDao.queryListMapBySqlId("da_afk_archivesKeeping.queryBoxLineAndEntityOutStk",params);
        if(CollectionUtil.isNotEmpty(entityFileIds)){
            List<Map> paramsList = new ArrayList<>();
            //遍历档案list构建回写参数
            for (Map map : entityFileIds) {
                Map params1 = new HashMap();
                //盒明细子表id
                params1.put("cboxlineid",CollectionUtil.getStringFromMap(map,"cguid"));
                //对应案卷id
                params1.put("centfileguid",CollectionUtil.getStringFromMap(map,"centfileguid"));
                paramsList.add(params1);
            }
            //回写盒明细子表的档案状态=移交不在库
            instkrecordDao.batchUpdateBySqlId("da_afk_filehandover.updateBoxLineInfoCancel", paramsList);
            //更新实体组卷：档案状态=移交不在库、 清空库位码？
            instkrecordDao.batchUpdateBySqlId("da_afk_filehandover.updateEntityFileInfoCancel", paramsList);

            //回写系统收集7个表 实体组卷状态=已装盒、清空库位码
            //案卷ids
            List<String> cfileIds = CollectionUtil.getStrColFromListMap(entityFileIds, "centfileguid");
            Map statusMap = new HashMap(4);
            statusMap.put("centityfilestatus", "7");
            statusMap.put("centityfilestatus_name", "移交不在库");
            statusMap.put("clocationcode", null);
            StkRecordUtil.dealFileStatus(cfileIds,statusMap);
        }
        List<String> groupIdList = null;
        //根据盒号boxids 查询盒id、盒所在库位的柜组id、格id
        List<Map> inStkLineInfo=instkrecordDao.queryListMapBySqlId("da_afk_archivesKeeping.queryBoxInfoByBoxIds",params);
        if(CollectionUtil.isNotEmpty(inStkLineInfo)){
            List<Map> paramsList = new ArrayList<>();
            for (Map map : inStkLineInfo) {
                Map params1 = new HashMap();
                //盒主表id
                params1.put("cguid",CollectionUtil.getStringFromMap(map,"cguid"));
                //柜组id
                params1.put("cstoregroup",CollectionUtil.getStringFromMap(map,"groupnum"));
                //格id
                params1.put("cstoregrid",CollectionUtil.getStringFromMap(map,"gridcode"));
                paramsList.add(params1);
            }
            //回写盒主表 状态=已装盒、 清空库位码
            instkrecordDao.batchUpdateBySqlId("da_afk_filehandover.updateBoxMainInfoCancel", paramsList);
            //回写库房组柜明细表 组柜已用容量
            instkrecordDao.batchUpdateBySqlId("da_afk_filehandover.updateGroupCapacityCancel", paramsList);
            //回写库房列格容量表 格已用容量
            instkrecordDao.batchUpdateBySqlId("da_afk_filehandover.updateGridCapacityCancel", paramsList);

            /*根据判断更新组柜明细的已使用组织id和name（此项需放在上面回写盒主表.groupnum 格id之后进行）*****/
            //先取出所有组柜ids
            groupIdList = inStkLineInfo.stream().map(e -> e.get("groupnum").toString()).collect(Collectors.toList());
            //去重
            groupIdList = groupIdList.stream().distinct().collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(groupIdList)) {
                //批次回写组柜的参数
                List<Map> paramsList1 = new ArrayList<>();
                params.put("groupids", groupIdList);
                //这些组柜id中 此组织使用记录不为空的组柜
                List<Map> notNullIdList = instkrecordDao.queryListMapBySqlId("da_afk_archivesKeeping.queryNotNullIdByGroupIds",params);
                for (String groupId : groupIdList) {
                    Map params1 = new HashMap();
                    //判断这个组柜 记录是否为空
                    boolean hasValue =false;
                    if(CollectionUtil.isNotEmpty(notNullIdList)){
                        for(Map map:notNullIdList){
                            if(map.containsValue(groupId)){
                                hasValue = true;
                                break;
                            }
                        }
                    }
                    //若此组织的此组柜记录为空，则需要将此组织从此组柜‘已使用组织id’删除掉
                    if(!hasValue) {
                        //此组柜的已使用组织id和name
                        String sql = "select wl.cguid,cusedorgs from da_bbs_storeroomline wl where wl.cguid =?";
                        Map usedOrgnInfo = instkrecordDao.queryMapBySql(sql, new Object[]{groupId});
                        if (!CollectionUtil.isBlankMap(usedOrgnInfo)) {
                            String usedorgs = CollectionUtil.getStringFromMap(usedOrgnInfo,"cusedorgs");
                            usedorgs = usedorgs.endsWith("，")?usedorgs.substring(0,usedorgs.length() - 1):usedorgs;
                            if (corgnname.equals(usedorgs)) {
                                //只有自己
                                usedorgs = usedorgs.replace( corgnname, "");
                            } else if (usedorgs.startsWith((corgnname + "，"))) {
                                //在开头
                                usedorgs = usedorgs.replace( corgnname + "，", "");
                            } else if (usedorgs.contains("，"+corgnname + "，")) {
                                //在中间
                                usedorgs = usedorgs.replace( "，"+corgnname + "，", "，");
                            } else if (usedorgs.endsWith("，"+corgnname)) {
                                //在末尾
                                usedorgs = usedorgs.replace(  "，"+corgnname, "");
                            }
                            params1.put("cusedorgs",usedorgs);
                            params1.put("cguid",groupId);
                            paramsList1.add(params1);
                        }
                    }
                }
                //回写库房组柜明细表 组柜已用组织id ,USEDORGS=nvl(USEDORGS,'') || ? ||','
                String sqlGroupn = "update da_bbs_storeroomline set cusedorgs = {cusedorgs} where cguid = {cguid} ";
                instkrecordDao.batchUpdateBySql(sqlGroupn,paramsList1);
            }
        }
    }

    /**
     * 下载对外移交的电子档案相关文件
     * @param params
     */
    @Override
    public String download(Params params){
        if (StringUtil.isEmpty(params.getString("cguid")) ||
                StringUtil.isEmpty(params.getString("corgnid"))) {
            throw new BusinessException("参数异常");
        }
        List<Map> ea_list = apsContextDb.getDb().queryMapListById("da_afk_filehandover_ea_download.get_cvoucherid", params);
        if (ea_list.size() == 0) {
            return null;
        }
        ArrayList<Object> cfilenums = new ArrayList<>();
        ArrayList<Object> cvoucherids = new ArrayList<>();
        ArrayList<Object> pz_cvoucherids = new ArrayList<>();
        ArrayList<Object> zb_cvoucherids = new ArrayList<>();
        ArrayList<Object> bg_cvoucherids = new ArrayList<>();
        ArrayList<Object> qt_cvoucherids = new ArrayList<>();
        Map voucherid_filenum_map = new HashMap();
        voucherid_filenum_map.put("PZ", new HashMap<>());
        voucherid_filenum_map.put("ZB", new HashMap<>());
        voucherid_filenum_map.put("BG", new HashMap<>());
        voucherid_filenum_map.put("QT", new HashMap<>());
        Map class_cvoucherids_map = new HashMap();
        class_cvoucherids_map.put("PZ", pz_cvoucherids);
        class_cvoucherids_map.put("ZB", zb_cvoucherids);
        class_cvoucherids_map.put("BG", bg_cvoucherids);
        class_cvoucherids_map.put("QT", qt_cvoucherids);
        ea_list.forEach(item -> {
            cfilenums.add((String) item.get("cfilenum"));
            cvoucherids.add((String) item.get("cvoucherid"));
            switch (CollectionUtil.getStringFromMap(item, "caccountclasscode")) {
                case "PZ":
                    Map pz = (Map) voucherid_filenum_map.get("PZ");
                    pz.put(item.get("cvoucherid"), item.get("cfilenum"));
                    pz_cvoucherids.add(item.get("cvoucherid"));
                    break;
                case "ZB":
                    Map zb = (Map) voucherid_filenum_map.get("ZB");
                    zb.put(item.get("cvoucherid"), item.get("cfilenum"));
                    zb_cvoucherids.add(item.get("cvoucherid"));
                    break;
                case "BG":
                    Map bg = (Map) voucherid_filenum_map.get("BG");
                    bg.put(item.get("cvoucherid"), item.get("cfilenum"));
                    bg_cvoucherids.add(item.get("cvoucherid"));
                    break;
                case "QT":
                    Map qt = (Map) voucherid_filenum_map.get("QT");
                    qt.put(item.get("cvoucherid"), item.get("cfilenum"));
                    qt_cvoucherids.add(item.get("cvoucherid"));
                    break;
            }
        });
        String filePath = ConfigHelper.getString("linuxFileSavePath");
        String tempurl = filePath + File.separator + params.getString("cguid") + "filehandoverFiles.zip";
        //tempurl = "C:\\" + File.separator + params.getString("cguid") + "filehandoverFiles.zip";
        FileOutputStream fout = null;
        ZipOutputStream zipOutputStream = null;
        InputStream inputStream = null;
        try {
            fout = new FileOutputStream(tempurl);
            zipOutputStream = new ZipOutputStream(fout);
            Map<String, String> props = ConfigHelper.getTagProps("aps-oss");
            String bucketName = ArrayUtil.getStringFromMap(props, "bucketName");
            // 下载归档数据
            Params t_params = new Params();
            t_params.put("corgnid", params.get("corgnid"));
            t_params.put("cfilenums", cfilenums);
            t_params.put("cvoucherids", cvoucherids);
            List<Map> ea_file_list = apsContextDb.getDb().queryMapListById("da_afk_filehandover_ea_download.get_ea_file", t_params);

            for(Map item: ea_file_list) {
                String fileurl = CollectionUtil.getStringFromMap(item, "curl");
                String ifiletype = CollectionUtil.getStringFromMap(item, "ifiletype");
                String cperiod = CollectionUtil.getStringFromMap(item, "cperiod");
                String cfilename = CollectionUtil.getStringFromMap(item, "cfilename");
                String cfilenum = CollectionUtil.getStringFromMap(item, "cfilenum");
                boolean flag = FileService.existsByBucket(bucketName, fileurl, "aps-oss");
                if(!flag){
                    continue;
//                    throw new ApsException(fileurl+",此文件不存在");
                }
                //将需要压缩的文件格式化为输入流
                try {
                    inputStream = FileService.readByBucket(bucketName, fileurl, "aps-oss");
                } catch (IOException e) {
                    e.printStackTrace();
                    continue;
                }
                String filepath = null;
                BufferedInputStream buffer = null;
                if ("4".equals(ifiletype)) {
                    String ctype = "元数据";
                    if(cfilename.startsWith("PZ-")){
                        ctype = "元数据" + File.separator + "凭证";
                        cfilename = cfilename.substring(3);
                    }else if(cfilename.startsWith("DJ-")){
                        ctype = "元数据" + File.separator + "单据";
                        cfilename = cfilename.substring(3);
                    }else if(cfilename.startsWith("PJ-")){
                        ctype = "元数据" + File.separator + "票据";
                        cfilename = cfilename.substring(3);
                    }else if(cfilename.startsWith("HD-")){
                        ctype = "元数据" + File.separator + "回单";
                        cfilename = cfilename.substring(3);
                    }
                    filepath = cfilenum + File.separator + ctype + File.separator + cfilename;
                } else {
                    filepath = cfilenum + File.separator + "版式文件" + File.separator + cfilename;
                }
                try{
                    ZipEntry zipEntry = new ZipEntry(filepath);
                    zipOutputStream.putNextEntry(zipEntry);
                    buffer = new BufferedInputStream(inputStream, 1024 * 10);
                    int read = 0;
                    byte[] data = new byte[1024 * 10];
                    while ((read = buffer.read(data, 0, 1024 * 10)) != -1) {
                        zipOutputStream.write(data, 0, read);
                    }
                    zipOutputStream.closeEntry();
                    buffer.close();
                } catch (IOException e) {
                    continue;
                } finally {
                    if (zipOutputStream != null) {
                        zipOutputStream.closeEntry();
                    }
                    if (buffer != null) {
                        buffer.close();
                    }
                }
            }
            // 下载原始文件
            StringBuffer sql = new StringBuffer();
            for(Object classtype: class_cvoucherids_map.keySet()) {
                List cbusinesstype = null;
                switch ((String) classtype) {
                    case "PZ":
                        cbusinesstype = Arrays.asList("PZ", "1", "2", "3");
                        break;
                    case "ZB":
                        cbusinesstype = Collections.singletonList("ZB");
                        break;
                    case "BG":
                        cbusinesstype = Collections.singletonList("BG");
                        break;
                    case "QT":
                        cbusinesstype = Collections.singletonList("QT");
                        break;
                }
                t_params = new Params();
                t_params.put("corgnid", params.get("corgnid"));
                t_params.put("cvoucherids", class_cvoucherids_map.get(classtype));
                List<Map> table_list = apsContextDb.getDb().queryMapListById("da_afk_filehandover_ea_download.get_fileinfo_" + classtype, t_params);
                // 根据file表名处理sql
                // 根据不同表名联查
                Map table_file_map = new HashMap();
                table_list.forEach(item -> {
                    if (table_file_map.containsKey(item.get("tablename"))) {
                        List table_cvoucherids = (List) table_file_map.get(item.get("tablename"));
                        table_cvoucherids.add(CollectionUtil.getStringFromMap(item, "cvoucherid"));
                    } else {
                        List table_cvoucherids = new ArrayList<>();
                        table_cvoucherids.add(CollectionUtil.getStringFromMap(item, "cvoucherid"));
                        table_file_map.put(item.get("tablename"), table_cvoucherids);
                    }
                });
                for(Object k: table_file_map.keySet()) {
                    Object v = table_file_map.get(k);
                    String table_sql = apsContextDb.getDb().getSql("da_afk_filehandover_ea_download.get_fileurl");
                    table_sql = table_sql.replace("tablename", (String) k);
                    SqlInfo sqlInfo = new SqlInfo(table_sql);
                    if (sql.length() != 0) {
                        sql.append(" union ");
                    }
                    Map temp_params = new HashMap();
                    temp_params.put("cbusinesstype", cbusinesstype);
                    temp_params.put("cvoucherids", v);
                    Eso eso = sqlInfo.getEso(temp_params);
                    sql.append(eso.toStaticSql());
                }
            }
            String final_sql = sql.toString();
            List<Map> billmaplist = apsContextDb.getDb().queryMapList(final_sql, new Params());
            for(Map item: billmaplist) {
                String storagemethod = (String) item.get("storagemethod");
                String dxccname = ArrayUtil.getStringFromMapWithDefault(item, "dxccname", "");
                String cstatus = ArrayUtil.getStringFromMapWithDefault(item, "cstatus", "");
                String fileurl = (String) item.get("fileurl");
                String cbusinesstype = (String) item.get("cbusinesstype");
                String cfilerealname = (String) item.get("cfilerealname");
                String filename = (String) item.get("filename");
                String syssource = (String) item.get("syssource");
                String filepath = (String) item.get("filepath");
                String dafilepath = (String) item.get("dafilepath");
                String cvoucherid = (String) item.get("cvoucherid");
                String fileName = null;
                String billbucket = null;
                String tagName = null;
                if ("1".equals(cstatus)) {
                    dxccname = bucketName;
                }
                if ("local".equals(storagemethod)) {
                    billbucket = StringUtil.isNotEmpty(dafilepath)? dafilepath: filepath;
                    fileName = filename;
                    tagName = syssource;
                } else if ("aliyunoss".equals(storagemethod)) {
                    billbucket = dxccname;
                    if (filepath.endsWith("/")) {
                        fileName = StringUtil.isEmpty(filepath) ? filename : filepath + filename;
                    } else {
                        fileName = StringUtil.isEmpty(filepath) ? filename : filepath + "/" + filename;
                    }
                    tagName = syssource;
                } else if ("aps-oss".equals(storagemethod)) {
                    //版式文件，使用档案的对象存储服务器
                    billbucket = dxccname;
                    fileName = filepath;
                    tagName = storagemethod;
                } else {
                    billbucket = dxccname;
                    fileName = filename;
                    tagName = StringUtil.isNotEmpty(storagemethod) ? storagemethod : "aps-oss";
                }
                if ("default".equals(storagemethod)) {
                    String local_file_path = dafilepath + File.separator + filename;
                    boolean flag = FileService.exists(local_file_path);
                    if(!flag){
                        continue;
                    }
                    inputStream = FileService.read(local_file_path);
                } else {
                    boolean flag = FileService.existsByBucket(billbucket, fileName, tagName);
                    if(!flag){
                        fileName += ("/" + filename);
                        if (!FileService.existsByBucket(billbucket, fileName, tagName)) {
                            continue;
                        }
                    }
                    //将需要压缩的文件格式化为输入流
                    try {
                        inputStream = FileService.readByBucket(billbucket, fileName, tagName);
                    } catch (IOException e) {
                        continue;
                    }
                }
                String temppath = null;
                BufferedInputStream buffer = null;
                String filenum = null;
                String fileclass = null;
                switch (cbusinesstype) {
                    case "PZ":
                        fileclass = "凭证";
                        filenum = (String) ((Map) voucherid_filenum_map.get("PZ")).get(cvoucherid);
                        break;
                    case "1":
                        fileclass = "凭证" + File.separator + "单据";
                        filenum = (String) ((Map) voucherid_filenum_map.get("PZ")).get(cvoucherid);
                        break;
                    case "2":
                        fileclass = "凭证" + File.separator + "票据";
                        filenum = (String) ((Map) voucherid_filenum_map.get("PZ")).get(cvoucherid);
                        break;
                    case "3":
                        fileclass = "凭证" + File.separator + "回单";
                        filenum = (String) ((Map) voucherid_filenum_map.get("PZ")).get(cvoucherid);
                        break;
                    case "ZB":
                        fileclass = "账簿";
                        filenum = (String) ((Map) voucherid_filenum_map.get("ZB")).get(cvoucherid);
                        break;
                    case "BG":
                        fileclass = "报告";
                        filenum = (String) ((Map) voucherid_filenum_map.get("BG")).get(cvoucherid);
                        break;
                    case "QT":
                        fileclass = "其它";
                        filenum = (String) ((Map) voucherid_filenum_map.get("QT")).get(cvoucherid);
                        break;
                }
                temppath = filenum + File.separator + "原始文件" + File.separator + fileclass + File.separator + filename;
                try{
                    ZipEntry zipEntry = new ZipEntry(temppath);
                    zipOutputStream.putNextEntry(zipEntry);
                    buffer = new BufferedInputStream(inputStream, 1024 * 10);
                    int read = 0;
                    byte[] data = new byte[1024 * 10];
                    while ((read = buffer.read(data, 0, 1024 * 10)) != -1) {
                        zipOutputStream.write(data, 0, read);
                    }
                    zipOutputStream.closeEntry();
                    buffer.close();
                } catch (IOException e) {
//                    throw new RuntimeException(e);
                    continue;
                } finally {
                    if (zipOutputStream != null) {
                        zipOutputStream.closeEntry();
                    }
                    if (buffer != null) {
                        buffer.close();
                    }
                }
                //票据的xml文件同步放入元数据下的票据文件夹中
                if("2".equalsIgnoreCase(cbusinesstype)&&filename.indexOf(".xml")!=-1){
                    String temppathpj = filenum + File.separator + "元数据" + File.separator + "票据" + File.separator + filename;
                    try{
                        ZipEntry zipEntry = new ZipEntry(temppathpj);
                        zipOutputStream.putNextEntry(zipEntry);
                        buffer = new BufferedInputStream(inputStream, 1024 * 10);
                        int read = 0;
                        byte[] data = new byte[1024 * 10];
                        while ((read = buffer.read(data, 0, 1024 * 10)) != -1) {
                            zipOutputStream.write(data, 0, read);
                        }
                        zipOutputStream.closeEntry();
                        buffer.close();
                    } catch (IOException e) {
//                    throw new RuntimeException(e);
                        continue;
                    } finally {
                        if (zipOutputStream != null) {
                            zipOutputStream.closeEntry();
                        }
                        if (buffer != null) {
                            buffer.close();
                        }
                    }
                }
            }
            // 更新移交状态为已接收
            // 移交单
            apsContextDb.getDb().updateById("da_afk_filehandover_ea_download.updatehandoverctransfer", params);
            // 归档表
            apsContextDb.getDb().updateById("da_afk_filehandover_ea_download.updateeactransfer", params);
            zipOutputStream.close();
        } catch (IOException ie) {
            ie.printStackTrace();
            return null;
        } catch (RuntimeException e) {
            e.printStackTrace();
            return null;
        } finally {
            if (null != zipOutputStream) {
                try {
                    zipOutputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (null != inputStream) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return tempurl;
    }

    /**
     * 提交前查看单据是否存在
     * @param params
     * @return
     */
    @Override
    public Long billExist(Params params) {
        if (StringUtil.isEmpty(params.getString("cguid"))){
            throw  new BusinessException("参数错误");
        }
        return apsContextDb.getDb().queryCountById("da_afk_filehandover.filehandoverexist", params);
    }

    /**
     * 生成文件
     * @param params
     * @return
     */

    @Override
    public String genfiles(Params params){
        if (StringUtil.isEmpty(params.getString("cguid")) ||
                StringUtil.isEmpty(params.getString("corgnid"))) {
            throw new BusinessException("参数异常");
        }
        List<Map> ea_list = apsContextDb.queryMapListById("da_afk_filehandover_ea_download.get_cvoucherid_bf", params);
        if (ea_list.size() == 0) {
            throw new BusinessException("档案不存在");
        }
        Map genfilesinfo = apsContextDb.getOneRecord("select igenfilestatus,cgenfilemsg from da_afk_file_handover where cguid = ?",new Object[]{params.getString("cguid")});
        String igenfilestatus = CollectionUtil.getStringFromMap(genfilesinfo,"igenfilestatus");
        String cgenfilemsg = CollectionUtil.getStringFromMap(genfilesinfo,"cgenfilemsg");
        if("1".equalsIgnoreCase(igenfilestatus)&&(cgenfilemsg==null||"".equalsIgnoreCase(cgenfilemsg))){
            throw new BusinessException("文件生成中，请稍后查看");
        }else if("2".equalsIgnoreCase(igenfilestatus)){
            throw new BusinessException("文件已生成");
        }
        params.put("igenfilestatus","1");
        apsContextDb.updateById("da_afk_filehandover_ea_download.updategenfilestatus", params);
        String rtn = "";
        for(Map item : ea_list){
            String corgnid = CollectionUtil.getStringFromMap(item,"corgnid");
            String corgncode = CollectionUtil.getStringFromMap(item,"corgncode");
            String cyear = CollectionUtil.getStringFromMap(item,"cyear");
            String cmonth = CollectionUtil.getStringFromMap(item,"cmonth");
            String caccountclasscode = CollectionUtil.getStringFromMap(item,"caccountclasscode");//会计分类编码
            String cdetailclasscode = CollectionUtil.getStringFromMap(item,"cdetailclasscode");//明细分类编码
            String cdetailclassguid_name = CollectionUtil.getStringFromMap(item,"cdetailclassguid_name");//明细分类名称
            String cperiod = CollectionUtil.getStringFromMap(item,"cperiod");//会计期间
            String cfilenum = CollectionUtil.getStringFromMap(item,"cfilenum");
            String cvouchercode = CollectionUtil.getStringFromMap(item,"cvouchercode");//凭证号
            String filePath = "";
            String cfileName = "";
            String cfileFullPath = "";
            if(cmonth!=null&&!"".equalsIgnoreCase(cmonth)){
                filePath = corgncode + "-" + cyear + "KU" + "/" + caccountclasscode + "/" + cmonth + "/" + cfilenum + "/";
            }else{
                filePath = corgncode + "-" + cyear + "KU" + "/" + caccountclasscode + "/" + cfilenum + "/";
            }
            String cvoucherid = CollectionUtil.getStringFromMap(item,"cvoucherid");//凭证id|账簿id|报告id|其他id
            if("PZ".equalsIgnoreCase(caccountclasscode)){
                //凭证
                cfileName = "PZ-" + cperiod + "-" + cvouchercode+ ".eep";
                cfileFullPath = filePath + cfileName;
                rtn = this.ysjToEEPAndUploadFile(cfileName, cfileFullPath, getVoucherYsj(cvoucherid, corgnid), "voucher.ftl", item);
                if(rtn!=null&&!"".equalsIgnoreCase(rtn)){
                    return rtn;
                }
                //票据
                //List<Map> PiaoJuYsjList = this.getPiaoJuYsj("374424301872412239", "941045491055893843");
                List<Map> PiaoJuYsjList = this.getPiaoJuYsj(cvoucherid, corgnid);
                if(PiaoJuYsjList!=null&&!PiaoJuYsjList.isEmpty()){
                    for (Map piaoju : PiaoJuYsjList) {
                        String ctype = CollectionUtil.getStringFromMap(piaoju,"ctype");
                        String ccode = CollectionUtil.getStringFromMap(piaoju,"ccode");
                        String cnumber = CollectionUtil.getStringFromMap(piaoju,"cnumber");
                        if(ccode!=null&&!"".equalsIgnoreCase(ccode)){
                            cfileName = "PJ-" + ctype + "-" + ccode + "-" + cnumber + ".eep";
                        }else{
                            cfileName = "PJ-" + ctype + "-" + cnumber + ".eep";
                            //数电票不再生成元数据
                            continue;
                        }
                        cfileName = cfileName.replaceAll("/","");
                        cfileFullPath = filePath + cfileName;
                        rtn = this.ysjToEEPAndUploadFile(cfileName, cfileFullPath, new Gson().toJson(piaoju), "piaoju.ftl", item);
                        if(rtn!=null&&!"".equalsIgnoreCase(rtn)){
                            return rtn;
                        }
                    }
                }
                //回单
                List<Map> HuiDanYsjList = this.getHuiDanYsj(cvoucherid, corgnid);
                if(HuiDanYsjList!=null&&!HuiDanYsjList.isEmpty()){
                    for (Map huidan : HuiDanYsjList) {
                        String ctransactionnum = CollectionUtil.getStringFromMap(huidan,"ctransactionnum");
                        huidan.put("cvoucherid",cvoucherid);
                        cfileName = "HD-" + ctransactionnum + ".eep";
                        cfileFullPath = filePath + cfileName;
                        rtn = this.ysjToEEPAndUploadFile(cfileName, cfileFullPath, new Gson().toJson(huidan), "huidan.ftl", item);
                        if(rtn!=null&&!"".equalsIgnoreCase(rtn)){
                            return rtn;
                        }
                    }
                }
                //单据
                List<Map> DanJuYsjList = this.getDanJuYsj(cvoucherid, corgnid);
                if(DanJuYsjList!=null&&!DanJuYsjList.isEmpty()){
                    for (Map danju : DanJuYsjList) {
                        String cftlName = CollectionUtil.getStringFromMap(danju,"cftlName");
                        if(cftlName==null||"".equalsIgnoreCase(cftlName)){
                            cftlName = "danju.ftl";
                        }
                        String cdabilltypename = CollectionUtil.getStringFromMap(danju,"cdabilltypename");
                        String cbillcode = CollectionUtil.getStringFromMap(danju,"cbillcode");
                        cfileName = "DJ-" + cdabilltypename + "-" + cbillcode + ".eep";
                        cfileFullPath = filePath + cfileName;
                        rtn = this.ysjToEEPAndUploadFile(cfileName, cfileFullPath, new Gson().toJson(danju), cftlName, item);
                        if(rtn!=null&&!"".equalsIgnoreCase(rtn)){
                            return rtn;
                        }
                    }
                }
            }else{
                cfileName = cperiod + "-" + cdetailclassguid_name + "-" + this.getls(cperiod + "-" + cdetailclassguid_name) + ".eep";
                cfileFullPath = filePath + cfileName;
                if("ZB".equalsIgnoreCase(caccountclasscode)){
                    //会计账簿
                    Map AccountBookMap = getAccountBookYsj(cvoucherid,cdetailclasscode);
                    String cftlName = CollectionUtil.getStringFromMap(AccountBookMap,"cftlName");
                    rtn = this.ysjToEEPAndUploadFile(cfileName, cfileFullPath, new Gson().toJson(AccountBookMap), cftlName, item);
                }else if("BG".equalsIgnoreCase(caccountclasscode)){
                    //会计报告
                    rtn = this.ysjToEEPAndUploadFile(cfileName, cfileFullPath, getAccountReportYsj(cvoucherid), "accountreport.ftl", item);
                }else if("QT".equalsIgnoreCase(caccountclasscode)){
                    //其他会计报告
                    if("QT02".equalsIgnoreCase(cdetailclasscode)){
                        //其他会计报告-银行对账单
                        rtn = this.ysjToEEPAndUploadFile(cfileName, cfileFullPath, getQTbankstatementYsj(cvoucherid), "otheraccountreport-bankstatement.ftl", item);
                    }else{
                        //其他会计报告-通用
                        rtn = this.ysjToEEPAndUploadFile(cfileName, cfileFullPath, getQTAccountReportYsj(cvoucherid), "otheraccountreport.ftl", item);
                    }
                }
                if(rtn!=null&&!"".equalsIgnoreCase(rtn)){
                    return rtn;
                }
            }
        }
        if(rtn==null||"".equalsIgnoreCase(rtn)){
            if(insertList!=null&&!insertList.isEmpty()){
                apsContextDb.batchInsertT("da_afk_ofdfileurl",insertList);
                insertList.clear();
            }
        }
        return rtn;
    }

    /**
     * 更新生成文件状态
     * @param rtn
     */

    @Override
    public void updategenfilestatus(Params params,String rtn) {
        if(rtn==null||"".equalsIgnoreCase(rtn)){
            params.put("igenfilestatus","2");
            params.put("cgenfilemsg","");
        }else{
            params.put("igenfilestatus","0");
            params.put("cgenfilemsg",rtn);
        }
        apsContextDb.updateById("da_afk_filehandover_ea_download.updategenfilestatus", params);
    }

    /**
     * 生成并上传元数据文件
     * @param cfileName
     * @param cfileFullPath
     * @param cYsjData
     * @param cftlName
     * @param data
     * @return
     */

    public String ysjToEEPAndUploadFile(String cfileName, String cfileFullPath, String cYsjData, String cftlName, Map data) {
        if(cYsjData==null||"".equalsIgnoreCase(cYsjData)){
            return null;
        }
        Params params = new Params();
        params.put("cfileName", cfileFullPath);
        byte[] file = JsonConvertXmlDAUtil.getXmlFromJson(cYsjData, cftlName).getBytes(StandardCharsets.UTF_8);
        Map rtn = FileService.uploadFile(params, cfileFullPath, file);
        Object object = rtn.get("cstatus");
        if (object != null && !"".equals(object)) {
            file = null;
            if (!"0".equals(object) && 0 != Integer.parseInt(object.toString())) {
                Map insertMap = new HashMap();
                insertMap.put("cGuid", Guid.g());
                insertMap.put("cfilenum", data.get("cfilenum"));
                insertMap.put("cvouguid", data.get("cvoucherid"));
                insertMap.put("cyear", data.get("cyear"));
                insertMap.put("cmonth", data.get("cmonth"));
                insertMap.put("curl", cfileFullPath);
                insertMap.put("cfilename", cfileName);
                insertMap.put("ifiletype", 4);
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                insertMap.put("cCreateDate", sdf.format(new Date()));
                insertMap.put("cTimeStamp", Guid.g());
                insertMap.put("cOrgnId", data.get("corgnid"));
                insertMap.put("cCreatorId", SessionHelper.getCurrentUserId());
                insertMap.put("cAdminOrgnId", data.get("cadminorgnid"));
                insertMap.put("cOrgnId_name", data.get("corgnid_name"));
                insertMap.put("cAdminOrgnId_name", data.get("cadminorgnid_name"));
                insertMap.put("cCreatorId_name", SessionHelper.getCurrentUserRealName());
                insertList.add(insertMap);
                return null;
            } else {
                Object msg = rtn.get("message");
                return msg != null && !"".equals(msg) ? msg.toString() : "调用上传失败，无报错信息！";
            }
        } else {
            file = null;
            return "调用上传失败，返回值为空！";
        }
    }

    /**
     * 凭证元数据
     * @param cvoucherid
     * @param corgnid
     * @return
     */

    public String getVoucherYsj(String cvoucherid, String corgnid) {
        String listSql = "select cdetailclassname,cperioddatelist,csourcesysname,cguid from da_fc_voucherinfo where cvoucherid = ? and corgnid = ?";
        String mainSql = "select corgnid,corgnid_name,cvoucherid,cvoucherdate,cvoucode,cvoutypename,cperioddate," +
                "cvoucreatorname,creviewer,cauditorname,cpostername,iaffix,idebittotal,icredittotal,csummary  from " +
                "da_fc_md_voucher where clistguid = ? and corgnid = ?";
        String detailsSql = "select cvoucherline,cacctstr,iqty,cuomname,iunitprice,iamt_f,ccurname,iexchangerate,idebitamt,icreditamt from " +
                "da_fc_md_voucherz where clistguid = ? and corgnid = ?";
        Map map = apsContextDb.queryMap(listSql, new Object[]{cvoucherid,corgnid});
        Object cguid = map.remove("cguid");
        Map mapMain = apsContextDb.queryMap(mainSql, new Object[]{cguid,corgnid});
        List<Map> detailsList = apsContextDb.queryMapList(detailsSql, new Object[]{cguid,corgnid});
        List<Map> banksList = apsContextDb.queryMapList("select ctransactionnum from da_fc_banksreceipt s where (s.cvoucherid = ? or s.cvoucherstatus = 1 and cmatchvoucherid = ?) and s.cOrgnId = ?",new Object[]{cvoucherid,cvoucherid,corgnid});
        List<Map> invslist = apsContextDb.queryMapList("select ctype,cnumber from da_invoice i where i.cvoucherid= ? and i.corgnid = ?",new Object[]{cvoucherid,corgnid});
        List<Map> billslist = billdetailservice.getBillTypeAndCode(cvoucherid, corgnid);
        map.put("main",mapMain);
        map.put("details",detailsList);
        map.put("banks",banksList);
        map.put("invs",invslist);
        map.put("bills",billslist);
        return new Gson().toJson(map);
    }

    /**
     * 票据元数据
     * @param cvoucherid
     * @param corgnid
     * @return
     */

    public List<Map> getPiaoJuYsj(String cvoucherid, String corgnid) {
        List<Map> list = apsContextDb.queryMapList("select * from da_invoice i where i.cvoucherid= ? and i.corgnid = ?",new Object[]{cvoucherid,corgnid});
        if(list==null||list.isEmpty()){
            return null;
        }
        list.forEach(map -> {
            List<Map> details = apsContextDb.queryMapList("select * from da_invoiceline l where l.cheadguid= ?",map.get("cguid"));
            map.put("details",details);
        });
        return list;
    }

    /**
     * 回单元数据
     * @param cvoucherid
     * @param corgnid
     * @return
     */

    public List<Map> getHuiDanYsj(String cvoucherid, String corgnid) {
        List<Map> list = apsContextDb.queryMapList("select * from da_fc_banksreceipt s where (s.cvoucherid = ? or s.cvoucherstatus = 1 and cmatchvoucherid = ?) and s.cOrgnId = ?",new Object[]{cvoucherid,cvoucherid,corgnid});
        return list;
    }

    /**
     * 单据元数据
     * @param cvoucherid
     * @param corgnid
     * @return
     */

    public List<Map> getDanJuYsj(String cvoucherid, String corgnid) {
        List<Map> list = apsContextDb.queryMapList("select * from da_api_fk_file_collection i where i.cvoucherid = ? and i.corgnid = ?",new Object[]{cvoucherid,corgnid});
        if(list!=null&&!list.isEmpty()){
            for(Map map : list){
                if(billdetailservice.isStandardBill(map)){
                    String json = billdetailservice.getStandardBill(map);
                    map.put("json",json);
                    String cdabilltypename = CollectionUtil.getStringFromMap(map,"cdabilltypename");
                    String[] gdzc = {"财智云固定资产卡片","财智云固定资产变更","财智云固定资产折旧","财智云固定资产摊销","财智云固定资产清理","财智云固定资产调入","财智云固定资产调出"};
                    if(cdabilltypename!=null&&Arrays.asList(gdzc).contains(cdabilltypename)){
                        map.put("cftlName","danju-gdzckp.ftl");
                    }else{
                        map.put("cftlName","danju.ftl");
                    }
                }else{
                    billdetailservice.getNStandardBill(map);
                }
            }
        }
        return list;
    }

    /**
     * 会计账簿元数据
     * @param cvoucherid
     * @return
     */

    public Map getAccountBookYsj(String cvoucherid, String cdetailclasscode) {
        String listSql = "select cdetailclasscode,cdetailclassname,cperioddatelist,LEFT(cperioddatelist,4) as iyear, creportname,csourcesysname from da_fc_accountbook where cguid = ?";
        Map listMap = apsContextDb.queryMap(listSql, new Object[]{cvoucherid});
        switch (cdetailclasscode) {
            case "ZB01" ://ZB01,总账
                glBook(cvoucherid,listMap);
                break;
            case "ZB02" ://ZB02,明细账
                subsidiaryBook(cvoucherid,listMap);
                break;
            case "ZB03" ://ZB03,现金日记账 1现金 2银行
                dayBook(cvoucherid,listMap,1);
                break;
            case "ZB04" ://ZB04,银行存款日记账 1现金 2银行
                dayBook(cvoucherid,listMap,2);
                break;
            case "ZB05" ://ZB05,固定资产明细账
                faDetail(cvoucherid,listMap);
                break;
            case "ZB06" : //ZB06,科目余额表
                accountBalance(cvoucherid,listMap);
                break;
            case "ZB07" : //ZB07,序时账
                timeBook(cvoucherid,listMap);
                break;
            case "ZB08" : //ZB08,往来明细账
                transactionBook(cvoucherid,listMap);
                break;
            case "ZB09" : //ZB09,固定资产折旧表*/
                faDeprDetail(cvoucherid,listMap);
                break;
            default:
                common(cvoucherid,listMap,0);
        }
        return listMap;
    }
    public Map<String, Object> common(Object object,Map<String, Object> listMap, int itype) {
        if(itype==0){
            listMap.put("cftlName","accountbook.ftl");
        }
        String mainSql = "select corgnid,corgnid_name,cperioddate,ccurname from da_fc_md_accountbook where clistguid = ?";
        Map<String, Object> mainMap = apsContextDb.queryMap(mainSql, object);
        listMap.put("main",mainMap);
        return listMap;
    }
    //总账
    public Map<String, Object> glBook(Object object,Map<String, Object> listMap) {
        listMap.put("cftlName","accountbook-zz.ftl");
        String mainSql = "select corgnid,corgnid_name,cyear,cperiod,ccurname from da_fc_md_glbook where clistguid = ?";
        String detailsSql = "select cacctcode,cacctname,ciinitbalanceamtdirection,iinitbalanceamt,iperioddebitamt,iperiodcreditamt," +
                "iyearaccumulateddebitamt,iyearaccumulatedcreditamt,cbalancedirection,ibalanceamt " +
                "from da_fc_md_glbookz where clistguid = ?";
        Map<String, Object> mainMap = apsContextDb.queryMap(mainSql, object);
        if(!CollectionUtil.isBlankMap(mainMap)){
            List<Map> detailsList = apsContextDb.queryMapList(detailsSql, object);
            listMap.put("main",mainMap);
            listMap.put("details",detailsList);
            return listMap;
        }else {
            return common(object,listMap,1);
        }
    }
    public Map<String, Object> subsidiaryBook(Object object,Map<String, Object> listMap) {
        listMap.put("cftlName","accountbook-mxz.ftl");
        String mainSql = "select corgnid,corgnid_name,cacctstr,cperiod,ccurname from da_fc_md_subsidiary_book where clistguid = ?";
        String detailsSql = "select cvoucherdate,cvoucode,csummary,idebitamt,icreditamt,cbalancedirsel,ibalanceamt from " +
                "da_fc_md_subsidiary_bookz where clistguid = ?";
        Map<String, Object> mainMap = apsContextDb.queryMap(mainSql, object);
        if(!CollectionUtil.isBlankMap(mainMap)){
            List<Map> detailsList = apsContextDb.queryMapList(detailsSql, object);
            listMap.put("main",mainMap);
            listMap.put("details",detailsList);
            return listMap;
        }else {
            return common(object,listMap,1);
        }
    }
    public Map<String, Object> dayBook(Object object,Map<String, Object> listMap,int icashtype) {
        if(icashtype==1){
            listMap.put("cftlName","accountbook-xjrjz.ftl");
        }else if(icashtype==2){
            listMap.put("cftlName","accountbook-yhckrjz.ftl");
        }
        String mainSql = "select corgnid,corgnid_name,cacctstr,cperiod,ccurname from da_fc_md_daybook where clistguid = ? and icashtype = ?";
        String detailsSql = "select cvoucherid,cvoucherdate,cvoucode,csummary,coacctstr,idebitamt,icreditamt,cbalancedirsel,ibalanceamt  from " +
                "da_fc_md_daybookz where clistguid = ?";
        Map<String, Object> mainMap = apsContextDb.queryMap(mainSql, object, icashtype);
        if(!CollectionUtil.isBlankMap(mainMap)){
            List<Map> detailsList = apsContextDb.queryMapList(detailsSql, object);
            listMap.put("main",mainMap);
            listMap.put("details",detailsList);
            return listMap;
        }else {
            return common(object,listMap,1);
        }
    }
    public Map<String, Object> accountBalance(Object object,Map<String, Object> listMap) {
        listMap.put("cftlName","accountbook-kmyeb.ftl");
        String mainSql = "select corgnid,corgnid_name,cperiod,ccurname from da_fc_md_accountbalance where clistguid = ?";
        String detailsSql = "select cacctcode,cacctname,ciinitdebitblcamt,ciinitcreditblcamt,ciperioddebitaccrual,ciperiodcreditaccrual," +
                "cidebityearacc,cicredityearacc,cienddebitblc,ciendcreditblc from da_fc_md_accountbalancez where clistguid = ?";
        Map<String, Object> mainMap = apsContextDb.queryMap(mainSql, object);
        if(!CollectionUtil.isBlankMap(mainMap)){
            List<Map> detailsList = apsContextDb.queryMapList(detailsSql, object);
            listMap.put("main",mainMap);
            listMap.put("details",detailsList);
            return listMap;
        }else {
            return common(object,listMap,1);
        }
    }
    public Map<String, Object> timeBook(Object object,Map<String, Object> listMap) {
        listMap.put("cftlName","accountbook-xsz.ftl");
        String mainSql = "select corgnid,corgnid_name,cperiod,ccurname from da_fc_md_timebook where clistguid = ?";
        String detailsSql = "select cacctcode,cacctname,cvoucherdate,cvoucode,csummary,cidebitamt,cicreditamt,cbalancedirsel," +
                "cibalanceamt from da_fc_md_timebookz where clistguid = ?";
        Map<String, Object> mainMap = apsContextDb.queryMap(mainSql, object);
        if(!CollectionUtil.isBlankMap(mainMap)){
            List<Map> detailsList = apsContextDb.queryMapList(detailsSql, object);
            listMap.put("main",mainMap);
            listMap.put("details",detailsList);
            return listMap;
        }else {
            return common(object,listMap,1);
        }
    }
    public Map<String, Object> transactionBook(Object object,Map<String, Object> listMap) {
        listMap.put("cftlName","accountbook-wlmxz.ftl");
        String mainSql = "select corgnid,corgnid_name,cperiod,ccurname from da_fc_md_transactionbook where clistguid = ?";
        String detailsSql = "select cacctcode,cacctname,cvoucherdate,cvoucode,csummary,cidebitamt,cicreditamt,cbalancedirsel,cibalanceamt,civerifiedamt," +
                "ciunverifiedamt from da_fc_md_transactionbookz where clistguid = ?";
        Map<String, Object> mainMap = apsContextDb.queryMap(mainSql, object);
        if(!CollectionUtil.isBlankMap(mainMap)){
            List<Map> detailsList = apsContextDb.queryMapList(detailsSql, object);
            listMap.put("main",mainMap);
            listMap.put("details",detailsList);
            return listMap;
        }else {
            return common(object,listMap,1);
        }
    }
    public Map<String, Object> faDetail(Object object,Map<String, Object> listMap) {
        listMap.put("cftlName","accountbook-gdzcmxz.ftl");
        String detailsSql = "select cassetclassid_name,cassetcode,cassetname,iinitblcorgamt,iendblcorgamt,idebitorgamt,icreditorgamt,iinitblcamiuvr," +
                "iendblcamiuvr,idebitamiuvr,icreditamiuvr,iinitblcdepr,iendblcdepr,idebitdepr,icreditdepr,iinitblcdepryearacc,iendblcdepryearacc," +
                "idebitdepryearacc,icreditdepryearacc,inetresiduals,inetplaces from da_fc_md_fadetail where clistguid = ?";
        List<Map> detailsList = apsContextDb.queryMapList(detailsSql, object);
        if(CollectionUtil.isNotEmpty(detailsList)){
            Object corgnid1 = listMap.get("corgnid");
            Object corgnid_name = listMap.get("corgnid_name");
            Object cperioddatelist = listMap.get("cperioddatelist");
            HashMap<Object, Object> mainMap = new HashMap<>();
            mainMap.put("corgnid",corgnid1);
            mainMap.put("corgnid_name",corgnid_name);
            mainMap.put("cperioddatelist",cperioddatelist);
            listMap.put("main",mainMap);
            listMap.put("details",detailsList);
            return listMap;
        }else {
            return common(object,listMap,1);
        }
    }
    public Map<String, Object> faDeprDetail(Object object,Map<String, Object> listMap) {
        listMap.put("cftlName","accountbook-gdzczjb.ftl");
        String detailsSql = "select cassetcode,cassetname,ciinitorgamt,ciinitdepracc,ciinitdepryearacc,ciinitamiuvr,ciinitnetresiduals,\n" +
                "       ciinitnetplaces,ciperiodorgamtincrease,ciperiodorgamtdecrease,ciperioddeprincrease,ciperioddeprdecrease,\n" +
                "       ciyeardeprincrease,ciyeardeprdecrease,ciperioddepramt,ciperiodimpairproincrease,ciperiodimpairprodecrease,ciendblcorgamt,cienddepracc\n" +
                ",cienddepryearacc,ciendamiuvr,ciendnetresiduals,ciendnetplaces from da_fc_md_fadeprdetail where clistguid = ?";
        List<Map> detailsList = apsContextDb.queryMapList(detailsSql, object);
        if(CollectionUtil.isNotEmpty(detailsList)){
            Object corgnid1 = listMap.get("corgnid");
            Object corgnid_name = listMap.get("corgnid_name");
            Object cperioddatelist = listMap.get("cperioddatelist");
            HashMap<Object, Object> mainMap = new HashMap<>();
            mainMap.put("corgnid",corgnid1);
            mainMap.put("corgnid_name",corgnid_name);
            mainMap.put("cperioddatelist",cperioddatelist);
            listMap.put("main",mainMap);
            listMap.put("details",detailsList);
            return listMap;
        }else {
            return common(object,listMap,1);
        }
    }

    /**
     * 会计报告元数据
     * @param cvoucherid
     * @return
     */

    public String getAccountReportYsj(String cvoucherid) {
        String listSql = "select cdetailclassname,cperioddatelist,creportname,csourcesysname from da_fc_accountreport where cguid = ?";
        String mainSql = "select corgnid,corgnid_name,reportcode,reportname,cperiod from da_fc_md_report where clistguid = ?";
        Map map = apsContextDb.queryMap(listSql, new Object[]{cvoucherid});
        Map mapMain = apsContextDb.queryMap(mainSql, new Object[]{cvoucherid});
        map.put("main",mapMain);
        return new Gson().toJson(map);
    }

    /**
     * 其他-通用元数据
     * @param cvoucherid
     * @return
     */

    public String getQTAccountReportYsj(String cvoucherid) {
        String listSql = "select cdetailclassname,cperioddatelist,creportname,csourcesysname from da_fc_otheraccountreport where cguid = ?";
        String mainSql = "select corgnid,corgnid_name,cperioddate,ccurname from da_fc_md_other where clistguid = ?";
        Map map = apsContextDb.queryMap(listSql, new Object[]{cvoucherid});
        Map mapMain = apsContextDb.queryMap(mainSql, new Object[]{cvoucherid});
        map.put("main",mapMain);
        return new Gson().toJson(map);
    }

    /**
     * 其他-银行对账单元数据
     * @param cvoucherid
     * @return
     */

    public String getQTbankstatementYsj(String cvoucherid) {
        String sql = "select cdetailclassname,cperioddatelist,creportname,csourcesysname from da_fc_otheraccountreport where cguid = ?";
        String sqlMain = "select issuer,fbnum,currency,accountno,accountname,clientno,cyear,cmonth,printnum,printdate,csourceorganname,csourceorganid from da_fc_md_otherstatement where clistguid = ?";
        Map map = apsContextDb.queryMap(sql, new Object[]{cvoucherid});
        Map mapMain = apsContextDb.queryMap(sqlMain, new Object[]{cvoucherid});
        map.put("main",mapMain);
        return new Gson().toJson(map);
    }

    /**
     * 获取流水
     * @param cfileName
     * @return
     */

    public String getls(String cfileName) {
        /*Map lsMap = apsContextDb.getOneRecord("select SUBSTRING_INDEX( i.cfilename, '-', 2 ) as yj, substring(i.cfilename,LENGTH(SUBSTRING_INDEX( i.cfilename, '-', 2 ) ) + 2,5) as ls from da_afk_ofdfileurl i where i.ifiletype = 4 and (substring(i.cfilename,LENGTH(SUBSTRING_INDEX( i.cfilename, '-', 2 ) ) + 2,5) REGEXP '[^0-9.]') = 0 and SUBSTRING_INDEX( i.cfilename, '-', 2 ) = ? order by substring(i.cfilename,LENGTH(SUBSTRING_INDEX( i.cfilename, '-', 2 ) ) + 2,5) DESC",new Object[]{cfileName});
        String ls = lsMap!=null&&!lsMap.isEmpty()?String.format("%05d", (CollectionUtil.getIntFromMap(lsMap,"ls")+1)):"00001";*/
        Map lsMap = apsContextDb.getOneRecord("select cnorule,isnno from da_afk_genfile_ls where cnorule = ?",new Object[]{cfileName});
        String ls = "00001";
        if(lsMap!=null&&!lsMap.isEmpty()){
            ls = String.format("%05d", (CollectionUtil.getIntFromMap(lsMap,"isnno")));
            lsMap.put("isnno",CollectionUtil.getIntFromMap(lsMap,"isnno")+1);
            apsContextDb.updateT("da_afk_genfile_ls",lsMap,"isnno","cnorule");
        }else{
            Map ins = new HashMap();
            ins.put("cguid",Guid.g());
            ins.put("cnorule",cfileName);
            ins.put("isnno",2);
            apsContextDb.insertT("da_afk_genfile_ls",ins);
        }
        return ls;
    }
}
