package com.aisino.da.fc.service.layoutGeneration;

import com.aisino.aosplus.core.ioc.annotation.Impl;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.da.fc.service.layoutGeneration.impl.LayoutGenerationServiceImpl;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@Impl(LayoutGenerationServiceImpl.class)
public interface LayoutGenerationService {

    /*
    * 凭证生成模拟打印文件
    * */
    Map genVoucherPDF(Params params);

    /*
     * 凭证生成模拟打印文件
     * */
    Map genAcctLedgerDataPDF(Params params) throws IOException;

    /*
     * 版式生成日志
     * */
    List<Map> getLayoutGenLog(Params params);

    /**
     * 删除版式生成日志数据
     * @param params 参数
     * @return 以分页+数据的形式返回
     */
    boolean delLayoutGenLog(Params params,String ctype);


}
