package com.aisino.da.fc.service.layoutGeneration.impl;

import com.aisino.aosplus.core.common.fault.BusinessException;
import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.annotation.Service;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.aosplus.core.util.Guid;
import com.aisino.aosplus.plugin.ms.Message;
import com.aisino.aosplus.session.SessionHelper;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import com.aisino.da.fc.bean.SyncArchiveApplyEnum;
import com.aisino.da.fc.dao.layoutGeneration.GenPdfConfigDao;
import com.aisino.da.fc.service.layoutGeneration.GenPdfConfigService;
import com.google.common.base.Enums;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class GenPdfConfigServiceImpl implements GenPdfConfigService {

    @Inject
    private GenPdfConfigDao genPdfConfigDao;

    @Inject
    private ApsContextDb db;

    @Override
    public Map<String, Object> loadLeftTreeChange() {
        Map<String, Object> map = genPdfConfigDao.getTreeZero();
        // 一级节点
        List<Map> treeOneTest = genPdfConfigDao.getTreeOneTest();
        treeOneTest.forEach(map1 -> {
            List<Map> treeTwoList = genPdfConfigDao.getTreeTwoTest(map1.get("cguid") + "");
            treeTwoList.forEach(map2 -> {
                List<Map> threeList = genPdfConfigDao.getTreeThree(map2.get("cguid") + "");
                threeList.forEach(map3 -> {
                    // 由于三级节点并不是都有子节点，type=1的三级节点并且判断是否存在此应用
                    String type = map3.get("type")+"";
                    String ccode = map3.get("ccode") + "";
                    if("1".equals(type)){
                        /*// 判断是否存在此应用   同步归档使用该业务
                        SyncArchiveApplyEnum syncArchiveApplyEnum = Enums.getIfPresent(SyncArchiveApplyEnum.class, ccode).orNull();
                        if(syncArchiveApplyEnum!=null){
                            String guid = syncArchiveApplyEnum.getGuid();
                            Map<String, Object> applyInfoByGuid = syncArchiveDao.getApplyInfoByGuid(guid);
                            // 等于null 证明没有存在，返回空集合
                            if(applyInfoByGuid==null){
                                map3.put("children",new ArrayList<>(1));
                                return;
                            }
                        }*/
                        map3.put("children",genPdfConfigDao.getTreeFour(map3.get("cguid")+""));
                    }
                });
                map2.put("children",threeList);
            });
            map1.put("children",treeTwoList);
        });
        // 最后放到根节点当中。
        map.put("children",treeOneTest);
        return map;
    }



    @Override
    public List<Map> getPageNodeList(Params params) {
        List<Map> reMap = genPdfConfigDao.getGenPdfData(params);
        if(reMap.isEmpty()){
            Map dataMap = new HashMap();
            String cguid = Guid.g();
            dataMap.put("cgenfile",0);
            dataMap.put("cguid",cguid);
            reMap.add(dataMap);
        }
        return reMap;
    }

    @Override
    public Map savePdf(Map map) {
        try{
            /*String cguid = Guid.g();
            map.put("cguid",cguid);*/
            map.put("cadminorgnId",SessionHelper.getCurrentAdminOrgnId());
            map.put("cadminorgnId_name",SessionHelper.getCurrentAdminOrgnName());
            map.remove("id");
            map.remove("ctemplatepdfname");
            db.insert("da_fc_gen_pdf",map);
            Map reMap = new HashMap();
            reMap.put("status","000");
            reMap.put("msg","保存成功");
            return reMap;
        } catch (Exception e){
            /*失败保存日志表*/
            Map dataMap = new HashMap();
            dataMap.put("cguid",Guid.g());
            dataMap.put("cheadid",map.get("cparentid"));
            dataMap.put("csunguid",map.get("cguid"));
            dataMap.put("errmsg",e.getMessage());
            dataMap.put("copera","add");
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date=new Date();
            String cfstime=sdf.format(date);
            dataMap.put("ccreatedate",cfstime);
            Long ctimestamp = date.getTime();
            dataMap.put("ctimestamp",ctimestamp.toString());
            dataMap.put("ccreatorid",SessionHelper.getCurrentUserId());
            db.insert("da_fc_perconfig_log",dataMap);
            Map reMap = new HashMap();
            reMap.put("status","1");
            reMap.put("msg","保存失败");
            return reMap;
        }
    }

    @Override
    public Map updatePdf(Map map) {
        try{
            String updateFidld = "carchivalid,carchivalname,cgenfile";
            List<Map> daList = new ArrayList<>();
            daList.add(map);
            db.batchUpdateT("da_fc_gen_pdf",daList,updateFidld.split(","),new Object[]{"cguid"});
            Map reMap = new HashMap();
            reMap.put("status","000");
            reMap.put("msg","保存成功");
            return reMap;
        } catch (Exception e){
            /*失败保存日志表*/
            Map dataMap = new HashMap();
            dataMap.put("cguid",Guid.g());
            dataMap.put("cheadid",map.get("cparentid"));
            dataMap.put("csunguid",map.get("cguid"));
            dataMap.put("errmsg",e.getMessage());
            dataMap.put("copera","modify");
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date=new Date();
            String cfstime=sdf.format(date);
            dataMap.put("ccreatedate",cfstime);
            Long ctimestamp = date.getTime();
            dataMap.put("ctimestamp",ctimestamp.toString());
            dataMap.put("ccreatorid",SessionHelper.getCurrentUserId());
            db.insert("da_fc_perconfig_log",dataMap);
            Map reMap = new HashMap();
            reMap.put("status","1");
            reMap.put("msg","保存失败");
            return reMap;
        }
    }

    @Override
    public Map savePdfLine(Map map) {
        try{
            map.remove("_X_ROW_KEY");
            map.put("cguid",Guid.g());
            db.insert("da_fc_gen_pdf_line",map);
            Map reMap = new HashMap();
            reMap.put("status","000");
            reMap.put("msg","保存成功");
            return reMap;
        } catch (Exception e){
            /*失败保存日志表*/
            Map dataMap = new HashMap();
            dataMap.put("cguid",Guid.g());
            dataMap.put("cheadid",map.get("cparentid"));
            dataMap.put("csunguid",map.get("cguid"));
            dataMap.put("errmsg",e.getMessage());
            dataMap.put("copera","add");
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date=new Date();
            String cfstime=sdf.format(date);
            dataMap.put("ccreatedate",cfstime);
            Long ctimestamp = date.getTime();
            dataMap.put("ctimestamp",ctimestamp.toString());
            dataMap.put("ccreatorid",SessionHelper.getCurrentUserId());
            db.insert("da_fc_perconfig_log",dataMap);
            /*保存模板配置关系到平台*/
            this.addTemplateRelation(map);
            Map reMap = new HashMap();
            reMap.put("status","1");
            reMap.put("msg","保存失败");
            return reMap;
        }
    }

    @Override
    public Map updatePdfLine(Map map) {
        try{
            String updateFidld = "ctemplatesortid,ctemplatesortcode,ctemplatesortname,ctemplatepdfid,ctemplatepdfcode,ctemplatepdfname,capplyorgnid,capplyorgname,callin";
            List<Map> daList = new ArrayList<>();
            daList.add(map);
            db.batchUpdateT("da_fc_gen_pdf_line",daList,updateFidld.split(","),new Object[]{"cguid"});
            /*删除平台保存的模板配置关系*/
            List<Map> dataList = new ArrayList<>();
            dataList.add(map);
            this.deleteTemplateRelation(dataList);
            /*保存模板配置关系到平台*/
            this.addTemplateRelation(map);
            Map reMap = new HashMap();
            reMap.put("status","000");
            reMap.put("msg","保存成功");
            return reMap;
        } catch (Exception e){
            /*失败保存日志表*/
            Map dataMap = new HashMap();
            dataMap.put("cguid",Guid.g());
            dataMap.put("cheadid",map.get("cparentid"));
            dataMap.put("csunguid",map.get("cguid"));
            dataMap.put("errmsg",e.getMessage());
            dataMap.put("copera","modify");
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date=new Date();
            String cfstime=sdf.format(date);
            dataMap.put("ccreatedate",cfstime);
            Long ctimestamp = date.getTime();
            dataMap.put("ctimestamp",ctimestamp.toString());
            dataMap.put("ccreatorid",SessionHelper.getCurrentUserId());
            db.insert("da_fc_perconfig_log",dataMap);
            Map reMap = new HashMap();
            reMap.put("status","1");
            reMap.put("msg","保存失败");
            return reMap;
        }
    }

    @Override
    public List<Map> getPdfLineList(Params params) {
        String cparentid = params.getString("cparentid");
        List<Map> reMap = genPdfConfigDao.getPdfLineList(cparentid);
        return reMap;
    }

    @Override
    public boolean delPdfLineList(Map map) {
        Params params = new Params();
        params.put("cguid",map.get("cguid"));
        /*调用删除平台的模版配置关系方法*/
        Map dataMap = db.queryMap("select * from da_fc_gen_pdf_line where cguid = ?",params.getString("cguid"));
        List<Map> daList= new ArrayList<>();
        daList.add(dataMap);
        this.deleteTemplateRelation(daList);
        boolean result = true;
        result = genPdfConfigDao.deteteALL(map);
        return result;
    }

    /*添加模版数据配置关系到平台*/
    private void addTemplateRelation(Map dataMap){
        Message message = new Message("aps_printTemplate_out_ref");
        Params params = new Params();
        params.put("action","add");
        params.put("corgid",SessionHelper.getCurrentAdminOrgnId());
        params.put("csysname","电子档案");
        List<String> templateIdList = new ArrayList<>();
        templateIdList.add(dataMap.get("ctemplatepdfid").toString());
        try {
            templateIdList.forEach(templateId ->{
                params.put("cprinttemplateid",templateId);
                message.publish(params);
            });
        }catch (Exception e){
            throw new BusinessException("打印模板与平台建立关系出错！");
        }
    }

    /*删除平台的模版配置关系*/
    private void deleteTemplateRelation(List<Map> templateIdList){
        String currentAdminOrgnId = SessionHelper.getCurrentAdminOrgnId();
        Message message = new Message("aps_printTemplate_out_ref");
        Params params = new Params();
        params.put("action","delete");
        params.put("corgid",currentAdminOrgnId);
        params.put("csysname","电子档案");
        try {
            templateIdList.forEach(templateIdMap ->{
                String ctemplatepdfid = templateIdMap.get("ctemplatepdfid").toString();
                params.put("cprinttemplateid",ctemplatepdfid);
                message.publish(params);
            });
        }catch (Exception e){
            throw new BusinessException("删除打印模板与平台建立关系出错！");
        }
    }

}
