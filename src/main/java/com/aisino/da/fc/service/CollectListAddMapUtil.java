package com.aisino.da.fc.service;

import com.aisino.aosplus.core.ioc.annotation.Inject;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.aosplus.core.util.Guid;
import com.aisino.aosplus.session.SessionHelper;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import com.aisino.da.fc.dao.filecollect.CollectDataDaoInterface;
import com.aisino.da.fc.service.collect.EndUploadAppendixUpdateStatusServiceInterface;
import com.aisino.da.fc.util.FileClassCode;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * CollectListAddMapUtil
 * 每行添加map
 * <AUTHOR>
 * @version 1.0, 2023/3/7
 * @see [相关类/方法]
 * @since [产品/模块版本]
 */
public class CollectListAddMapUtil{
    @Inject
    public ApsContextDb db;
    @Inject
    public CollectDataDaoInterface collectDataDaoInterface;
    @Inject
    private EndUploadAppendixUpdateStatusServiceInterface endUploadAppendixUpdateStatusService;
    public Object doMs(Params params,String code) {
        Map result = params.getMap("result");
        String cmatchreceiptstatus = params.getString("cmatchreceiptstatus");//匹配回单状态
        String cflowstatus = params.getString("cflowstatus");//匹配流水状态
        for (Object key : result.keySet()) {
            if(!"Page".equalsIgnoreCase(key.toString())){
                Object o = result.get(key);
                if(o instanceof List){
                    List<Map> custodyYearNameList = queryDetailClassByMainCode(code);
                    List<Map> list = (List<Map>) o;
                    Iterator<Map> iterator = list.iterator();
                    while(iterator.hasNext()) {
                        Map row = iterator.next();
                        /*Boolean isLoad = true;//是否加载标识
                        //按“匹配回单状态”（凭证存在）处理数据  未匹配 已匹配时候+科目 包含 银行存款。
                        if(("0".equals(cmatchreceiptstatus)||"1".equals(cmatchreceiptstatus))
                        ||("0".equals(cflowstatus)||"1".equals(cflowstatus))){//查询条件存在
                            String cguid = CollectionUtil.getStringFromMap(row, "cguid");
                            //未匹配 已匹配时候+科目 包含 银行存款
                            List<Map> voucherMetadataDetail = collectDataDaoInterface.getVoucherMetadataDetail(cguid);
                            if(CollectionUtil.isNotEmpty(voucherMetadataDetail)){
                                for (Map voucherMetadataDetailMap : voucherMetadataDetail) {
                                    String cmatchreceiptstatusvalue = CollectionUtil.getStringFromMap(voucherMetadataDetailMap, "cmatchreceiptstatus");
                                    String cflowstatusvalue = CollectionUtil.getStringFromMap(voucherMetadataDetailMap, "cflowstatus");
                                    Boolean aBoolean1 = statusVal(cmatchreceiptstatus, cmatchreceiptstatusvalue);
                                    Boolean aBoolean2 = statusVal(cflowstatus, cflowstatusvalue);
                                    if(!aBoolean1||!aBoolean2){//不符合查询条件的凭证，不显示
                                        isLoad = false;
                                        break;
                                    }
                                }
                            }
                        }
                        if(!isLoad){//不符合查询条件的，不显示
                            iterator.remove();
                            continue;
                        }*/
                        setDisabledStatus(row,code);
                        setCustodyYearName(row,custodyYearNameList);
                        String cguid = CollectionUtil.getStringFromMap(row, "cguid");
                        if(FileClassCode.voucherRoot.equals(code)){
                            //endUploadAppendixUpdateStatusService.updateInfo(cguid,"da_fc_voucherinfo");
                        }else if(FileClassCode.book.equals(code)){
                            endUploadAppendixUpdateStatusService.updateInfo(cguid,"da_fc_accountbook");
                        }else if(FileClassCode.report.equals(code)){
                            endUploadAppendixUpdateStatusService.updateInfo(cguid,"da_fc_accountreport");
                        }else if(FileClassCode.other.equals(code)){
                            endUploadAppendixUpdateStatusService.updateInfo(cguid,"da_fc_otheraccountreport");
                        }
                        if("QT".equals(code)){//其它会计资料,收集人默认为当前用户
                            /*row.put("ccollectorid_name",SessionHelper.getCurrentUserRealName());
                            row.put("ccollectorid",SessionHelper.getCurrentUserId());*/
                        }
                    }
                }
            }
        }
        return null;
    }
    private Boolean statusVal(String status,String value){
        if("0".equals(status)||"1".equals(status)){
            Boolean flag = false;
            if("1".equals(status)&&"1".equals(value)){
                flag = true;
            }
            //按未匹配查询，并且不是匹配状态
            if("0".equals(status)&&!"1".equals(value)){
                flag = true;
            }
            //按匹配查询，并且是匹配状态
            return flag;
        }
        return true;
    }
    /**
     *〈一句话功能简述〉某行根据已归档或已组卷，置灰
     *〈功能详细描述〉
     * <AUTHOR>
     * @version 1.0, 2023/3/7
     * @param row
     * @return void
     * @exception/throws [违例类型] [违例说明]
     * @see [类、类#方法、类#成员]
     * @since 1.0
     */
    public void setDisabledStatus(Map row,String code){
        Object ieastatus = row.get("ieastatus");//已归档 5
        Object centityfilestatus = row.get("centityfilestatus");//已组卷3；
        String ifileborrowstatus = CollectionUtil.getStringFromMap(row, "ifileborrowstatus");
        String cintegritystatus = CollectionUtil.getStringFromMap(row, "cintegritystatus");
        if(ieastatus==null&&centityfilestatus==null&&StringUtils.isBlank(ifileborrowstatus)
                &&StringUtils.isBlank(cintegritystatus)){//其他会计资料为空
            row.put("enablecheckbox",1);
            row.put("enableuploadfile",1);
            row.put("enabledelete",1);
        }else {
            //Integer eastatus = Integer.valueOf(ieastatus.toString());
            //Integer entityfilestatus = Integer.valueOf(centityfilestatus.toString());
            if(!"0".equals(String.valueOf(ieastatus))||!"0".equals(String.valueOf(centityfilestatus))
                    ||StringUtils.isNotBlank(ifileborrowstatus)||"6".equals(cintegritystatus)){
                row.put("enableuploadfile",0);
                row.put("enabledelete",0);
                /*if(!FileClassCode.voucherRoot.equals(code)){
                    row.put("enablecheckbox",0);
                }else {//凭证不置灰勾选框
                }*/
                row.put("enablecheckbox",0);
            }else {
                row.put("enablecheckbox",1);
                row.put("enableuploadfile",1);
                row.put("enabledelete",1);
            }
        }
    }
    /**
     *〈一句话功能简述〉动态赋予某行保管年限
     *〈功能详细描述〉
     * <AUTHOR>
     * @version 1.0, 2023/3/7
     * @param row 某行数据
     * @param custodyYearNameList 档案分类预制数据
     * @return void
     * @exception/throws [违例类型] [违例说明]
     * @see [类、类#方法、类#成员]
     * @since 1.0
     * @deprecated
     */
    public void setCustodyYearName(Map row, List<Map> custodyYearNameList){
        for (Map map : custodyYearNameList) {
            //Object ccode = map.get("ccode");//档案分类便编码
            String cname = map.get("cname").toString();//档案分类名称
            if(cname.equals(row.get("cdetailclassname"))){
                Object ccustody_year_name = map.get("ccustody_year_name");//保管年限
                row.put("ccustody_year_name",ccustody_year_name);
                break;
            }
        }
    }
    public List<Map> queryDetailClassByMainCode(String code){
        Params par = new Params();
        par.put("ccode",code);
        return db.queryMapListById("da_fc_common_sql.queryDetailClassByMainCode",par);
    }
    /**
     *〈一句话功能简述〉 补充插入数据固定参数
     *〈功能详细描述〉 补充页面建模创建实体的一些固定值
     * <AUTHOR>
     * @version 1.0, 2023/2/23
     * @param map
     * @return java.util.Map
     * @exception/throws [违例类型] [违例说明]
     * @see [类、类#方法、类#成员]
     * @since 1.0
     */
    public Map makeDefalultValue(Map map,String selectOrgnid){
        map.put("cguid", Guid.g());
        String corgnid = SessionHelper.getCurrentOrgnId();
        String corgnid_name = SessionHelper.getCurrentOrgnName();
        String cadminorgnid = SessionHelper.getCurrentAdminOrgnId();
        String cadminorgnid_name = SessionHelper.getCurrentAdminOrgnName();
        String ccreatedate = getSysTime();
        String ccreatorid = SessionHelper.getCurrentUserId();
        String ccreatorid_name = SessionHelper.getCurrentUserRealName();
        if(!corgnid.equals(selectOrgnid)){
            Map oneMap = db.queryMap("select cname from aos_orgn where cguid='" + selectOrgnid + "'");
            corgnid_name = oneMap.get("cname").toString();
            corgnid = selectOrgnid;
        }
        map.put("corgnid", corgnid);
        map.put("csourceorganid", corgnid);
        map.put("csourceorganname", corgnid_name);
        map.put("corgnid_name", corgnid_name);
        map.put("cadminorgnid", cadminorgnid);
        map.put("cadminorgnid_name", cadminorgnid_name);
        map.put("ctimestamp", String.valueOf(System.currentTimeMillis()));
        map.put("ccreatedate",ccreatedate);
        map.put("ccreatorid", ccreatorid);
        map.put("ccreatorid_name",ccreatorid_name);
        return map;
    }
    /**
     *〈一句话功能简述〉组装一些参数
     *〈功能详细描述〉 接口返回的期间月、年组装会计期间等信息。期间月、年必填项
     * <AUTHOR>
     * @version 1.0, 2023/3/7
     * @param map
     * @param cpageid 页面建模打开浏览界面必须参数：浏览界面的页面id
     * @param ctemplateid 页面建模打开浏览界面必须参数：浏览界面的模板id
     * @return java.util.Map
     * @exception/throws [违例类型] [违例说明]
     * @see [类、类#方法、类#成员]
     * @since 1.0
     * @deprecated
     */
    public Map makeSomeValue(Map map,String cpageid,String ctemplateid){
        map.put("cpageid",cpageid);
        map.put("ctemplateid",ctemplateid);
        String corgnid = SessionHelper.getCurrentOrgnId();
        String corgnid_name = SessionHelper.getCurrentOrgnName();
        String ccreatorid = SessionHelper.getCurrentUserId();
        //String ccreatorid_name = SessionHelper.getCurrentUserName();
        String UserRealName = SessionHelper.getCurrentUserRealName();
        map.put("ccollecttime", getSysTime());//收集时间（精确到秒）
        map.put("ccollectdate", getSysDate());//收集日期 (查询条件 年月日)
        Object cmonth = map.get("cmonth");
        if(cmonth.toString().length()==1){//1-9
            map.put("cperioddate", map.get("cyear")+"-0"+cmonth);//会计期间（查询2023-01）
            map.put("cperioddatelist", map.get("cyear")+"0"+cmonth);//会计期间(列表202301)
        }else {//01 12
            map.put("cperioddate", map.get("cyear")+"-"+cmonth);//会计期间（查询2023-12）
            map.put("cperioddatelist", map.get("cyear")+""+cmonth);//会计期间(列表202312)
        }
        map.put("ceastatus", "收集成功");
        map.put("ieastatus", "0");
        map.put("centityfilestatus_name", "收集成功");
        map.put("centityfilestatus","0");
        map.put("csuaistatus", "未检测");
        map.put("isuaistatus", 0);
        map.put("cmanualrelstatus", "未关联");
        map.put("imanualrelstatus", 0);
        //map.put("csourceorganid", corgnid);
        //map.put("csourceorganname", corgnid_name);
        map.put("ccollectorid", ccreatorid);
        map.put("ccollectorid_name",UserRealName);
        return map;
    }
    /**
     *〈一句话功能简述〉获取当前日期yyyy-MM-dd
     *〈功能详细描述〉
     * <AUTHOR>
     * @version 1.0, 2023/2/23
     * @return java.lang.String
     * @exception/throws [违例类型] [违例说明]
     * @see [类、类#方法、类#成员]
     * @since 1.0
     */
    public String  getSysDate(){
        Date date=new Date();
        SimpleDateFormat s=new SimpleDateFormat("yyyy-MM-dd");
        String time =s.format(date);
        return time;
    }
    public String  getSysYearMonth(){
        Date date=new Date();
        SimpleDateFormat s=new SimpleDateFormat("yyyy-MM");
        String time =s.format(date);
        return time;
    }
    /**
     *〈一句话功能简述〉获取当前日期yyyy-MM-dd HH:mm:ss
     *〈功能详细描述〉
     * <AUTHOR>
     * @version 1.0, 2023/2/27
     * @return java.lang.String
     * @exception/throws [违例类型] [违例说明]
     * @see [类、类#方法、类#成员]
     * @since 1.0
     * @deprecated
     */
    private String  getSysTime(){
        Date date=new Date();
        SimpleDateFormat s=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String time =s.format(date);
        return time;
    }
}
