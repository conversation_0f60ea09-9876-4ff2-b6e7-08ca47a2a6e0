package com.aisino.da.fc.service.layoutGeneration.impl;

import com.aisino.aosplus.core.ConfigHelper;
import com.aisino.aosplus.core.Constants;
import com.aisino.aosplus.core.common.fault.BusinessException;
import com.aisino.aosplus.core.dao.DbHelper;
import com.aisino.aosplus.core.mvc.DataContext;
import com.aisino.aosplus.core.mvc.annotation.Service;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.core.util.Guid;
import com.aisino.aosplus.core.util.StringUtil;
import com.aisino.aosplus.session.AcsHelper;
import com.aisino.aosplus.session.SessionHelper;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import com.aisino.aps.common.exception.ApsException;
import com.aisino.da.common.util.ArrayUtil;
import com.aisino.da.fc.service.layoutGeneration.LayoutGenerationService;
import com.aisino.da.fc.util.ESConfigUtil;
import com.aisino.da.fc.util.ESUtil;
import com.aisino.da.fc.util.PrintPdfUtil;
import com.aisino.da.fc.util.bankreceipts.ocrbank.CollectionStringUtil;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class LayoutGenerationServiceImpl implements LayoutGenerationService {

    PrintPdfUtil printUtil = new PrintPdfUtil();

    /*@Inject
    private ApsContextDb db;*/

    private static Log logger = LogFactory.getLog(LayoutGenerationServiceImpl.class);

    public Map addFile(String fileTable,Map dataMap,ApsContextDb db,Map returnfileMap,Params params){
        Map reMap = new HashMap();
        String ctype = dataMap.get("ctype").toString();
        String clistguid = dataMap.get("列表主键").toString();
        List<Map> fileData = db.queryMapList("select * from " +fileTable+" where dafilepath like '%format%' and  clistguid = ?",clistguid);
        if(!fileData.isEmpty()){
            try {
                //ApsContextDb dbsvr = new ApsContextDb();
                Map map = new HashMap<>();
                map.put("cguid", fileData.get(0).get("cguid"));
                //dbsvr.deleteT(fileTable, map, "cguid");
                //调用ES删除
                List<String> listGuid = new ArrayList<>();
                listGuid.add(clistguid);
                ESUtil.deleteSeDetial(listGuid, "PZ");
                db.deleteT(fileTable, map, "cguid");
            }catch (Exception e){
                throw new BusinessException("更新文件表中版式文件数据失败！");
            }
        }

        List<Map> fileData2 = db.queryMapList("select * from " +fileTable+" where dafilepath like '%format%' and  clistguid = ?",clistguid);

        if(fileData2.isEmpty()){

            Map fieldMap = params.getFieldMap();
            String isAuto = fieldMap.get("isAuto") == null ? "" : fieldMap.get("isAuto").toString();
            //String corgnid = "";
            String cuserid = "";
            if("".equals(isAuto)){
                //corgnid = SessionHelper.getCurrentOrgnId();
                cuserid = SessionHelper.getCurrentUserId();
            } else if ("0".equals(isAuto)) {
                //Map fileMap = params.getFieldMap();
                //corgnid = fileMap.get("corgnid").toString();
                /*定时任务生成版式失败时创建人为超级管理员*/
                cuserid = "1";
            }
            try {
                Map insData = new HashMap();
                String cguid = Guid.g();
                insData.put("cguid", cguid);
                Map<String, String> props = ConfigHelper.getTagProps("aps-oss");
                String dxccname_da = ArrayUtil.getStringFromMap(props, "bucketName");
                insData.put("storagemethod", "default");
                insData.put("dxccname", dxccname_da);
                insData.put("cbusinesstype", ctype);
                insData.put("clistguid", clistguid);
                if("PZ".equals(ctype)){
                    insData.put("cvoucherid", dataMap.get("凭证id"));
                    insData.put("datainterval", dataMap.get("凭证日期").toString().substring(0,7));
                }else{
                    insData.put("datainterval", dataMap.get("会计期间"));
                }
                insData.put("cfilessize", returnfileMap.get("cfilessize"));
                Map fileNameMap = this.getFileNameAndPath(returnfileMap);
                insData.put("filename", fileNameMap.get("filename"));
                insData.put("cfilerealname", fileNameMap.get("cfilerealname"));
                insData.put("cfiletype", "2");
                insData.put("cfilesourcetype", "9");
                insData.put("syssource", "aps-oss");
                insData.put("dafilepath", fileNameMap.get("dafilepath"));
                insData.put("filepath", fileNameMap.get("dafilepath"));
                String ccreatedate = CollectionStringUtil.getSysDateTime();
                insData.put("ccreatedate", ccreatedate);
                Long time = System.currentTimeMillis();
                insData.put("cTimeStamp", String.valueOf(time));
                insData.put("cstatus", returnfileMap.get("status"));
                insData.put("cCreatorId", cuserid);
                insData.put("cfiles_table_name", fileTable);
                insData.put("corgnid", SessionHelper.getCurrentOrgnId());
                insData.put("corgnid_name", SessionHelper.getCurrentOrgnName());
                db.insert(fileTable, insData);
                //调用ES
                Map daData = db.getOneRecord("select 'PZ' caccountclasscode,'会计凭证' caccountclassname,cdetailclasscode,cdetailclassguid_name cdetailclassname,cperioddate  from da_fc_voucherinfo where cguid = ?", clistguid);
                daData.put("cguid",cguid);
                daData.put("corgnid",SessionHelper.getCurrentOrgnId());
                daData.put("corgnid_name",SessionHelper.getCurrentOrgnName());
                daData.put("cfilerealname",fileNameMap.get("cfilerealname"));
                daData.put("cfiles_table_name",fileTable);
                daData.put("dafilepath",fileNameMap.get("dafilepath"));
                daData.put("filename",fileNameMap.get("filename"));
                List<Map> filelist = new ArrayList<>();
                filelist.add(daData);
                if(ESConfigUtil.checkConfig()){
                    ESUtil esUtil = new ESUtil();
                    esUtil.addSeDetial(filelist, db.getDbID(),"PZ");
                }
                reMap.put("ccode","0000");
            } catch (Exception e){
                reMap.put("ccode","9999");
                reMap.put("msg","系统异常");
                reMap.put("exc",e.getMessage());
            }
        }
        if(reMap.isEmpty()){
            reMap.put("ccode","0000");
        }
        return reMap;
    }

    public Map addPdfLog(Map m,String bussinessid,String message,String exc,String filename,String ope,String cdetailclasscode,String clistguid,Params params222,ApsContextDb db){
        Map returnMap = new HashMap();
        Map fileMap = params222.getFieldMap();
        String isAuto = params222.get("isAuto") == null ? "" : params222.get("isAuto").toString();
        String corgnid = "";
        String cuserid = "";
        if("".equals(isAuto)){
            corgnid = SessionHelper.getCurrentOrgnId();
            cuserid = SessionHelper.getCurrentUserId();
        } else if ("0".equals(isAuto)) {

            corgnid = fileMap.get("corgnid").toString();
            /*定时任务生成版式失败时创建人为超级管理员*/
            cuserid = "1";
        }
        try {
            if ("add".equals(ope)) {
                /*操作时间*/
                Date date = new Date();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String coperatedate = sdf.format(date);

                /*失败原因*/
                String cfailreason = message;
                Map cfailMap = new HashMap();
                cfailMap.put("coperatedate", coperatedate);
                cfailMap.put("cdataname", filename);
                if(exc.isEmpty()){
                    cfailMap.put("cfailreason", cfailreason);
                }else{
                    cfailMap.put("cfailreason", "系统异常");
                }
                cfailMap.put("csyserror", cfailreason);
                cfailMap.put("bussinessid", bussinessid);
                cfailMap.put("clistguid", clistguid);
                cfailMap.put("corgnid", corgnid);
                cfailMap.put("cfiletype", cdetailclasscode);
                cfailMap.put("cstatus", "0");
                cfailMap.put("cguid", Guid.g());
                String ccreatedate = CollectionStringUtil.getSysDateTime();
                cfailMap.put("ccreatedate", ccreatedate);
                Long time = System.currentTimeMillis();
                cfailMap.put("ctimestamp", String.valueOf(time));
                cfailMap.put("ccreatorid", cuserid);
                db.insert("da_fc_gen_pdf_log", cfailMap);
                returnMap.put("result", "0");
                returnMap.put("msg", "版式生成日志插入成功");
            } else if ("update".equals(ope)) {
                /*操作时间*/
                Date date = new Date();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String coperatedate = sdf.format(date);
                /*失败原因*/
                String cfailreason = message;

                Params params = new Params();
                params.put("bussinessid", bussinessid);
                params.put("cfailreason", cfailreason);
                params.put("csyserror", cfailreason);
                params.put("coperatedate", coperatedate);
                params.put("cdataname", filename);
                //db.update("update da_fc_gen_pdf_log set cfailreason = '"+cfailreason+"',csyserror = '"+cfailreason+"',coperatedate = '"+coperatedate+"',cdataname = '"+filename+"' where bussinessid = ?",params);
                db.updateById("da_fc_gen_pdf_sql.updategenpdflog",params);
                returnMap.put("result", "0");
                returnMap.put("msg", "版式生成日志插入成功");
            }
        }catch (Exception e){
            throw new RuntimeException(e.getMessage()+"版式生成日志插入失败");
        }
        return returnMap;
    }

    @Override
    public Map genVoucherPDF(Params params) {

        String dsId = AcsHelper.getLoginUserMap().get(Constants.SESSION_DSID).toString();
        if (!StringUtil.isEmpty(dsId)) {
            if (DataContext.getInstance() == null) {
                DataContext.init(null, null);
            }
            DbHelper.setCurrentDsID(dsId);
        }
        ApsContextDb db = new ApsContextDb(dsId);


        logger.error("##########入参:"+params);

        List<Map> returnList = new ArrayList<>();
        String message = "";
        String cdetailclasscode = params.getString("cdetailclasscode");
        List<String> cguidList = (List<String>)params.get("cvouguid");
        logger.error("##########凭证列表id:"+cguidList);
        String isAuto = params.getString("isAuto");
        String cadminorgnId = "";
        String sessionCorgnid = "";
        if(isAuto==null){
            cadminorgnId =SessionHelper.getCurrentAdminOrgnId();
            sessionCorgnid = SessionHelper.getCurrentOrgnId();
        }

        List<Map> voucherList = new ArrayList<>();//需要生成版式文件的数据
        /*获取凭证需要打印的数据*/
        logger.error("##########cguidList"+cguidList);
        if(cguidList!=null){
            for (int i = 0; i < cguidList.size(); i++) {
                Params params2 = new Params();
                params2.put("clistguid",cguidList.get(i));
                List<Map> listvoucherMap = db.queryMapListById("da_fc_template_sql.getvoucherinfodata",params2.getFieldMap().get("clistguid"));
                if(!listvoucherMap.isEmpty()){
                    String ieastatus = listvoucherMap.get(0).get("ieastatus").toString();
                    String centityfilestatus = listvoucherMap.get(0).get("centityfilestatus").toString();
                    String cvoucode = listvoucherMap.get(0).get("凭证字号").toString();
                    if("0".equals(ieastatus)&&"0".equals(centityfilestatus)){
                        List<Map> voucherMap = db.queryMapListById("da_fc_template_sql.getvoucherdata",params2.getFieldMap().get("clistguid"));
                        voucherMap.get(0).put("cdetailclassguid",listvoucherMap.get(0).get("cdetailclassguid"));
                        voucherMap.get(0).put("cvoucode",cvoucode);
                        voucherMap.get(0).put("会计期间",listvoucherMap.get(0).get("会计期间"));
                        voucherMap.get(0).put("cgenpdfstatus",listvoucherMap.get(0).get("cgenpdfstatus"));
                        voucherList.addAll(voucherMap);
                    } else if (i == cguidList.size() -1&&voucherList.isEmpty()) {
                        message = "归档组卷后不能生成版式文件";
                    }
                    /*完整性状态 人工核对校验*/
                    String cintegritystatus = listvoucherMap.get(0).get("cintegritystatus") == null ? "" : listvoucherMap.get(0).get("cintegritystatus").toString();
                    if("6".equals(cintegritystatus)){
                        message = message + "凭证[" + cvoucode + "]人工核对完整不允许生成版式文件！";
                    }
                }
            }
            //循环打印文件数据
            if(voucherList.size()>0){
                String templcguid = "";
                /*取用模板后 进行模拟打印，生成版式文件*/
                for(Map m : voucherList) {
                    String detailclassguid = m.get("cdetailclassguid").toString();
                    String cgenpdfstatus =  m.get("cgenpdfstatus") == null ? "0" : m.get("cgenpdfstatus").toString();
                    String cvoucode = m.get("cvoucode").toString();
                    String year = m.get("会计期间").toString();
                    String cvoucherdate = m.get("凭证日期").toString();
                    Params params1 = new Params();
                    /*获取模板id  用户被指定多个模板时，优先取用非全局适用模板*/
                    params1.put("carchivalid",detailclassguid);
                    params1.put("cAdminOrgnId",cadminorgnId);
                    params1.put("capplyorgnid",sessionCorgnid);
                    List<Map> tempList = db.queryMapListById("da_fc_template_sql.getctemplatepdfid",params1);
                    logger.error("##########模板数据"+tempList);
                    if(tempList.size() > 1){
                        for (int j = 0; j < tempList.size(); j++) {
                            String callin = tempList.get(j).get("callin").toString();
                            if("0".equals(callin)){
                                templcguid = tempList.get(j).get("ctemplatepdfid").toString();
                            }
                        }
                    } else if (tempList.size() == 1) {
                        templcguid = tempList.get(0).get("ctemplatepdfid") == null ? "" : tempList.get(0).get("ctemplatepdfid").toString();
                    }
                    /*主表元数据获取到模板id后 删除明细分类id*/
                    m.remove("detailclassguid");
                    m.remove("cgenpdfstatus");



                    if(!templcguid.isEmpty()&&"".equals(message)){
                        String filename = "";

                        try {
                            Params params2 = new Params();
                            params2.put("cvoucherid", m.get("凭证id"));
                            Map voucherMap = db.queryMap("select cdetailclassguid,concat(isexistfile,'') isexistfile from da_fc_voucherinfo where cvoucherid = ?",params2.getFieldMap().get("cvoucherid"));
                            int isexistfile = Integer.parseInt(voucherMap.get("isexistfile") == null ? "0" : voucherMap.get("isexistfile").toString());
                            /*校验版式生成设置是否为启用*/
                            String cdetailclassguid = voucherMap.get("cdetailclassguid").toString();
                            Params params3 = new Params();
                            params3.put("cdetailclassguid",cdetailclassguid);
                            Map cgenfile =  db.queryMap("select c.cgenfile from da_fc_gen_pdf c where c.cadminorgnId = '"+cadminorgnId+"'and c.carchivalid = ?",params3.getFieldMap().get("cdetailclassguid"));
                            String cgenfilestr = cgenfile.get("cgenfile") == null ? "":cgenfile.get("cgenfile").toString();
                            if("0".equals(cgenfilestr)|| StringUtil.isEmpty(cgenfilestr)){
                                message = "未启用生成版式文件";
                            }
                            if("".equals(message)){
                            logger.error("**********调用生成版式文件工具类*****参数为:"+params);
                            logger.error("==========版式生成前dsid=========="+dsId);
                            Map returnfileMap = printUtil.printPdf(templcguid, m, cdetailclasscode,params,db);
                            logger.error("==========版式生成业务类dsid=========="+dsId);
                            if (returnfileMap.get("pathName") != null) {
                                //如果有报错，文件名称记录到日志表
                                filename = returnfileMap.get("fileName").toString();
                                /*获取文件表的表名 用于插入模拟打印文件信息到文件表*/
                                String daFileTableName = "da_files_" + cvoucherdate.substring(0, 4);
                                m.put("pdfUrl", returnfileMap.get("pathName"));
                                m.put("ctype", "PZ");
                                /*调用插入文件表方法，插入文件表版式文件数据*/
                                Map fileMap = addFile(daFileTableName, m, db,returnfileMap,params);
                                /*文件生成成功，更新收集表版式生成状态为“已完成”  凭证列表的表文件总数加1，版式生成状态为已生成*/
                                if ("0000".equals(fileMap.get("ccode").toString())) {

                                    String cvoucherid =  m.get("凭证id").toString();

                                    Params params4 = new Params();
                                    params4.put("cvoucherid",cvoucherid);
                                    if(message.isEmpty() && 0==isexistfile){
                                        //db.update("update da_fc_voucherinfo set cgenpdfstatus = '1' and cnumberoffiles = 1 where cvoucherid = ?", cvoucherid);
                                        //db.update("update da_fc_md_voucher set cgenpdfstatus = '1' where cvoucherid = ?", cvoucherid);
                                        params4.put("cgenpdfstatus","1");
                                        params4.put("cnumberoffiles","1");
                                        params4.put("isexistfile","1");
                                        db.updateById("da_fc_gen_pdf_sql.updateVoucherinfo",params4);
                                        Params params5 = new Params();
                                        params5.put("cgenpdfstatus","1");
                                        params5.put("cvoucherid",cvoucherid);
                                        db.updateById("da_fc_gen_pdf_sql.updateMdVoucher",params5);
                                    }else if(message.isEmpty() && 1==isexistfile){
                                        Map reMap = db.queryMap("select cnumberoffiles from da_fc_voucherinfo where cvoucherid = ?",params4.getString("cvoucherid"));
                                        //db.update("update da_fc_voucherinfo set cgenpdfstatus = '1' and cnumberoffiles = cnumberoffiles + 1 where cvoucherid = ?", cvoucherid);
                                        //db.update("update da_fc_md_voucher set cgenpdfstatus = '1' where cvoucherid = ?", cvoucherid);
                                        String cnumberoffiles = "";
                                        if("1".equals(cgenpdfstatus)){
                                            cnumberoffiles = String.valueOf(Integer.parseInt(reMap.get("cnumberoffiles").toString()));
                                        }else{
                                            cnumberoffiles = String.valueOf(reMap.get("cnumberoffiles") == null ? 1 :Integer.parseInt(reMap.get("cnumberoffiles").toString())+1);
                                        }

                                        params4.put("cgenpdfstatus","1");
                                        params4.put("cnumberoffiles",cnumberoffiles);
                                        params4.put("isexistfile","1");
                                        db.updateById("da_fc_gen_pdf_sql.updateVoucherinfo",params4);
                                        Params params5 = new Params();
                                        params5.put("cgenpdfstatus","1");
                                        params5.put("cvoucherid",cvoucherid);
                                        db.updateById("da_fc_gen_pdf_sql.updateMdVoucher",params5);
                                    } else if (StringUtil.isEmpty(isexistfile)) {
                                        message = "未查询到列表数据，无法判断是否存在文件";
                                    }
                                } else {
                                    /*文件生成失败，失败信息插入版式生成日志*/
                                    /*失败原因*/
                                    String cfailreason = fileMap.get("exc").toString();
                                    /*资料名称*/
                                    filename = cvoucherdate.substring(0,7) + "_" + m.get("cvoucode").toString();
                                    /*凭证id*/
                                    String bussinessid = m.get("cvoucherid").toString();
                                    String clistguid = m.get("列表主键").toString();
                                    List<Map> resultLog = db.queryMapList("select * from da_fc_gen_pdf_log g where g.cfiletype = 'PZ01' and g.bussinessid ='"+bussinessid+"'");
                                    if(resultLog.isEmpty()){
                                        addPdfLog(m,bussinessid,cfailreason,"",filename,"add",cdetailclasscode,clistguid,params,db);
                                    }else{
                                        addPdfLog(m,bussinessid,cfailreason,"",filename,"update",cdetailclasscode,clistguid,params,db);
                                    }
                                }

                            }else{
                                /*文件生成失败，失败信息插入版式生成日志*/
                                message = returnfileMap.get("msg").toString();
                                filename = cvoucherdate.substring(0,7) + "_" + m.get("cvoucode").toString();
                                /*凭证id*/
                                String bussinessid = m.get("凭证id").toString();
                                String clistguid = m.get("列表主键").toString();
                                List<Map> resultLog = db.queryMapList("select * from da_fc_gen_pdf_log g where g.cfiletype = 'PZ01' and g.bussinessid ='"+bussinessid+"'");
                                if(resultLog.isEmpty()){
                                    addPdfLog(m,bussinessid,message,"",filename,"add",cdetailclasscode,clistguid,params,db);
                                }else{
                                    addPdfLog(m,bussinessid,message,"",filename,"update",cdetailclasscode,clistguid,params,db);
                                }
                            }
                            if ("1".equals(returnfileMap.get("status").toString())) {
                                returnList.add(returnfileMap);
                            }
                            }
                        } catch (Exception e) {
                            filename = cvoucherdate.substring(0,7) + "_" + m.get("cvoucode").toString();
                            /*文件生成失败，失败信息插入版式生成日志*/
                            String bussinessid = m.get("凭证id").toString();
                            String clistguid = m.get("列表主键").toString();
                            /*凭证id*/
                            List<Map> resultLog = db.queryMapList("select * from da_fc_gen_pdf_log g where g.cfiletype = 'PZ01' and g.bussinessid ='"+bussinessid+"'");
                            if(resultLog.isEmpty()){
                                addPdfLog(m,bussinessid,e.getMessage(),"exc",filename,"add",cdetailclasscode,clistguid,params,db);
                            }else{
                                addPdfLog(m,bussinessid,e.getMessage(),"exc",filename,"update",cdetailclasscode,clistguid,params,db);
                            }
                        }
                    }else if(templcguid.isEmpty()){
                        message = message + "未获取到任何模板，请检查当前组织是否设置模板;";
                    }
                    if(!message.isEmpty()){
                        String filename = cvoucherdate.substring(0,7) + "_" + m.get("cvoucode").toString();
                        /*凭证id*/
                        String bussinessid = m.get("凭证id").toString();
                        String clistguid = m.get("列表主键").toString();
                        List<Map> resultLog = db.queryMapList("select * from da_fc_gen_pdf_log g where g.cfiletype = 'PZ01' and g.bussinessid ='"+bussinessid+"'");
                        if(resultLog.isEmpty()){
                            addPdfLog(m,bussinessid,message,"",filename,"add",cdetailclasscode,clistguid,params,db);
                        }else{
                            addPdfLog(m,bussinessid,message,"",filename,"update",cdetailclasscode,clistguid,params,db);
                        }
                    }
                    db.getDbService().commit();
                }
            }
        }else{
            message = "请选择需要生成版式文件的数据";
        }
        logger.error("##########跑完了"+cguidList);
        Map reMap = new HashMap();
        if(!"".equals(message)){
            reMap.put("status","0");
            reMap.put("message",message);
        }else if("".equals(message)&&voucherList.size() == returnList.size()){
            reMap.put("status","1");
            reMap.put("message","版式文件生成成功,共生成"+returnList.size()+"条；");
        } else if ("".equals(message)&&voucherList.size() != returnList.size()) {
            int success = returnList.size();
            int fail = voucherList.size() - returnList.size();
            reMap.put("status","2");
            reMap.put("message","版式文件生成成功"+success +"条；"+"失败"+fail+"条；");
        }
        return reMap;
    }

    @Override
    public Map genAcctLedgerDataPDF(Params params) throws IOException {

        String dsId = AcsHelper.getLoginUserMap().get(Constants.SESSION_DSID).toString();
        if (!StringUtil.isEmpty(dsId)) {
            if (DataContext.getInstance() == null) {
                DataContext.init(null, null);
            }
            DbHelper.setCurrentDsID(dsId);
        }
        ApsContextDb db = new ApsContextDb(dsId);

        List<Map> returnList = new ArrayList<>();
        String message = "";
        String ctype = params.getString("cdetailclasscode");
        /*过滤明细分类编码，集合总账id*/
        List<String> cguidList = (List<String>)params.get("cZB01guid");

        String cadminorgnId = SessionHelper.getCurrentAdminOrgnId();
        List<Map> zb01List = new ArrayList<>();
        List<Map> zbclassList = new ArrayList<>();
        /*获取总账需要打印的数据*/
        if(cguidList!=null){
            for (int i = 0; i < cguidList.size(); i++) {
                Params params2 = new Params();
                params2.put("clistguid",cguidList.get(i));
                List<Map> listacctMap = db.queryMapListById("da_fc_template_sql.getacctinfodata",params2.getFieldMap().get("clistguid"));
                if(!listacctMap.isEmpty()) {
                    String cgenpdfstatus = listacctMap.get(0).get("cgenpdfstatus") == null ? "0":listacctMap.get(0).get("cgenpdfstatus").toString();
                    String ieastatus = listacctMap.get(0).get("ieastatus") == null ? "":listacctMap.get(0).get("ieastatus").toString();
                    String centityfilestatus = listacctMap.get(0).get("centityfilestatus") == null ? "":listacctMap.get(0).get("centityfilestatus").toString();
                    if(("0".equals(cgenpdfstatus))||("1".equals(cgenpdfstatus)&&"0".equals(ieastatus)&&"0".equals(centityfilestatus))){
                        List<Map> acctLedgerMap = db.queryMapListById("da_fc_template_sql.getacctledgerdata",params2.getFieldMap().get("clistguid"));
                        zb01List.addAll(acctLedgerMap);
                        zbclassList.addAll(listacctMap);
                    } else if (i == cguidList.size()-1&&zb01List.isEmpty()) {
                        message = "没有可生成的数据";
                    }
                }

            }
            //循环打印文件数据
            if(zb01List.size()>0) {
                for (int i = 0; i < zbclassList.size(); i++) {
                    String detailclassguid = zbclassList.get(i).get("cdetailclassguid").toString();
                    Params params1 = new Params();
                    /*获取模板id  用户被指定多个模板时，优先取用非全局适用模板*/
                    params1.put("carchivalid",detailclassguid);
                    params1.put("cAdminOrgnId",cadminorgnId);
                    List<Map> tempList = db.queryMapListById("da_fc_template_sql.getctemplatepdfid",params1);
                    String templcguid = "";
                    if(tempList.size() > 1){
                        for (int j = 0; j < tempList.size(); j++) {
                            String callin = tempList.get(j).get("callin").toString();
                            if("0".equals(callin)){
                                templcguid = tempList.get(j).get("ctemplatepdfid") == null ? "" : tempList.get(j).get("ctemplatepdfid").toString();
                            }
                        }
                    } else if (tempList.size() == 1) {
                        templcguid = tempList.get(0).get("ctemplatepdfid") == null ? "" : tempList.get(0).get("ctemplatepdfid").toString();
                    }
                    if(!templcguid.isEmpty()){
                        for (Map m : zbclassList) {
                            //获取当前登录组织、模板id查询对应的模板选择表的数据，再反向查询版式生成设置主表数据
                            Params params3 = new Params();
                            params3.put("ctemplatepdfid", templcguid);
                            /*2.若校验结果未启用打印校验通过的凭证*/
                            String filename = "";
                            Map returnfileMap = printUtil.printPdf(templcguid, zb01List, ctype,params,db);
                            if (returnfileMap.get("pathName") != null) {
                                //如果有报错，文件名称记录到日志表
                                filename = returnfileMap.get("fileName").toString();
                                /*获取文件表的表名 用于插入模拟打印文件信息到文件表*/
                                String daFileTableName = "da_files_" + m.get("年度").toString();
                                m.put("pdfUrl", returnfileMap.get("pathName"));
                                m.put("ctype", "ZB");
                                /*调用插入文件表方法，插入文件表版式文件数据*/
                                Map fileMap = addFile(daFileTableName, m, db, returnfileMap,params);
                                /*文件生成成功，更新收集表版式生成状态为“已完成”*/
                                if ("0000".equals(fileMap.get("ccode").toString())) {
                                    String cgenpdfstatus =  m.get("cgenpdfstatus") == null ? "0" : m.get("cgenpdfstatus").toString();
                                    Params params2 = new Params();
                                    params2.put("cguid", m.get("列表主键"));
                                    Map bookMap = db.queryMap("select ifnull(cast(isexistfile as char),0) isexistfile,cdetailclassguid from da_fc_accountbook where cguid = ?", params2.getFieldMap().get("cguid"));
                                    int isexistfile = Integer.parseInt(bookMap.get("isexistfile").toString());
                                    params3.clear();
                                    params3.put("cdetailclassguid", bookMap.get("cdetailclassguid"));
                                    Map cgenfile = db.queryMap("select c.cgenfile from da_fc_gen_pdf c where c.cadminorgnId = '" + cadminorgnId + "'and c.carchivalid = ?", params3.getFieldMap().get("cdetailclassguid"));
                                    String cgenfilestr = cgenfile.get("cgenfile") == null ? "":cgenfile.get("cgenfile").toString();
                                    if("0".equals(cgenfilestr)|| StringUtil.isEmpty(cgenfilestr)){
                                        message = "未启用生成版式文件;";
                                    }
                                    if ("".equals(message) && 0 == isexistfile) {
                                        Params params4 = new Params();
                                        params4.put("cgenpdfstatus","1");
                                        params4.put("clistguid",m.get("列表主键"));
                                        db.updateById("da_fc_gen_pdf_sql.updatemdglbook",params4);
                                        Params params5 = new Params();
                                        params5.put("isexistfile","1");
                                        params5.put("cnumberoffiles","1");
                                        params5.put("cgenpdfstatus","1");
                                        params5.put("cguid",m.get("列表主键"));
                                        db.updateById("da_fc_gen_pdf_sql.updateaccountbook", params5);
                                    } else if ("".equals(message) && 1 == isexistfile) {
                                        Map reMap = db.queryMap("select cnumberoffiles from da_fc_accountbook where cguid = ?",m.get("列表主键"));
                                        String cnumberoffiles = "";
                                        if("0".equals(cgenpdfstatus)){
                                            cnumberoffiles = String.valueOf(Integer.parseInt(reMap.get("cnumberoffiles").toString())+1);
                                        }else{
                                            cnumberoffiles = String.valueOf(reMap.get("cnumberoffiles") == null ? 1 :Integer.parseInt(reMap.get("cnumberoffiles").toString()));
                                        }                                        Params params4 = new Params();
                                        params4.put("cgenpdfstatus","1");
                                        params4.put("clistguid",m.get("列表主键"));
                                        db.updateById("da_fc_gen_pdf_sql.updatemdglbook",params4);
                                        Params params5 = new Params();
                                        params5.put("isexistfile","1");
                                        params5.put("cnumberoffiles",cnumberoffiles);
                                        params5.put("cgenpdfstatus","1");
                                        params5.put("cguid",m.get("列表主键"));
                                        db.updateById("da_fc_gen_pdf_sql.updateaccountbook", params5);
                                    }
                                } else {
                                    /*文件生成失败，失败信息插入版式生成日志*/
                                    /*操作时间*/
                                    Date date = new Date();
                                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
                                    String coperatedate = sdf.format(date);
                                    /*资料名称*/
                                    String dat = m.get("期间") == null ? "" : m.get("期间").toString();
                                    String dt = "";
                                    if (!dat.isEmpty()) {
                                        String[] arr = dat.split("-");
                                        for (int j = 0; j < arr.length; j++) {
                                            dt = dt + arr[j];
                                        }
                                    }

                                    filename = dt + "_" + "总账";
                                    /*失败原因*/
                                    String cfailreason = fileMap.get("exc").toString();
                                    /*总账元数据表主键*/
                                    String bussinessid = m.get("列表主键").toString();
                                    List<Map> resultLog = db.queryMapList("select * from da_fc_gen_pdf_log g where g.cfiletype = 'PZ01' and g.bussinessid ='"+bussinessid+"'");
                                    if(resultLog.isEmpty()){
                                        addPdfLog(m,bussinessid,cfailreason,"",filename,"add",ctype,bussinessid,params,db);
                                    }else{
                                        addPdfLog(m,bussinessid,cfailreason,"",filename,"update",ctype,bussinessid,params,db);
                                    }
                                }
                            } else {
                                /*文件生成失败，失败信息插入版式生成日志*/
                                /*操作时间*/
                                Date date = new Date();
                                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
                                String coperatedate = sdf.format(date);
                                /*资料名称*/
                                String dat = m.get("期间") == null ? "" : m.get("期间").toString();
                                String dt = "";
                                if (!dat.isEmpty()) {
                                    String[] arr = dat.split("-");
                                    for (int j = 0; j < arr.length; j++) {
                                        dt = dt + arr[j];
                                    }
                                }
                                filename = dt + "_" + "总账";
                                /*失败原因*/
                                String cfailreason = returnfileMap.get("msg").toString();

                                /*总账元数据表主键*/
                                String bussinessid = m.get("列表主键").toString();
                                List<Map> resultLog = db.queryMapList("select * from da_fc_gen_pdf_log g where g.cfiletype = 'PZ01' and g.bussinessid ='"+bussinessid+"'");
                                if(resultLog.isEmpty()){
                                    addPdfLog(m,bussinessid,cfailreason,"",filename,"add",ctype,bussinessid,params,db);
                                }else{
                                    addPdfLog(m,bussinessid,cfailreason,"",filename,"update",ctype,bussinessid,params,db);
                                }
                            }
                            if ("1".equals(returnfileMap.get("status").toString()) && "".equals(message)) {
                                returnList.add(returnfileMap);
                            }
                            if(!message.isEmpty()){
                                /*总账元数据表主键*/
                                String bussinessid = m.get("列表主键").toString();
                                List<Map> resultLog = db.queryMapList("select * from da_fc_gen_pdf_log g where g.cfiletype = 'PZ01' and g.bussinessid ='"+bussinessid+"'");
                                if(resultLog.isEmpty()){
                                    addPdfLog(m,bussinessid,message,"",filename,"add",ctype,bussinessid,params,db);
                                }else{
                                    addPdfLog(m,bussinessid,message,"",filename,"update",ctype,bussinessid,params,db);
                                }

                            }
                        }
                    }else{
                        message = "未获取到所取用的模板，请设置模板后再生成版式文件";
                        for (Map m : zb01List) {
                            /*资料名称*/
                            String dat = m.get("期间") == null ? "" : m.get("期间").toString();
                            String dt = "";
                            if (!dat.isEmpty()) {
                                String[] arr = dat.split("-");
                                for (int j = 0; j < arr.length; j++) {
                                    dt = dt + arr[j];
                                }
                            }
                            String filename = dt + "_" + "总账";
                            /*总账元数据表主键*/
                            String bussinessid = m.get("列表主键").toString();
                            List<Map> resultLog = db.queryMapList("select * from da_fc_gen_pdf_log g where g.cfiletype = '"+ctype+"' and g.bussinessid ='"+bussinessid+"'");
                            if(resultLog.isEmpty()){
                                addPdfLog(m,bussinessid,message,"",filename,"add",ctype,bussinessid,params,db);
                            }else{
                                addPdfLog(m,bussinessid,message,"",filename,"update",ctype,bussinessid,params,db);
                            }
                        }
                    }
                    db.getDbService().commit();
                }
            }else{
                message = "未查询到可生成的数据";
            }
        }else{
            message = "请选择需要生成版式文件的数据";
        }
        Map reMap = new HashMap();
        if(!"".equals(message)){
            reMap.put("status","0");
            reMap.put("message",message);
        }else if("".equals(message)&&zbclassList.size() == returnList.size()){
            reMap.put("status","1");
            reMap.put("message","版式文件生成成功,共生成"+returnList.size()+"条；");
        } else if ("".equals(message)&&zbclassList.size() != returnList.size()) {
            int success = returnList.size();
            int fail = zbclassList.size() - returnList.size();
            reMap.put("status","2");
            reMap.put("message","版式文件生成成功"+success +"条；"+"失败"+fail+"条；");
        }
        return reMap;
    }

    @Override
    public List<Map> getLayoutGenLog(Params params) {

        String dsId = AcsHelper.getLoginUserMap().get(Constants.SESSION_DSID).toString();
        ApsContextDb db = new ApsContextDb(dsId);
        //DbService db = new ApsContextDb().getDb();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        String dt =sdf.format(date);
        params.put("coperatedate",dt);
        List<Map> dataLog = db.queryMapListById("da_fc_template_sql.getLayoutGenLog",params);
        return dataLog;
    }

    @Override
    public boolean delLayoutGenLog(Params params,String ctype) {

        String dsId = AcsHelper.getLoginUserMap().get(Constants.SESSION_DSID).toString();
        ApsContextDb db = new ApsContextDb(dsId);

        boolean result = true;
        Map resultMap = (Map)params.getFieldMap().get("result");
        List<Map> dataList = new ArrayList<>();
        if("PZ".equals(ctype)){//记账凭证数据
            dataList = (List<Map>) resultMap.get("9acd3fd7203e92113a4ce70cd9b94bfd");
        } else if ("ZB01".equals(ctype)) {//总分类账数据
            dataList = (List<Map>) resultMap.get("ba12c6bfb2dc207877d873ced0517f79");
        }
        try {
            if (dataList!=null) {
                for (Map map : dataList) {
                    String cgenpdfstatus = map.get("cgenpdfstatus") == null ? "0" : map.get("cgenpdfstatus").toString();
                    if ("0".equals(cgenpdfstatus)) {
                        String clistguid = map.get("cguid").toString();
                        List<Map> logList = db.queryMapList("select * from da_fc_gen_pdf_log where clistguid = ?", clistguid);
                        if (!logList.isEmpty()) {
                            Map paraMap = new HashMap<>();
                            paraMap.put("clistguid",clistguid);
                            db.deleteT("da_fc_gen_pdf_log", paraMap, "clistguid");
                        }
                    }
                }
            }
            return result;
        }catch (Exception e){
            throw new ApsException(e);
        }
    }

    public Map getFileNameAndPath(Map fileMap){
        String filename = "";
        String cfilerealname = "";
        String pathName = fileMap.get("pathName").toString();
        String cfileName = fileMap.get("fileName").toString();
        String[] arr = pathName.split("/");
        cfilerealname = arr[arr.length-1];
        filename = cfileName + ".pdf";
        Map reMap = new HashMap();
        reMap.put("filename",filename);
        reMap.put("cfilerealname",cfilerealname);
        String [] arr2 = pathName.split("/");
        String dafilepath = "";
        for (int i = 0; i < arr2.length; i++) {
            if(i != arr2.length - 1){
                if(i == arr2.length - 2){
                    dafilepath = dafilepath + arr2[i];
                }else{
                    dafilepath = dafilepath + arr2[i]+"/";
                }
            }
        }
        reMap.put("dafilepath",dafilepath);
        return reMap;
    }


}
