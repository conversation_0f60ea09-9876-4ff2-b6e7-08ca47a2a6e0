package com.aisino.da.fc.service.layoutGeneration.impl;

import com.aisino.aosplus.core.ConfigHelper;
import com.aisino.aosplus.core.Constants;
import com.aisino.aosplus.core.common.fault.BusinessException;
import com.aisino.aosplus.core.dao.DbService;
import com.aisino.aosplus.core.mvc.annotation.Service;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.aosplus.core.util.CollectionUtil;
import com.aisino.aosplus.core.util.Guid;
import com.aisino.aosplus.core.util.StringUtil;
import com.aisino.aosplus.plugin.ms.Message;
import com.aisino.aosplus.session.AcsHelper;
import com.aisino.aosplus.session.SessionHelper;
import com.aisino.aps.apsdb.dbhelper.ApsContextDb;
import com.aisino.da.common.util.ArrayUtil;
import com.aisino.da.core.service.FileService;
import com.aisino.da.fc.service.layoutGeneration.LayoutGenerationCommonService;
import com.aisino.da.fc.util.ESConfigUtil;
import com.aisino.da.fc.util.ESUtil;
import com.aisino.da.fc.util.PrintPdfUtil;
import com.aisino.da.fc.util.bankreceipts.ocrbank.CollectionStringUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.io.FileUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class LayoutGenerationCommonServiceImpl implements LayoutGenerationCommonService {
    /*@Inject
    private ApsContextDb db;*/
    PrintPdfUtil printUtil = new PrintPdfUtil();
    private static Log logger = LogFactory.getLog(LayoutGenerationCommonServiceImpl.class);

    private static final ObjectMapper objectmapper = new ObjectMapper();

    public static boolean isJson(String json){
        try{
            objectmapper.readTree(json);
            return true;
        } catch (JsonProcessingException e) {
            return false;
        }
    }

    /*获取报文接口定义的主表id*/
    public String getCguid(String tablename, String orgnid,String billtype)
    {
        //查询有没有适合当前组织的报文定义，，，如果有直接返回
        String innerCode = queryInnerCode(orgnid);
        List<Map> list = lookForParent(innerCode, billtype);
        if(CollectionUtil.isNotEmpty(list)){
            return CollectionUtil.getStringFromMap(list.get(0), "cHeadId");
        }
        //没有符合当前组织的查询 默认没有设置适用组织范围的报文定义
        Map bwconfigmap = queryBwConfig(billtype);
        if (!CollectionUtil.isBlankMap(bwconfigmap))
        {
            String cguid = CollectionUtil.getStringFromMap(bwconfigmap, "cguid");
            return cguid;
        }else
        {
            return null;
        }
    }

    public String queryInnerCode(String csourceorgnid) {
        DbService db = new ApsContextDb().getDb();
        String sql = "SELECT cInnerCode FROM aos_orgn WHERE cguid = ?" ;
        return db.queryColumn(sql,csourceorgnid);
    }

    public List<Map> lookForParent(String innerCode, String billtype) {
        DbService db = new ApsContextDb().getDb();
        String sql = "SELECT bo.* FROM da_bw_organscope_config bo " +
                "left join da_bw_config bc  " +
                "on bc.cguid = bo.cHeadId " +
                "WHERE bc.cbwnameguid_name =? and ((? LIKE CONCAT('%',bo.cInner_Code,'%')  AND bo.cContain = '1') or bo.cInner_Code=?) ORDER BY bo.cInner_code" ;

/*
        oracle写法
        select * from aos_orgn where '1m!13501!13504!135010106%'like '%'|| cInnerCode||'%'
*/

        return db.queryMapList(sql, billtype,innerCode, innerCode);
    }

    public Map queryBwConfig(String billtype)
    {
        DbService db = new ApsContextDb().getDb();
        String sql = "select * from da_bw_config i  where i.cbwnameguid_name=? and (select count(1) as num from da_bw_organscope_config f where f.cHeadId =i.cGuid) = 0";
        return db.queryMap(sql, billtype);
    }


    @Override
    public Map genCommonPDFBill(Params params) throws IOException {
        String dsId = AcsHelper.getLoginUserMap().get(Constants.SESSION_DSID).toString();
        ApsContextDb db = new ApsContextDb();
        db.getDb(dsId);

        Map reMap = new HashMap<>();
        String allerrmsg = "";
        Map fieldMap = params.getFieldMap();
        List<Map> mapList = (List<Map>)fieldMap.get("getCheckedData");
        /*生成成功的数据*/
        int success = 0;
        /*失败的数据*/
        int defeat = 0;
        if(!mapList.isEmpty()){
            String templcguid = "";
            /*当前登录组织、管理组织*/
            String cadminorgnId = SessionHelper.getCurrentAdminOrgnId();
            String sessionCorgnid = SessionHelper.getCurrentOrgnId();
            for(Map map : mapList){
                String message = "";
                /*获取单据名称、编码、单据id、单据日期、对应的单据表*/
                String cbillcode = map.get("cbillcode").toString();
                String cdabilltypename = map.get("cdabilltypename").toString();
                String cdabilltypeid = map.get("cdabilltypeid").toString();
                String billid = map.get("billid").toString();
                String datasource = map.get("datasource").toString();
                String exttablename = "";
                if("财智云费控".equals(datasource)){
                    message = message+"暂不支持数据来源为[财智云费控]的单据资料生成版式文件!";
                }else{
                    exttablename = map.get("exttablename") == null ? "" : map.get("exttablename").toString();
                    if("".equals(exttablename)){
                        message = message+"未获取到单据资料["+cdabilltypename+"]的实体表!";
                    }
                }
                /*获取报文接口定义的主表id   校验配置报文明细为是还是否   是，可以生成版式文件   否，不能生成版式文件*/
                String bwcguid = getCguid(exttablename,sessionCorgnid,cdabilltypename);
                if(!"".equals(bwcguid)&&!bwcguid.isEmpty()){
                    Map is_configdetaisMap = db.queryMap("select is_configdetais from da_bw_config where cguid= '"+bwcguid+"'");
                    String is_configdetais = is_configdetaisMap.get("is_configdetais").toString();
                    if("否".equals(is_configdetais)){
                        //message = message+"暂不支持配置报文明细设置为否的单据资料生成版式文件";
                        message = message+"未配置报文明细,无法生成版式文件!";
                    }
                }


                String cvoucherid = map.get("cvoucherid").toString();
                /*文件名称*/
                //2025/6/18 wpf 去掉cbillcode中"/",否则上传文件时会拆分建成文件夹,导致文件名字和云存储返回的不一致
                String fileName = cdabilltypename + "_" + cbillcode.replaceAll("/","") + ".pdf";

                /*完整性状态 人工核对校验*/
                String cintegritystatus = map.get("cintegritystatus") == null ? "" : map.get("cintegritystatus").toString();
                if("6".equals(cintegritystatus)){
                    message = message + "人工核对完整不允许生成版式文件!";
                }

                /*查询版式生成设置表，校验是否启用生成版式文件*/
                Map cgenfileMap = db.queryMap("select c.cgenfile from da_fc_gen_pdf c where c.cadminorgnId = '"+cadminorgnId+"' and c.carchivalid = '"+cdabilltypeid+"'");
                String cgenfile = cgenfileMap == null ? "0" : cgenfileMap.get("cgenfile").toString();
                if("0".equals(cgenfile)|| StringUtil.isEmpty(cgenfile)){
                    if(!message.contains("单据资料["+cdabilltypename+"]未启用生成版式文件!")){
                        message = message+"单据资料["+cdabilltypename+"]未启用生成版式文件!";
                    }
                }

                Params params2 = new Params();
                params2.put("billid",billid);
                List<Map> listglMap = db.queryMapListById("da_fc_template_sql.getbilldata",params2.getFieldMap().get("billid"));
                if(!listglMap.isEmpty()){
                    String ceastatus = listglMap.get(0).get("ceastatus") == null ? "" : listglMap.get(0).get("ceastatus").toString();
                    if(!"0".equals(ceastatus)&&!"".equals(ceastatus)){
                        message = "归档后不能生成版式文件!";
                    }else if("".equals(ceastatus)){
                        message = "请先收集数据!";
                    }
                }

                if("".equals(message)){
                    /*单据日期*/
                    String ddate = map.get("ddate").toString();
                    String[] ddateArr = ddate.split("-");
                    String year = ddateArr[0];
                    /*文件表资料区间取值*/
                    String carriertype = ddateArr[0] + "-" + ddateArr[1];

                    /*校验是否配置打印模板*/
                    Params params1 = new Params();
                    /*获取模板id  用户被指定多个模板时，优先取用非全局适用模板*/
                    params1.put("carchivalid",cdabilltypeid);
                    params1.put("cAdminOrgnId",cadminorgnId);
                    params1.put("capplyorgnid",sessionCorgnid);
                    List<Map> tempList = db.queryMapListById("da_fc_template_sql.getctemplatepdfid",params1);
                    logger.error("##########模板数据"+tempList);
                    if(tempList.size() > 1){
                        for (int j = 0; j < tempList.size(); j++) {
                            String callin = tempList.get(j).get("callin").toString();
                            if("0".equals(callin)){
                                templcguid = tempList.get(j).get("ctemplatepdfid").toString();
                            }
                        }
                    } else if (tempList.size() == 1) {
                        templcguid = tempList.get(0).get("ctemplatepdfid") == null ? "" : tempList.get(0).get("ctemplatepdfid").toString();
                    }

                    if(!"".equals(templcguid)){
                        List<Map> billList = db.queryMapList("select * from "+exttablename+" bbb where bbb.oldcguid = '"+billid+"'");
                        if(!billList.isEmpty()){
                            for(Map mapbill: billList){
                                String cbilljson = mapbill.get("cbilljson").toString();
                                boolean result = isJson(cbilljson);
                                if(result){
                                    /*cbilljson转map 取出明细表数据*/
                                    Map billMapData =(Map) JSONObject.parse(cbilljson);
                                    /*创建一个明细表的map集合*/
                                    Map billdetailMap = new HashMap();
                                    for (int i = 1; i <= 10; i++) {
                                        List<Map> detailbill = (List<Map>)billMapData.get("明细表"+i);
                                        if(detailbill != null && !detailbill.isEmpty()){
                                            String key = "明细表"+i;
                                            billdetailMap.put(key,detailbill);
                                            billMapData.remove("明细表"+i);
                                        }
                                    }

                                    /*组装需要打印的数据*/
                                    Map parMap = new HashMap();

                                    if(!billMapData.isEmpty()){
                                        List<Map> title = new ArrayList<>();
                                        billMapData.put("单据类型",cdabilltypename);
                                        title.add(billMapData);
                                        parMap.put(cdabilltypename,title);
                                    }

                                    if(!billdetailMap.isEmpty()){
                                        int i = billdetailMap.size();
                                        if(i>0){
                                            for (int j = 1; j <= i+1; j++) {
                                                String key = "明细表"+j;
                                                List<Map> detailList = (List<Map>)billdetailMap.get(key);
                                                if(detailList!=null){
                                                    parMap.put(key,detailList);
                                                }
                                            }
                                        }
                                    }

                                    Message msg = new Message( "aps.print.GetImageStreamMessage");
                                    Params par = new Params();

                                    par.put("printTemplateId",templcguid);
                                    par.put("data",parMap);
                                    Object obj = msg.publish(par);
                                    /*上传文件服务器  路径为   format/组织编码/凭证id/单据类型名称cdabilltypename/文件名称(单据类型名称+单据编号  cdabilltypename_+cbillcode).pdf*/
                                    String orgncode = db.queryColumnById("da_fc_gen_pdf_sql.getcorgnidccode",SessionHelper.getCurrentOrgnId());
                                    String printURL = "format/"+ orgncode + "/" + cvoucherid + "/" +cdabilltypename + "/" + fileName;
                                    if(obj == null){
                                        reMap.put("status","0");
                                        reMap.put("message","模拟打印失败：没有返回值");
                                    }else{
                                        Map<String,Object> m = (Map) obj;
                                        String sucflag = CollectionUtil.getStringFromMap(m,"sucflag");//0:成功 1：失败
                                        String printMsg = CollectionUtil.getStringFromMap(m,"msg");//返回信息
                                        long cfilessize = 0;
                                        if("0".equals(sucflag)){

                                            try{
                                                Object filestream = m.get("filestream");//获取文件流
                                                byte[] bt = null;
                                                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                                                ObjectOutputStream oos = new ObjectOutputStream(bos);
                                                oos.writeObject(filestream);
                                                oos.flush();
                                                bt = bos.toByteArray();
                                                oos.close();
                                                bos.close();

                                                /*上传文件服务器*/
                                                InputStream inputStream = new ByteArrayInputStream(bt);
                                                /*获取文件大小*/
                                                InputStream inputStream2 = new ByteArrayInputStream(bt);

                                                Map fileremap = FileService.uploadFile(params,printURL,inputStream);

                                                try {
                                                    File file = new File("temfile.pdf");
                                                    FileUtils.copyInputStreamToFile(inputStream2, file);
                                                    /*文件大小*/
                                                    cfilessize = file.length();
                                                    file.delete();
                                                }catch (Exception e){
                                                    printMsg = "获取文件大小失败："+e.toString();
                                                    message = printMsg;
                                                }
                                                inputStream.close();
                                                inputStream2.close();


                                                Object object = fileremap.get("cstatus");
                                                if (object == null || "".equals(object)){
                                                    message = "调用上传失败，返回值为空！";
                                                }
                                                if ("0".equals(object) || 0==Integer.parseInt(object.toString())){
                                                    Object mssg = fileremap.get("message");
                                                    if (mssg == null || "".equals(mssg)){
                                                        message = "调用上传失败，无报错信息！";
                                                    }else {
                                                        message = printMsg + mssg.toString();
                                                    }
                                                }
                                            }catch(Exception e){
                                                message = "生成版式文件失败！";
                                            }
                                        }else{
                                        /*reMap.put("status","0");
                                        reMap.put("message","模拟打印失败："+printMsg);*/
                                            message = "模拟打印失败："+printMsg;
                                        }
                                        if("".equals(message)){
                                            Map paraMap = new HashMap();
                                            paraMap.put("cbillid",billid);
                                            paraMap.put("cfilessize",cfilessize);
                                            paraMap.put("fileName",fileName);
                                            paraMap.put("pathName",printURL);
                                            paraMap.put("cvoucherid",cvoucherid);
                                            paraMap.put("carriertype",carriertype);
                                            paraMap.put("datainterval",carriertype);
                                            /*插入文件表*/
                                            /*添加文件表  获取列表保存的文件表表名，当表名为空时拼接单据日期年份作为文件表表名*/
                                            String fileTableName = "";
                                            String cfiles_table_name = map.get("cfiles_table_name") == null ? "" : map.get("cfiles_table_name").toString();
                                            if("".equals(cfiles_table_name)){
                                                fileTableName = "da_files_" + year;
                                            }else{
                                                fileTableName = cfiles_table_name;
                                            }
                                            Map fileMap = addBillFile(fileTableName,paraMap,db,reMap);
                                            if(!"0000".equals(fileMap.get("ccode").toString())){
                                            /*reMap.put("status","0");
                                            reMap.put("message","插入文件表失败！");*/
                                                message = "插入文件表失败！";
                                            }else{

                                                String ifileqty = map.get("ifileqty") == null ? "0" : map.get("ifileqty").toString();
                                                String cgenpdfstatus = map.get("cgenpdfstatus") == null ? "0" : map.get("cgenpdfstatus").toString();
                                                String cguid = map.get("cguid").toString();

                                                if("1".equals(cgenpdfstatus)){
                                                    ifileqty =  String.valueOf(Integer.parseInt(map.get("ifileqty").toString()));
                                                }else{
                                                    ifileqty =  String.valueOf(Integer.parseInt(map.get("ifileqty").toString())+1);
                                                }

                                                Params params5 = new Params();
                                                params5.put("ifileqty",ifileqty);
                                                params5.put("cgenpdfstatus","1");
                                                params5.put("cguid",cguid);
                                                db.updateById("da_fc_gen_pdf_sql.updatebilldata", params5);
                                            }
                                        }
                                    }
                                }else {
                                    message = "单据报文不符合json格式，请重新核对报文！";
                                }
                            }
                        }else{
                            /*reMap.put("status","0");
                            reMap.put("message","未查询到单据类型为["+cdabilltypename+"]单据编号为["+cbillcode+"]的单据，无法生成版式文件！");*/
                            message = "未查询到单据类型为["+cdabilltypename+"]单据编号为["+cbillcode+"]的单据，无法生成版式文件！";
                        }
                    }else{
                        /*reMap.put("status","0");
                        reMap.put("message","请先配置单据打印模板！");*/
                        message = "请先配置单据打印模板！";
                    }
                }
                if(!"".equals(message)){/*当message为空的时候，插入版式生成日志表*/
                    /*单据id*/
                    String bussinessid = billid;
                    List<Map> resultLog = db.queryMapList("select * from da_fc_gen_pdf_log g where g.cfiletype = '1' and g.bussinessid ='"+bussinessid+"'");
                    if(resultLog.isEmpty()){
                        /*操作时间*/
                        Date date = new Date();
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        String coperatedate = sdf.format(date);
                        /*失败原因*/
                        String cfailreason = message;
                        if(!allerrmsg.contains(message)){
                            allerrmsg = allerrmsg + message;
                        }
                        Map cfailMap = new HashMap();
                        cfailMap.put("coperatedate", coperatedate);
                        fileName = cdabilltypename + "_" + cbillcode;
                        cfailMap.put("cdataname", fileName);
                        cfailMap.put("cfailreason", cfailreason);
                        cfailMap.put("csyserror", cfailreason);
                        cfailMap.put("bussinessid", bussinessid);
                        cfailMap.put("corgnid", SessionHelper.getCurrentOrgnId());
                        //单据资料资料类型编码应为 1 ，此处在插入式改为bill(平台查询时用数字位编码有bug)
                        cfailMap.put("cfiletype", "bill");
                        cfailMap.put("cstatus", "0");
                        cfailMap.put("cguid", Guid.g());
                        String ccreatedate = CollectionStringUtil.getSysDateTime();
                        cfailMap.put("ccreatedate", ccreatedate);
                        Long time = System.currentTimeMillis();
                        cfailMap.put("ctimestamp", String.valueOf(time));
                        cfailMap.put("ccreatorid", SessionHelper.getCurrentUserId());
                        db.insert("da_fc_gen_pdf_log", cfailMap);
                    }else{
                        /*操作时间*/
                        Date date = new Date();
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        String coperatedate = sdf.format(date);
                        /*失败原因*/
                        String cfailreason = message;

                        Params paramslog = new Params();
                        paramslog.put("bussinessid", bussinessid);
                        paramslog.put("cfailreason", cfailreason);
                        paramslog.put("csyserror", cfailreason);
                        paramslog.put("coperatedate", coperatedate);
                        paramslog.put("cdataname", fileName);
                        //db.update("update da_fc_gen_pdf_log set cfailreason = '"+cfailreason+"',csyserror = '"+cfailreason+"',coperatedate = '"+coperatedate+"',cdataname = '"+filename+"' where bussinessid = ?",params);
                        db.updateById("da_fc_gen_pdf_sql.updategenpdflog",params);
                    }
                    defeat++;
                }else{
                    success++;
                }
            }
        }

        if(success>0&&defeat>0){
            reMap.put("status","2");
            reMap.put("message","版式文件生成成功"+success +"条；"+"失败"+defeat+"条；");
        } else if (success>0&&defeat==0) {
            reMap.put("status","1");
            reMap.put("message","版式文件生成成功,共生成"+success+"条；");
        } else if (success == 0&& defeat>0) {
            reMap.put("status","0");
            reMap.put("message",allerrmsg);
        }
        return reMap;
    }


    @Override
    public Map genCommonPDF(Params params) throws IOException {

        String dsId = AcsHelper.getLoginUserMap().get(Constants.SESSION_DSID).toString();
        ApsContextDb db = new ApsContextDb();
        db.getDb(dsId);

        List<Map> returnList = new ArrayList<>();
        Map reMap = new HashMap<>();
        String message = "";
        Map fieldMap = params.getFieldMap();
        List<Map> mapList = (List<Map>)fieldMap.get("getCheckedData");
        if(!mapList.isEmpty()){
            String cadminorgnId = SessionHelper.getCurrentAdminOrgnId();
            String sessionCorgnid = SessionHelper.getCurrentOrgnId();
            for(Map map : mapList){
                String msg = "";
                /*获取明细分类id、名称、编码、打印数据的列表id、列表的期间*/
                String cdetailclassguid = map.get("cdetailclassguid").toString();
                String cdetailclasscode = map.get("cdetailclasscode").toString();
                String sourTableName = getTableName(cdetailclasscode);
                String cdetailclassguid_name = map.get("cdetailclassguid_name").toString();
                String cperioddate = map.get("cperioddate").toString();
                String clistguid = map.get("cguid").toString();
                /*完整性状态 人工核对校验*/
                String cintegritystatus = map.get("cintegritystatus") == null ? "" : map.get("cintegritystatus").toString();
                if("6".equals(cintegritystatus)){
                    msg = msg + "人工核对完整不允许生成版式文件!";
                }
                Params params2 = new Params();
                params2.put("clistguid",clistguid);
                List<Map> listglMap = db.queryMapListById("da_fc_template_sql.getaccountdata",params2.getFieldMap().get("clistguid"));
                if(!listglMap.isEmpty()){
                    String ieastatus = listglMap.get(0).get("ieastatus") == null ? "" : listglMap.get(0).get("ieastatus").toString();
                    String centityfilestatus = listglMap.get(0).get("centityfilestatus") == null ? "" : listglMap.get(0).get("centityfilestatus").toString();
                    if((!"0".equals(ieastatus)&&!"".equals(ieastatus))||(!"0".equals(centityfilestatus)&&!"".equals(centityfilestatus))){
                        msg = msg + "归档组卷后不能生成版式文件!";
                    }else if("".equals(ieastatus)&&"".equals(centityfilestatus)){
                        msg = msg + "请先收集数据!";
                    }
                }

                /*获取公司代码配置，判断是否为一汽项目，是在判断明细分类是否为明细账，是则取clistguid为期间*/
                /*String CompanyCode = ConfigHelper.getString("CompanyCode");
                if(CompanyCode!=null){
                    if("YQ".equals(CompanyCode)&&"明细账".equals(cdetailclassguid_name)){
                        clistguid = cperioddate;
                    }
                }else{
                    clistguid = map.get("cguid").toString();
                }*/

                /*查询版式生成设置表，校验是否启用生成版式文件*/
                Params params3 = new Params();
                params3.put("carchivalid",cdetailclassguid);
                params3.put("cadminorgnId",cadminorgnId);
                //String cgenfile =  db.queryColumn("select c.cgenfile from da_fc_gen_pdf c where c.cadminorgnId = '"+cadminorgnId+"'and c.carchivalid = ?",params3.getFieldMap().get("cdetailclassguid"));
                Map cgenfileMap = db.queryMap("select c.cgenfile from da_fc_gen_pdf c where c.cadminorgnId = '"+cadminorgnId+"' and c.carchivalid = '"+cdetailclassguid+"'");
                String cgenfile = cgenfileMap == null ? "0" : cgenfileMap.get("cgenfile").toString();
                if("0".equals(cgenfile)|| StringUtil.isEmpty(cgenfile)){
                    msg = msg+"明细分类["+cdetailclassguid_name+"]未启用生成版式文件!";
                }
                if(msg.isEmpty()){
                    String templcguid = "";
                    /*获取模板id  用户被指定多个模板时，优先取用非全局适用模板*/
                    Params params1 = new Params();
                    params1.put("carchivalid",cdetailclassguid);
                    params1.put("cAdminOrgnId",cadminorgnId);
                    params1.put("capplyorgnid",sessionCorgnid);
                    List<Map> tempList = db.queryMapListById("da_fc_template_sql.getctemplatepdfid",params1);
                    if(tempList.size() > 1){
                        for (int j = 0; j < tempList.size(); j++) {
                            String callin = tempList.get(j).get("callin").toString();
                            if("0".equals(callin)){
                                templcguid = tempList.get(j).get("ctemplatepdfid").toString();
                            }
                        }
                    } else if (tempList.size() == 1) {
                        templcguid = tempList.get(0).get("ctemplatepdfid") == null ? "" : tempList.get(0).get("ctemplatepdfid").toString();
                    }
                    if(!templcguid.isEmpty()){
                        /*组装打印所需参数准备打印*/
                        Map genMap = new HashMap();
                        genMap.put("id",clistguid);
                        genMap.put("cdetailclassguid_name",cdetailclassguid_name);
                        genMap.put("cdetailclasscode",cdetailclasscode);
                        genMap.put("cperioddate",cperioddate);
                        genMap.put("templcguid",templcguid);
                        logger.error("==========版式生成前commondsid=========="+dsId);
                        Map returnfileMap = printUtil.printCommonPdf(genMap,db);
                        db.getDb(dsId);
                        logger.error("==========版式生成业务类commondsid=========="+dsId);
                        String status = returnfileMap.get("status").toString();
                        if("0".equals(status)){
                            msg = returnfileMap.get("msg").toString();
                        }else{
                            /*获取页面模板id，查询出对应的列表表名，用来更新版式生成状态*/
                            String ctemplateid = map.get("ctemplateid").toString();
                            Params params10 = new Params();
                            params10.put("cguid",ctemplateid);
                            String tableName = db.queryColumnById("da_fc_gen_pdf_sql.getTableName",params10);
                            System.out.println(tableName);
                            Map filenum = db.queryMap("select cnumberoffiles,cgenpdfstatus from "+sourTableName+" where cguid = ?",clistguid);
                            String cnumberoffiles = "";
                            if(filenum == null){
                                msg = "未查询到可生成的数据！";
                            }else{
                                if (filenum.get(cnumberoffiles) == null){
                                    cnumberoffiles = "1";
                                }else{
                                    String cgenpdfstatus = filenum.get("cgenpdfstatus") == null ? "0" : filenum.get("cgenpdfstatus").toString();
                                    if("1".equals(cgenpdfstatus)){
                                        cnumberoffiles =  String.valueOf(Integer.parseInt(filenum.get("cnumberoffiles").toString()));
                                    }else{
                                        cnumberoffiles =  String.valueOf(Integer.parseInt(filenum.get("cnumberoffiles").toString())+1);
                                    }
                                }
                                Params params5 = new Params();
                                params5.put("isexistfile","1");
                                params5.put("cnumberoffiles",cnumberoffiles);
                                params5.put("cgenpdfstatus","1");
                                params5.put("cguid",clistguid);
                                db.updateById("da_fc_gen_pdf_sql.updateaccountbook", params5);
                                /*生成成功的数据统计数量*/
                                returnList.add(map);
                                /*添加文件表*/
                                String fileTableName = "da_files_" + cperioddate.substring(0, 4);
                                Map paraMap = new HashMap();
                                String classcode = db.queryColumnById("da_fc_gen_pdf_sql.getccodefromclass",cdetailclassguid);
                                paraMap.put("ctype",classcode);
                                paraMap.put("列表主键",clistguid);
                                paraMap.put("会计期间",map.get("cperioddate"));
                                Map fileMap = addFile(fileTableName,paraMap,db,returnfileMap);
                                if(!"0000".equals(fileMap.get("ccode").toString())){
                                    msg = "插入文件表失败";
                                }
                            }
                        }
                    }else{
                        msg = "未获取到任何模板，请检查当前组织是否设置模板!";
                    }
                    /*会重复插入数据，所以注释掉
                    if(!msg.isEmpty()){
                        *//*校验msg有没有提示，有责代表生成文件或上传对象存储错误，插入日志表*//*
                        String cfilename = cdetailclassguid_name +"-"+cperioddate;
                        List<Map> resultLog = db.queryMapList("select * from da_fc_gen_pdf_log g where g.cfiletype = 'PZ01' and g.bussinessid ='"+clistguid+"'");
                        if(resultLog.isEmpty()){
                            addPdfLog(map,clistguid,msg,"",cfilename,"add",cdetailclasscode,clistguid);
                        }else{
                            addPdfLog(map,clistguid,msg,"",cfilename,"update",cdetailclasscode,clistguid);
                        }
                    }*/
                }
                message = message + msg;
                if(!msg.isEmpty()){
                    /*校验msg有没有提示，有责代表生成文件或上传对象存储错误，插入日志表*/
                    String cfilename = cperioddate + "_" + cdetailclassguid_name;
                    List<Map> resultLog = db.queryMapList("select * from da_fc_gen_pdf_log g where g.cfiletype = 'PZ01' and g.bussinessid ='"+clistguid+"'");
                    if(resultLog.isEmpty()){
                        addPdfLog(map,clistguid,msg,"",cfilename,"add",cdetailclasscode,clistguid);
                    }else{
                        addPdfLog(map,clistguid,msg,"",cfilename,"update",cdetailclasscode,clistguid);
                    }
                }
            }
        }else{
            message = "没有可生成版式文件的数据";
        }
        if(!"".equals(message)&&returnList.isEmpty()){
            reMap.put("status","0");
            reMap.put("message",message);
        }else if("".equals(message)&&mapList.size() == returnList.size()){
            reMap.put("status","1");
            reMap.put("message","版式文件生成成功,共生成"+returnList.size()+"条；");
        } else if (!"".equals(message)&&mapList.size() != returnList.size()) {
            int success = returnList.size();
            int fail = mapList.size() - returnList.size();
            reMap.put("status","2");
            reMap.put("message","版式文件生成成功"+success +"条；"+"失败"+fail+"条；失败日志："+message);
        }
        return reMap;
    }

    public  String getTableName(String cdetailclasscode){
        String sourTableName = "";
        if("PZ01".equals(cdetailclasscode)){
            sourTableName = "da_fc_voucherinfo";
        }else if(cdetailclasscode.contains("ZB")){
            sourTableName = "da_fc_accountbook";
        }
        return sourTableName;
    }

    public Map addFile(String fileTable,Map dataMap,ApsContextDb db,Map returnfileMap){
        Map reMap = new HashMap();
        String ctype = dataMap.get("ctype").toString();
        String clistguid = dataMap.get("列表主键").toString();
        List<Map> fileData = db.queryMapList("select * from " +fileTable+" where dafilepath like '%format%' and  clistguid = ?",clistguid);
        if(!fileData.isEmpty()){
            try {
                Map map = new HashMap<>();
                map.put("cguid", fileData.get(0).get("cguid"));
                //调用ES删除
                List<String> listGuid = new ArrayList<>();
                listGuid.add(clistguid);
                ESUtil.deleteSeDetial(listGuid, "ZB");
                db.deleteT(fileTable, map, "cguid");
            }catch (Exception e){
                throw new BusinessException("更新文件表中版式文件数据失败！");
            }
        }

        List<Map> fileData2 = db.queryMapList("select * from " +fileTable+" where dafilepath like '%format%' and  clistguid = ?",clistguid);

        if(fileData2.isEmpty()){
            try {
                Map insData = new HashMap();
                String cguid = Guid.g();
                insData.put("cguid", cguid);
                Map<String, String> props = ConfigHelper.getTagProps("aps-oss");
                String dxccname_da = ArrayUtil.getStringFromMap(props, "bucketName");
                insData.put("storagemethod", "default");
                insData.put("dxccname", dxccname_da);
                insData.put("cbusinesstype", ctype);
                insData.put("clistguid", clistguid);
                if("PZ".equals(ctype)){
                    insData.put("cvoucherid", dataMap.get("凭证id"));
                    insData.put("datainterval", dataMap.get("凭证日期").toString().substring(0,7));
                }else{
                    insData.put("datainterval", dataMap.get("会计期间"));
                }
                insData.put("cfilessize", returnfileMap.get("cfilessize"));
                Map fileNameMap = this.getFileNameAndPath(returnfileMap);
                insData.put("filename", fileNameMap.get("filename")+".pdf");
                insData.put("cfilerealname", fileNameMap.get("cfilerealname"));
                insData.put("cfiletype", "2");
                insData.put("cfilesourcetype", "9");
                insData.put("syssource", "aps-oss");
                insData.put("dafilepath", fileNameMap.get("dafilepath"));
                String ccreatedate = CollectionStringUtil.getSysDateTime();
                insData.put("ccreatedate", ccreatedate);
                Long time = System.currentTimeMillis();
                insData.put("cTimeStamp", String.valueOf(time));
                insData.put("cstatus", returnfileMap.get("status"));
                insData.put("cCreatorId", SessionHelper.getCurrentUserId());
                insData.put("cfiles_table_name", fileTable);
                insData.put("corgnid", SessionHelper.getCurrentOrgnId());
                insData.put("corgnid_name", SessionHelper.getCurrentOrgnName());
                db.insert(fileTable, insData);
                //调用ES
                Map daData = db.getOneRecord("select 'ZB' caccountclasscode,'会计账簿' caccountclassname,cdetailclasscode,cdetailclassguid_name cdetailclassname,cperioddate from da_fc_accountbook where cguid = ?", clistguid);
                daData.put("cguid",cguid);
                daData.put("corgnid",SessionHelper.getCurrentOrgnId());
                daData.put("corgnid_name",SessionHelper.getCurrentOrgnName());
                daData.put("cfilerealname",fileNameMap.get("cfilerealname"));
                daData.put("cfiles_table_name",fileTable);
                daData.put("dafilepath",fileNameMap.get("dafilepath"));
                daData.put("filename",fileNameMap.get("filename")+".pdf");
                List<Map> filelist = new ArrayList<>();
                filelist.add(daData);
                if(ESConfigUtil.checkConfig()){
                    ESUtil esUtil = new ESUtil();
                    esUtil.addSeDetial(filelist, db.getDbID(),"ZB");
                }
                reMap.put("ccode","0000");
            } catch (Exception e){
                reMap.put("ccode","9999");
                reMap.put("msg","系统异常");
                reMap.put("exc",e.getMessage());
            }
        }
        if(reMap.isEmpty()){
            reMap.put("ccode","0000");
        }
        return reMap;
    }

    public Map addBillFile(String fileTable,Map dataMap,ApsContextDb db,Map returnfileMap){
        Map reMap = new HashMap();
        String cbillid = dataMap.get("cbillid").toString();
        List<Map> fileData = db.queryMapList("select * from " +fileTable+" where dafilepath like '%format%' and  cbillid = ?and cvoucherid = '"+dataMap.get("cvoucherid")+"'",cbillid);
        if(!fileData.isEmpty()){
            try {
                Map map = new HashMap<>();
                map.put("cguid", fileData.get(0).get("cguid"));
                //调用ES删除
                List<String> listGuid = new ArrayList<>();
                listGuid.add(cbillid);
                ESUtil.deleteSeDetial(listGuid, "DJ");
                db.deleteT(fileTable, map, "cguid");
            }catch (Exception e){
                throw new BusinessException("更新文件表中版式文件数据失败！");
            }
        }

        List<Map> fileData2 = db.queryMapList("select * from " +fileTable+" where dafilepath like '%format%' and  cbillid = ? and cvoucherid = '"+dataMap.get("cvoucherid")+"'",cbillid);

        if(fileData2.isEmpty()){
            try {
                Map insData = new HashMap();
                String cguid = Guid.g();
                insData.put("cguid", cguid);
                Map<String, String> props = ConfigHelper.getTagProps("aps-oss");
                String dxccname_da = ArrayUtil.getStringFromMap(props, "bucketName");
                insData.put("storagemethod", "default");
                insData.put("dxccname", dxccname_da);
                insData.put("cbusinesstype", "1");
                insData.put("cbillid", cbillid);
                insData.put("cfilessize", dataMap.get("cfilessize"));
                insData.put("datainterval", dataMap.get("datainterval"));
                insData.put("carriertype", dataMap.get("carriertype"));
                Map fileNameMap = this.getFileNameAndPath(dataMap);
                insData.put("filename", fileNameMap.get("filename"));
                insData.put("cfilerealname", fileNameMap.get("cfilerealname"));
                insData.put("cfiletype", "2");
                insData.put("cfilesourcetype", "9");
                insData.put("syssource", "aps-oss");
                insData.put("cvoucherid", dataMap.get("cvoucherid"));
                insData.put("dafilepath", fileNameMap.get("dafilepath"));
                String ccreatedate = CollectionStringUtil.getSysDateTime();
                insData.put("ccreatedate", ccreatedate);
                Long time = System.currentTimeMillis();
                insData.put("cTimeStamp", String.valueOf(time));
                insData.put("cstatus", "1");
                insData.put("cCreatorId", SessionHelper.getCurrentUserId());
                insData.put("cfiles_table_name", fileTable);
                insData.put("corgnid", SessionHelper.getCurrentOrgnId());
                insData.put("corgnid_name", SessionHelper.getCurrentOrgnName());
                db.insert(fileTable, insData);
                //调用ES
                Map daData = db.getOneRecord("select 'DJ' caccountclasscode,'单据资料' caccountclassname,cbillcode cdetailclasscode,cdabilltypename cdetailclassname,ddate cperioddate from da_api_fk_file_collection where billid = ?", cbillid);
                daData.put("cguid",cguid);
                daData.put("corgnid",SessionHelper.getCurrentOrgnId());
                daData.put("corgnid_name",SessionHelper.getCurrentOrgnName());
                daData.put("cfilerealname",fileNameMap.get("cfilerealname"));
                daData.put("cfiles_table_name",fileTable);
                daData.put("dafilepath",fileNameMap.get("dafilepath"));
                daData.put("filename",fileNameMap.get("filename")+".pdf");
                List<Map> filelist = new ArrayList<>();
                filelist.add(daData);
                if(ESConfigUtil.checkConfig()){
                    ESUtil esUtil = new ESUtil();
                    esUtil.addSeDetial(filelist, db.getDbID(),"DJ");
                }
                reMap.put("ccode","0000");
            } catch (Exception e){
                reMap.put("ccode","9999");
                reMap.put("msg","系统异常");
                reMap.put("exc",e.getMessage());
            }
        }
        if(reMap.isEmpty()){
            reMap.put("ccode","0000");
        }
        return reMap;
    }

    public Map getFileNameAndPath(Map fileMap){
        String filename = "";
        String cfilerealname = "";
        String pathName = fileMap.get("pathName").toString();
        String cfileName = fileMap.get("fileName").toString();
        String[] arr = pathName.split("/");
        cfilerealname = arr[arr.length-1];
        filename = cfileName;
        Map reMap = new HashMap();
        reMap.put("filename",filename);
        reMap.put("cfilerealname",cfilerealname);
        String [] arr2 = pathName.split("/");
        String dafilepath = "";
        for (int i = 0; i < arr2.length; i++) {
            if(i != arr2.length - 1){
                if(i == arr2.length - 2){
                    dafilepath = dafilepath + arr2[i];
                }else{
                    dafilepath = dafilepath + arr2[i]+"/";
                }
            }
        }
        reMap.put("dafilepath",dafilepath);
        return reMap;
    }

    public Map addPdfLog(Map m,String bussinessid,String message,String exc,String filename,String ope,String cdetailclasscode,String clistguid){

        String dsId = AcsHelper.getLoginUserMap().get(Constants.SESSION_DSID).toString();
        ApsContextDb db = new ApsContextDb();
        db.getDb(dsId);

        Map returnMap = new HashMap();
        try {
            if ("add".equals(ope)) {
                /*操作时间*/
                Date date = new Date();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String coperatedate = sdf.format(date);

                /*失败原因*/
                String cfailreason = message;
                Map cfailMap = new HashMap();
                cfailMap.put("coperatedate", coperatedate);
                cfailMap.put("cdataname", filename);
                if(exc.isEmpty()){
                    cfailMap.put("cfailreason", cfailreason);
                }else{
                    cfailMap.put("cfailreason", "系统异常");
                }
                cfailMap.put("csyserror", cfailreason);
                cfailMap.put("bussinessid", bussinessid);
                cfailMap.put("clistguid", clistguid);
                cfailMap.put("corgnid", SessionHelper.getCurrentOrgnId());
                cfailMap.put("cfiletype", cdetailclasscode);
                cfailMap.put("cstatus", "0");
                cfailMap.put("cguid", Guid.g());
                String ccreatedate = CollectionStringUtil.getSysDateTime();
                cfailMap.put("ccreatedate", ccreatedate);
                Long time = System.currentTimeMillis();
                cfailMap.put("ctimestamp", String.valueOf(time));
                cfailMap.put("ccreatorid", SessionHelper.getCurrentUserId());
                db.insert("da_fc_gen_pdf_log", cfailMap);
                returnMap.put("result", "0");
                returnMap.put("msg", "版式生成日志插入成功");
            } else if ("update".equals(ope)) {
                /*操作时间*/
                Date date = new Date();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String coperatedate = sdf.format(date);
                /*失败原因*/
                String cfailreason = message;

                Params params = new Params();
                params.put("bussinessid", bussinessid);
                db.update("update da_fc_gen_pdf_log set cfailreason = '"+cfailreason+"',csyserror = '"+cfailreason+"',coperatedate = '"+coperatedate+"',cdataname = '"+filename+"' where bussinessid = ?", params);
                returnMap.put("result", "0");
                returnMap.put("msg", "版式生成日志插入成功");
            }
        }catch (Exception e){
            throw new RuntimeException("版式生成日志插入失败");
        }
        return returnMap;
    }

}
