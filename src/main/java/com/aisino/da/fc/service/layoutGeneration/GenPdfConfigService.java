package com.aisino.da.fc.service.layoutGeneration;

import com.aisino.aosplus.core.mvc.bean.Params;

import java.util.List;
import java.util.Map;

public interface GenPdfConfigService {

    /**
     * 获取左树节点
     * @return 返回获取左树列表
     */
    Map<String,Object> loadLeftTreeChange();

    /**
     * 根据所接收的节点返回右表的数据
     * @param params 参数
     * @return 以分页+数据的形式返回
     */
    List<Map> getPageNodeList(Params params);

    /**
     * 批量新增版式生成设置数据
     * @param map 新增数据
     * @return true:新增成功，false:新增失败
     */
    Map savePdf(Map map);

    /**
     * 批量更新版式生成设置数据
     * @param mapList 新增数据
     * @return true:新增成功，false:新增失败
     */
    Map updatePdf(Map map);

    /**
     * 批量新增版式生成设置数据
     * @param map 新增数据
     * @return true:新增成功，false:新增失败
     */
    Map savePdfLine(Map map);

    /**
     * 批量更新版式生成设置数据
     * @param mapList 新增数据
     * @return true:新增成功，false:新增失败
     */
    Map updatePdfLine(Map map);

    /**
     * 获取版式生成子表数据
     * @param params 参数
     * @return 以分页+数据的形式返回
     */
    List<Map> getPdfLineList(Params params);

    /**
     * 删除版式生成子表数据
     * @param params 参数
     * @return 以分页+数据的形式返回
     */
    boolean delPdfLineList(Map map);
}
