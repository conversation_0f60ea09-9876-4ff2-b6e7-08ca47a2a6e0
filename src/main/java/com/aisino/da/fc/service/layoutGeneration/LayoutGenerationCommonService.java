package com.aisino.da.fc.service.layoutGeneration;

import com.aisino.aosplus.core.ioc.annotation.Impl;
import com.aisino.aosplus.core.mvc.bean.Params;
import com.aisino.da.fc.service.layoutGeneration.impl.LayoutGenerationCommonServiceImpl;

import java.io.IOException;
import java.util.Map;

@Impl(LayoutGenerationCommonServiceImpl.class)
public interface LayoutGenerationCommonService {
    Map genCommonPDF(Params params) throws IOException;

    Map genCommonPDFBill(Params params) throws IOException;
}
