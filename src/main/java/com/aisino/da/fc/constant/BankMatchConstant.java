package com.aisino.da.fc.constant;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/03/06/13:21
 */

/**
 * <AUTHOR>
 * @Description 银行回单匹配常量
 * @Date 2025/03/06/13:21
 */
public class BankMatchConstant {

    public final static String IAMT_F_START = "iamt_f_start";
    public final static String IAMT_F_END = "iamt_f_end";
    public final static String IDEBITAMT = "idebitamt";
    public final static String IDEBIT = "idebit";
    public final static String ICREDITAMT = "icreditamt";
    public final static String ICREDIT = "icredit";
    public static final String NUM_STR_ZERO = "0";
    public static final String NUM_STR_ONE = "1";
    public static final String NUM_STR_TWO = "2";
    public static final String NUM_STR_THREE = "3";
    public static final String NUM_STR_FORE = "4";
    public static final int WEEK_DAYS = 7;
    public static final int LOOP_NUM = 2000;
    public static final String LOGIC_NAME_AND = "并且";
    public static final String LOGIC_NAME_OR = "或者";
    public static final String LOGIC_NAME_EQUAL = "等于";
    public static final String LOGIC_NAME_NOT_EQUAL = "不等于";
    public static final String LOGIC_NAME_CONTAIN = "包含";
    public static final String LOGIC_NAME_NOT_CONTAIN = "不包含";
    public static final String LOGIC_NAME_ISNULL = "为空";
    public static final String LOGIC_NAME_ISNOTNULL = "不为空";
    public static final String COMPANYCODE_YQ = "YQ";
    public static final String CEASTATUS_NAME_SUCCESS = "收集成功";
    public static final String CMATCHSTATUS_NAME_SUCCESS = "已匹配";
    public static final String CINTEGRITYSTATUS_NAME_SUCCESS = "人工核对完整";

}
