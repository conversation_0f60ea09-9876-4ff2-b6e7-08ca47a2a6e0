package com.aisino.da.fc.constant;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @ClassName DaResult
 * @Deacription 校验结果返回
 * <AUTHOR>
 * @Date 2023/6/25 10:58
 * @Version 1.0
 **/
public class OcrResult {
    public Boolean cstatus;
    public String osrmsg;

    public List<Map> data;
    public static final Boolean SUCESS_CODE = true;
    public static final Boolean ERROR_CODE = false;

    public OcrResult() {
        this.cstatus = SUCESS_CODE;
        this.osrmsg = "成功";
        this.data = new ArrayList<>();
    }
    public OcrResult sucess (String msg) {
        this.cstatus = SUCESS_CODE;
        this.osrmsg = msg;
        return this;
    }

    public OcrResult error (String msg) {
        this.cstatus = ERROR_CODE;
        this.osrmsg = msg;
        return this;
    }

    public OcrResult setdata(List<Map> data) {
        this.cstatus = SUCESS_CODE;
        this.data = data;
        return this;
    }

    public Boolean getCstatus() {
        return cstatus;
    }

    public String getOsrmsg() {
        return osrmsg;
    }

    public List<Map> getData() {
        return data;
    }

}
