package com.aisino.da.fc.constant;

import java.util.HashMap;
import java.util.Map;

public class CollectConstant
{
    public final static String COLLECTTYPEKEY = "报文传输方式";

    /**
     * 报文传输方式Json
     */
    public final static String COLLECTTYPJSONVALUE = "1";

    /**
     * 报文传输方式文件
     */
    public final static String COLLECTTYPFILEVALUE = "0";

    public final static String FK_CODE_OK = "ok";

    public final static String FK_DEFAULT_CODE = "PZYWFL01";
    public final static String FK_DEFAULT_CODE_VALUE = "CZY01";

    public final static String  A6_DEFAULT_CODE = "PZYWFL02";

    public final static String  A6_DEFAULT_CODE_VALUE = "CZY02";

    public final static String A8_DEFAULT_CODE = "A8";

    public final static String VOUCHER = "01";//凭证
    public final static String VOUCHER_NAME = "记账凭证";//凭证

    public final static String VOUCHER_TABLE_NAME = "da_fc_voucherinfo_list";//凭证

    public final static String BILL = "02";//单据
    public final static String BILL_NAME = "单据资料";//单据

    /**
     * 资料收集单据方式区间code编码
     */
    public final static String BILL_COLL_INTERVAL_CODE = "0";

    /**
     * 资料收集单据方式日期 日期code编码
     */
    public final static String BILL_COLL_CODE = "1";

    /**
     * 资料收集票据方式区间code编码
     */
    public final static String TICKET_COLL_INTERVAL_CODE = "2";

    /**
     * 资料收集票据方式日期 日期code编码
     */
    public final static String TICKET_COLL_CODE = "1";

    public final static String BILL_TABLE_NAME = "da_data_collect_bill_log";//单据
    public final static String ACCOUNTBOOK = "03";//会计账簿
    public final static String ACCOUNTBOOK_NAME = "会计账簿";//会计账簿

    public final static String ACCOUNTBOOK_TABLE_NAME = "da_fc_accountbook_list";//会计账簿
    public final static String ACCOUNTREPORT = "04";//会计报告
    public final static String ACCOUNTREPORT_NAME = "会计报告";//会计报告
    public final static String OTHERACCOUNTREPORT = "05";//其他会计资料
    public final static String OTHERACCOUNTREPORT_NAME = "其他会计资料";//其他会计资料
    public final static String ACCOUNTREPORT_TABLE_NAME = "da_fc_accountreport_list ";//会计报告
    public final static String BANK_TABLE_NAME = "da_data_collect_bankreceipt_log";//回单流水

    public final static String TICKET_TABLE_NAME = "da_data_collect_ticket_logs";//票据
    //06 银行回单
    public final static String BANKRECEIPT = "06";//银行回单
    public final static String BANKRECEIPT_NAME = "银行回单";//银行回单

    //07 银行流水
    public final static String BANKSTATEMENT = "07";//银行流水
    public final static String BANKSTATEMENT_NAME = "银行流水";//银行流水

    //08 票据
    public final static String TICKET = "08";//票据资料
    public final static String TICKET_NAME = "票据资料";//票据资料

    //******** ddy 增加凭证关联关系
    public final static String PZZLREL = "09";//凭证关联关系
    public final static String PZZLREL_NAME = "凭证与原始资料关系";//凭证与原始资料关系；

    public final static String TICKET_CONTENT = "FSZL02";//票据资料适配收集内容
    //  01 、等待接收
    public final static String WAIT                      = "等待接收";
    public final static String WAIT_CODE                 = "01";
    //02收集中
    public final static String COLLECTING                = "收集中";
    public final static String COLLECTING_CODE           = "02";
    //03收集完成
    public final static String COLLECTION_COMPLETED      = "收集完成";
    public final static String COLLECTION_COMPLETED_CODE = "03";
    //04已取消
    public final static String CANCELLATION              = "已取消";
    public final static String CANCELLATION_CODE         = "04";

    public final static String ERROR_CODE                = "0000";

    public final static String SUCCESS_CODE              = "10000";

    public final static String FK_SUCCESS_CODE           = "000000";
    /**
     * 单据+凭证标识
     */
    public static final String VOUCHERANDBILL            = "05";
    public static final String MD_ERROR_CODE             = "md0000";

    public static final String ISCOVER                   = "是";
    public static final String NISCOVER                   = "否";


    /**
     *  银行流水分组依据
     */
    public final static int BATCHSIZE_BANKSTATEMENT = 2;

    /**
     *  银行回单分组依据
     */
    public final static int BATCHSIZE_BANKSRECEIPT = 2;

    /**
     *  票据分组依据
     */
    public final static int BATCHSIZE_TICKET = 2;

    /**
     *  单据分组依据
     */
    public final static int BATCHSIZE_BILLRECEIPT = 500;

    /**
     *  默认总批次
     */
    public final static int BATCHSIZE_COUNT = 1;

    /**
     *  默认当前批次
     */
    public final static int BATCHSIZE_DEFAULT = 1;

    /**
     * 已开始重新批量下载文件
     */
    public final static String IBATCHDOWNLOAD_TRUE = "1";

    /**
     * 未开始重新批量下载文件或已批量下载结束
     */
    public final static String IBATCHDOWNLOAD_FALSE= "0";

    public final static String COLLECTION_TYPE_PK_CODE = "filepackage";
    public final static String COLLECTION_TYPE_PK_NAME = "文件包";

    public final static String COLLECTION_TYPE_BW_CODE = "bw";
    public final static String COLLECTION_TYPE_BW_NAME = "报文格式";

    /**
     * 根据区间联查到相关凭证的票据
     */
    public final static String GET_INTERVAL_TICKET = "1";

    public final static String ISMETADATAJSON = "否";

    public final static String METADATAJSON = "单据通用元数据";

    public final static String ISJOSN = "是否解析";

    public final static String ISJOSNTRUE = "1";

    public final static String ISJOSNFALSE = "0";

    /**
     * 根据区间联查到相关凭证
     */
    public final static String GET_INTERVAL_BY_VOC = "2";

    /**
     * 是否为英文报文
     */
    public final static String EN = "EN";

    public final static String MYSQLDB = "mysql";
    public static final Map<String, String> PZ_ZL_RELATION_MAP=new HashMap<String, String>();
    static
    {
        PZ_ZL_RELATION_MAP.put("1","单据资料");
        PZ_ZL_RELATION_MAP.put("2","票据资料");
        PZ_ZL_RELATION_MAP.put("3","银行回单");
        PZ_ZL_RELATION_MAP.put("4","银行流水");
    }

}
