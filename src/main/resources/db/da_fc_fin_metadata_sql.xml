<?xml version="1.0" encoding="UTF-8"?>
<sqls>
    <sql group="da_fc_fin_metadata_sql">
        <i id="queryVoucher" desp="获取凭证元数据"><![CDATA[
            select * from da_fc_md_voucher a
            where #equal(a.csourceorganid,orgn) and #equal(a.cvoucherid,cvoucherid)
        ]]></i>
        <i id="queryVoucherz" desp="获取凭证元数据"><![CDATA[
            select b.* from da_fc_md_voucher a
            inner join da_fc_md_voucherz b on a.cGuid=b.cheadid
            where #equal(a.csourceorganid,orgn) and #equal(a.cvoucherid,cvoucherid)
        ]]></i>

        <i id="queryGLBook" desp="获取总账元数据"><![CDATA[
            select * from da_fc_md_glbook a
            where #equal(a.csourceorganid,orgn) and #equal(a.cperiod,period) and #equal(a.cacctstr,acctinfo)
        ]]></i>
        <i id="queryGLBookz" desp="获取总账元数据"><![CDATA[
            select * from da_fc_md_glbook a
            inner join da_fc_md_glbookz b on a.cGuid=b.cheadid and a.cperiod
            where #equal(a.csourceorganid,orgn) and #equal(a.cperiod,period) and #equal(a.cacctstr,acctinfo)
        ]]></i>

        <i id="queryDetailBook" desp="获取明细账元数据"><![CDATA[
            select * from da_fc_md_detailedbook a
            where #equal(a.csourceorganid,orgn) and #equal(a.cperiod,period) and #equal(a.cacctstr,acctinfo)
        ]]></i>
        <i id="queryDetailBookz" desp="获取明细账元数据"><![CDATA[
            select * from da_fc_md_detailedbook a
            inner join da_fc_md_detailedbookz b on a.cGuid=b.cheadid and a.cperiod
            where #equal(a.csourceorganid,orgn) and #equal(a.cperiod,period) and #equal(a.cacctstr,acctinfo)
        ]]></i>

        <i id="queryDayBook" desp="获取日记账元数据"><![CDATA[
            select * from da_fc_md_daybook a
            where #equal(a.csourceorganid,orgn) and #equal(a.cperiod,period)
            and #equal(a.cacctstr,acctinfo)
            and #equal(a.icashtype,icashtype)
        ]]></i>
        <i id="queryDayBookz" desp="获取日记账元数据"><![CDATA[
            select * from da_fc_md_daybook a
            inner join da_fc_md_daybookz b on a.cGuid=b.cheadid and a.cperiod
            where #equal(a.csourceorganid,orgn) and #equal(a.cperiod,period)
            and #equal(a.cacctstr,acctinfo)
            and #equal(a.icashtype,icashtype)
        ]]></i>

        <i id="queryCard" desp="获取固定资产明细账元数据"><![CDATA[
            select * from da_fc_md_fadetail a
            where #equal(a.csourceorganid,orgn) and #equal(a.cguid,cardid)
        ]]></i>

        <i id="queryReport" desp="获取报告元数据"><![CDATA[
            select * from  da_fc_md_report
            where #equal(a.csourceorganid,orgn) and #equal(a.cperiod,period) and #equal(a.reportcode,reportcode)
        ]]></i>

        <i id="queryOtherReport" desp="获取其他报告元数据"><![CDATA[
            select * from  da_fc_otheraccountreport
            where #equal(a.csourceorganid,orgn) and #equal(a.cperioddate,period) and #equal(a.cdetailclasscode,detailclasscode)
        ]]></i>
    </sql>
</sqls>