<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls>
    <sql group="da_fc_gen_pdf_sql">
        <i id="loadTreeZero" desp="加载根节点">
            <![CDATA[
            select '档案分类' as cname, '#ROOT0000' as cguid,'0' as ilevel;
        ]]>
        </i>
        <i id="loadTreeOneTest" desp="加载一级节点">
            <![CDATA[
            select cname,cguid,'1' as ilevel ,'#ROOT0000' as cParentId, '档案分类' as cParentId_name
            from da_bbs_file_class where cParentId = '#ROOT0000';
        ]]>
        </i>
        <i id="loadTreeOne" desp="加载一级节点">
            <![CDATA[
            select '会计档案' as cname, '#ROOT0000' as cguid,'1' as ilevel;
        ]]>
        </i>
        <i id="loadTreeTwoTest" desp="加载二级节点">
            <![CDATA[
            select cname,cguid,'2' as ilevel ,cParentId,cParentId_name from da_bbs_file_class where cParentId = ?;
        ]]>
        </i>
        <i id="loadTreeTwo" desp="加载二级节点">
            <![CDATA[
            select cname,cguid,ilevel+1 as ilevel ,cParentId,cParentId_name from da_bbs_file_class;
        ]]>
        </i>
        <i id="loadTreeThree" desp="加载三级节点">
            <![CDATA[
            select * from (
                select fcs.cname,fcs.cguid,'3' as ilevel,cheadid as cParentId,cheadid_name as cParentId_name,'0'as type,
                fcs.cname as fname,fcs.ccode
                from da_bbs_file_class_sublist fcs
				join da_bbs_file_class_sublist_rel fcsr on fcsr.cItemGuid = fcs.cguid
                join da_bbs_file_class fc on fc.cguid = fcs.cHeadId
                where fc.cguid = ? and fcsr.istatus = 1
                union all
                select fcsd.cname,fcsd.cguid,'3' as ilevel,fcsd.cheadid as cParentId,fcsd.cheadid_name as cParentId_name,'1'as type,
                fcsd.cname as fname,fcsd.ccode
                from da_bbs_file_class_sublist_deta fcsd
                join da_bbs_file_class_sublist fcs on fcs.cguid = fcsd.cHeadId
                join da_bbs_file_class fc on fc.cguid = fcs.cHeadId
                where fc.cguid = ?
            ) a1  order by ccode
        ]]>
        </i>
        <i id="loadTreeFour" desp="加载四级节点">
            <![CDATA[
            select bf.csourcepagename_name as cname,bf.cfiledatatype cguid,'4' as ilevel,bf.cHeadId as cParentId,bf.cHeadId_name as cParentId_name,
            bf.csourcepagename_name as fname,csourcepage_pageid
            from da_bbs_filerelationship bf
            join da_bbs_file_class_sublist_deta fcsd on fcsd.cguid = bf.cHeadId
            where fcsd.cguid = ?;
        ]]>
        </i>
        <i id="getGenPdfData" desp="加载右表数据">
            <![CDATA[
            select f.carchivalid,
                   f.carchivalname,
                   f.cgenfile,
                   f.cguid,
                   e.ctemplatepdfname,
                   f.cadminorgnId
              from da_fc_gen_pdf f
	    left  join da_fc_gen_pdf_line e
			    on f.cGuid = e.cparentid
            where f.carchivalid = {carchivalid}
              and f.cadminorgnId = {cadminorgnId}
              order by e.ctemplatepdfname desc
        ]]>
        </i>
        <i id="getPdfLineList" desp="加载版式生成子表数据">
            <![CDATA[
            select f.ctemplatesortcode,
                   f.ctemplatesortname,
                   f.ctemplatesortid,
                   f.ctemplatepdfcode,
                   f.ctemplatepdfname,
                   f.ctemplatepdfid,
                   f.cguid,
                   f.cparentid,
                   (case f.callin when 1
                        then '全局适用'
                        else f.capplyorgname end) as capplyorgname,
                   f.capplyorgnid
            from da_fc_gen_pdf_line f
            where f.cparentid = {cparentid}
              and f.cadminorgnId = {cadminorgnId}
        ]]>
        </i>

        <i id="getOrganById" desp="根据主键查询当前组织数据">
            <![CDATA[
		   SELECT
	            cguid,
	            cname,
	            ccode,
	            cparentid,
	            istatus,
	            iShareDept,
	            cShareOrgn,
	            ilevel,
	            cInnerCode
           FROM
           	aos_orgn
           WHERE
	         cguid = ?
		]]>
        </i>

        <i id="getOrganByInnercode" desp="获取组织下级的数据">
            <![CDATA[
            SELECT
	            cguid,
	            cname,
	            ccode,
	            cparentid,
           FROM
           	aos_orgn
           WHERE
	         cparentid = ?
		]]>
        </i>

        <i id="getPrintTemplate" desp="版式生成子表过滤打印模板">
            <![CDATA[
		         SELECT
                     pte.cguid AS cguid,
                     pte.ccode AS ccode,
                     pte.cname AS cname
                 FROM
                     aps_print_template pte
                 left JOIN aps_print_type pty ON pty.cguid = pte.ctype where pte.ctype = ?
		]]>
        </i>

        <i id="updateVoucherinfo" desp="更新凭证子表版式生成状态(列表)">
            <![CDATA[
		         update da_fc_voucherinfo set cgenpdfstatus = {cgenpdfstatus}, cnumberoffiles = {cnumberoffiles},isexistfile = {isexistfile} where cvoucherid = {cvoucherid}
		]]>
        </i>
        <i id="updateMdVoucher" desp="更新凭证子表版式生成状态(元数据表)">
            <![CDATA[
		         update da_fc_md_voucher set cgenpdfstatus = {cgenpdfstatus} where cvoucherid = {cvoucherid}
		]]>
        </i>
        <i id="updatemdglbook" desp="更新凭证子表版式生成状态(列表)">
            <![CDATA[
		         update da_fc_md_glbook v set v.cgenpdfstatus = {cgenpdfstatus}  where v.clistguid = {clistguid}
		]]>
        </i>
        <i id="updateaccountbook" desp="更新总账版式生成状态(列表)">
            <![CDATA[
		         update da_fc_accountbook v set v.isexistfile = {isexistfile} , v.cnumberoffiles = {cnumberoffiles},v.cgenpdfstatus = {cgenpdfstatus} where v.cguid = {cguid}
		    ]]>
        </i>
        <i id="updatebilldata" desp="更新单据资料收集列表版式生成状态(列表)">
            <![CDATA[
		         update da_api_fk_file_collection v set  v.ifileqty = {ifileqty},v.cgenpdfstatus = {cgenpdfstatus} where v.cguid = {cguid}
		    ]]>
        </i>
        <i id="updategenpdflog" desp="更新版式生成日志表">
            <![CDATA[
		         update da_fc_gen_pdf_log set cfailreason = {cfailreason},csyserror = {csyserror},coperatedate = {coperatedate},cdataname = {cdataname} where bussinessid = {bussinessid}
		    ]]>
        </i>
        <i id="getPrintTemplateMs" desp="打印模版ms数据过滤">
            <![CDATA[
                select
                    ccode,
                    cname,
                    ctype,
                    cguid,
                    corgnid
                from
                aps_print_template
                where ctype = {ctype}
                  and corgnid is null
		    ]]>
        </i>
        <i id="getNoAllIn" desp="获取适用组织非全局适用的模板数据">
            <![CDATA[
                SELECT
	                *
                 FROM da_fc_gen_pdf_line f
            WHERE ( f.callin = '0' OR f.callin IS NULL )
	          AND $LIKE(f.capplyorgnid,capplyorgnid)
	          AND f.cparentid = {cparentid}
	          AND f.ctemplatesortid = {ctemplatesortid}
	          AND f.ctemplatepdfid = {ctemplatepdfid}
		    ]]>
        </i>
        <i id="getTableName" desp="根据实体id获取表名">
            <![CDATA[
                SELECT
	                tem.cTableCode
                FROM
                    aps_page_pagetemplate tab
                RIGHT JOIN aps_data_resource_table tem ON tem.cguid = tab.cformentityid
                WHERE tab.cguid = {cguid}
		    ]]>
        </i>
        <i id="getccodefromclass" desp="根据明细分类id获取大类编码">
            <![CDATA[
                SELECT
	                bb.ccode
                FROM
	            da_bbs_file_class_sublist flb
	            left join da_bbs_file_class bb
	            on flb.cHeadId = bb.cGuid
	            where flb.cguid = ?
		    ]]>
        </i>
        <i id="getcorgnidccode" desp="根据组织编码">
            <![CDATA[
                select ccode from aos_orgn where cguid = ?
		    ]]>
        </i>

    </sql>
</sqls>
