<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls>
<sql group="da_fc_archiveAction">
    <i id="loadTreeZero" desp="加载根节点">
        <![CDATA[
            select '档案分类' as cname, '#ROOT0000' as cguid,'0' as ilevel
        ]]>
    </i>

    <i id="loadTreeOneTest" desp="加载一级节点">
        <![CDATA[
            select cname,cguid,'1' as ilevel ,'#ROOT0000' as cParentId, '档案分类' as cParentId_name
            from da_bbs_file_class where cParentId = '#ROOT0000'
        ]]>
    </i>

    <i id="loadTreeTwoTest" desp="加载二级节点">
        <![CDATA[
            select cname,cguid,'2' as ilevel ,cParentId,cParentId_name from da_bbs_file_class where cParentId = ?
        ]]>
    </i>

    <i id="loadTreeOne" desp="加载一级节点">
        <![CDATA[
            select '会计档案' as cname, '#ROOT0000' as cguid,'1' as ilevel
        ]]>
    </i>

    <i id="loadTreeTwo" desp="加载二级节点">
        <![CDATA[
            select cname,cguid,ilevel+1 as ilevel ,cParentId,cParentId_name from da_bbs_file_class
        ]]>
    </i>

    <i id="loadTreeThree" desp="加载三级节点">
        <![CDATA[
            select distinct * from (
                select fcs.cname,fcs.cguid,'3' as ilevel,cheadid as cParentId,cheadid_name as cParentId_name,'0'as type,
                fcs.cname as fname,fcs.ccode
                from da_bbs_file_class_sublist fcs
				join da_bbs_file_class_sublist_rel fcsr on fcsr.cItemGuid = fcs.cguid
                join da_bbs_file_class fc on fc.cguid = fcs.cHeadId
                where fc.cguid = ? and fcsr.istatus = 1
                union all
                select fcsd.cname,fcsd.cguid,'3' as ilevel,fcsd.cheadid as cParentId,fcsd.cheadid_name as cParentId_name,'1'as type,
                fcsd.cname as fname,fcsd.ccode
                from da_bbs_file_class_sublist_deta fcsd
                join da_bbs_file_class_sublist fcs on fcs.cguid = fcsd.cHeadId
                join da_bbs_file_class fc on fc.cguid = fcs.cHeadId
                where fc.cguid = ?
            ) a1  order by ccode
        ]]>
    </i>
    <i id="loadTreeFour" desp="加载四级节点">
        <![CDATA[
            select bf.csourcepagename_name as cname,bf.cguid,'4' as ilevel,bf.cHeadId as cParentId,bf.cHeadId_name as cParentId_name,
            bf.csourcepagename_name as fname,csourcepage_pageid
            from da_bbs_filerelationship bf
            join da_bbs_file_class_sublist_deta fcsd on fcsd.cguid = bf.cHeadId
            where fcsd.cguid = ?
        ]]>
    </i>
    <i id="getUpstreamBills" desp="根据父单据id获取上游单据数据">
        <![CDATA[
            select cguid,csourcepagename_name as cname,cbilltypename,cHeadId,cHeadId_name, '1' as type,
            cguid as cupstreamid,csourcepage_pageid
            from da_bbs_filerelationshipline bf
            where bf.cHeadId = ?
        ]]>
    </i>
    <i id="getRightTableList" desp="根据父id获取右表数据">
        <![CDATA[
            select fsa.*,(
                select cprint_template_name
                from da_fc_sync_archive_line fsal
                where fsal.cHeadId = fsa.cguid
                and (fsal.capplicability like concat('%',?,'%') or fsal.capplicability = 'GF' )
                order by capplicability
                limit 1
            ) as cprint_template_name
             from da_fc_sync_archive fsa where fsa.cParentId = ? and fsa.cAdminOrgnId = ?
        ]]>
    </i>
    <i id="getPrintTempLate" desp="根据父id获取打印模板数据">
        <![CDATA[
            select * from da_fc_sync_archive_line fsal where fsal.cHeadId = ?
        ]]>
    </i>

    <i id="selfAndLowerLevel" desp="获取组织本级以及下级的所有数据id">
        <![CDATA[
		    select cguid,cname,cparentid,istatus,iShareDept,cShareOrgn
            from aos_orgn,(
                select @ids as _ids,
                (select @ids := group_concat(cguid) from aos_orgn where find_in_set(cparentid,@ids)) as cid
                from aos_orgn,(select @ids:= {orgnId}) b
                where @ids is not null
                ) ta
            where find_in_set(cguid,ta._ids) and iStatus = 1
		]]>
    </i>

    <i id="selfAndLowerLevelByLevel" desp="获取组织本级以及下级的数据id(只递归一层)">
        <![CDATA[
            select cguid,cname,cparentid,istatus,iShareDept,cShareOrgn
            from aos_orgn,(
                select @ids as _ids,
                (select @ids := group_concat(cguid) from aos_orgn where find_in_set(cparentid,@ids)) as cid
                from aos_orgn,(select @ids:= {orgnId}) b
                where @ids is not null
                ) ta
            where find_in_set(cguid,ta._ids) and iStatus = 1 and ilevel <= {ilevel}
			and if(cguid != {orgnId},cguid!=cAdminOrgnID,1=1)
		]]>
    </i>

    <i id="selfAndUpLevel" desp="获取组织本级以及上级的所有数据id">
        <![CDATA[
		    select t2.cname,t2.cguid from
            (select
                @r AS _id,
                (select @r := cParentId from aos_orgn where cguid = _id limit 1 ) AS cParentId,
                @s := @s + 1 AS sort
                from (select @r := {orgnId}, @s := 0) temp,aos_orgn
                where @r > 0 ) t1
            join aos_orgn t2 on t1._id = t2.cguid
		]]>
    </i>

    <i id="printTemplateByOrgn" desp="获取打印模板数据根据页面id和组织进行查询">
        <![CDATA[
		    select distinct * from v_da_fc_print_template pt
            where #equal(pt.cbillguid,cpage_template_id) and
            (isystem = 1 or $in(pt.cOrgnId,corgnidList))
		]]>
    </i>

    <i id="getOrganById" desp="根据主键查询组织数据">
        <![CDATA[
		   select cguid,cname,ccode,cparentid,istatus,iShareDept,cShareOrgn,ilevel from aos_orgn
           where cguid = ?
		]]>
    </i>

    <i id="getOrganByListId" desp="查询多个组织数据">
        <![CDATA[
		   select * from aos_orgn aos
           where $in(aos.cguid,guidList)
		]]>
    </i>

    <i id="getPrintTemplateById" desp="根据主键查询系统应用下打印模板数据">
        <![CDATA[
          select pte.cguid AS cguid,pte.ccode AS ccode,
          pte.cname AS cname, pte.iSystem AS iSystem,pte.cOrgnId AS cOrgnId,
          pte.cshare AS cshare, pte.ienvironmentsign AS ienvironmentsign
          from aps_print_template pte
          where pte.cguid = ?
		]]>
    </i>

    <i id="getHeadId" desp="查询子表中的父id">
        <![CDATA[
          select cheadid from da_fc_sync_archive_line where cguid = ?
		]]>
    </i>

    <i id="getAllSub" desp="根据父id查询所有的打印模板，但不包含自身">
        <![CDATA[
          select cguid from da_fc_sync_archive_line where cheadid = ? and cguid != ?
		]]>
    </i>

    <i id="getPrintTemplateCodeOrName" desp="获取打印模板的名称和编码，来源是打印模板设计">
        <![CDATA[
           select
               ccode as cprint_template_code,
               cname as cprint_template_name,
               cguid as cprint_template
           from aps_print_template pt where pt.cguid = ?
		]]>
    </i>

    <i id="getArchiveGuidByAdminOrgan" desp="查询同步归档的管理组织下面的所有主键">
        <![CDATA[
           select cguid from da_fc_sync_archive where cadminorgnid = ?
		]]>
    </i>

    <i id="getPrintTempLateField" desp="根据父id获取打印模板数据，显示一些需要的字段">
        <![CDATA[
            select
            cprint_template,
            cprint_template_name,
            cprint_template_code,
            capplicability,
            capplicability_name
            from da_fc_sync_archive_line fsal where fsal.cHeadId = ?
        ]]>
    </i>

    <i id="getPrintTemplateByTemplateId" desp="查询同步归档子表是否存在查询的打印模板">
        <![CDATA[
            select
            cprint_template,
            cprint_template_name,
            cprint_template_code
            from da_fc_sync_archive_line fsal where fsal.cprint_template = ?
        ]]>
    </i>

    <i id="getTablePrintName" desp="获取右表显示的打印模板名称">
        <![CDATA[
            select cprint_template_name
            from da_fc_sync_archive_line fsal
            where fsal.cHeadId = ?
            and fsal.capplicability like concat('%',?,'%')
            order by capplicability
            limit 1
        ]]>
    </i>

    <i id="isCitePrintTemplate" desp="判断管理组织中是否存在其他引用打印模板的数据">
        <![CDATA[
            select cprint_template from da_fc_sync_archive_line
            where cAdminOrgnId = ? and cprint_template = ?
            and cguid != ?
        ]]>
    </i>

    <i id="getPrintTemplateByLineGuid" desp="获取同步归档子表打印模板数据根据子表主键">
        <![CDATA[
            select * from da_fc_sync_archive_line
            where cguid = ?
        ]]>
    </i>

    <!-- 方法名是获取二级节点，但由于是提供服务的sql，不把会计档案当成最大的一级节点 -->
    <i id="getTreeTwo" desp="获取二级节点id">
        <![CDATA[
            select fcs.cguid
            from da_bbs_file_class_sublist fcs
            join da_bbs_file_class fc on fc.cguid = fcs.cHeadId
            where fc.cguid = ?
        ]]>
    </i>
    <!-- 获取同步归档的数据，因为可能获取多个,单独另写一个sql -->
    <i id="getTableList" desp="根据父id获取三级节点对应数据">
        <![CDATA[
            select cguid,carchive_data,cdata_attribute,sync_archive,redcyan_archive,layout_file,print_style,cpage_template_id as cpageid,cparentid,cparentid_name
            from da_fc_sync_archive fsa
            where #in(fsa.cParentId,idList) and $equal(fsa.cAdminOrgnId,cadminorgnId)
        ]]>
    </i>

    <!--获取同步归档数据，用于对接系统内部-->
    <i id="getTableListSystem" desp="根据父id获取三级节点对应数据">
        <![CDATA[
            select carchive_data,sync_archive,layout_file,cparentid_name
            from da_fc_sync_archive fsa
            where #in(fsa.cParentId,idList) and $equal(fsa.cAdminOrgnId,cadminorgnId)
        ]]>
    </i>

    <!-- 获取的是一级节点id,不把"会计档案"当成最大的一级节点 -->
    <i id="getTreeOneByCode" desp="根据编码获取对应的id">
        <![CDATA[
           select cguid from da_bbs_file_class where ccode = ?
        ]]>
    </i>
    <!-- 获取的是二级节点id,不把"会计档案"当成最大的一级节点 -->
    <i id="getTreeTwoByCode" desp="根据编码获取对应的id">
        <![CDATA[
           select cguid from da_bbs_file_class_sublist where #in(ccode,codeList)
        ]]>
    </i>
    <!-- 获取的是三级节点id,不把"会计档案"当成最大的一级节点 -->
    <i id="getTreeThreeByCode" desp="根据编码获取对应的id">
        <![CDATA[
           select cguid from da_bbs_file_class_sublist_deta where ccode = ?
        ]]>
    </i>

    <i id="getDetailInfoByGuid" desp="根据id获取明细分类信息">
        <![CDATA[
           select * from da_bbs_file_class_sublist where cguid = ?
        ]]>
    </i>

    <i id="getApplyInfoByGuid" desp="根据id获取应用信息">
        select * from aps_application a where a.cguid = ?
    </i>

</sql>
</sqls>
