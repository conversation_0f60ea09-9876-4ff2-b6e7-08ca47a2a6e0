<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls>
<sql group="da_fc_dataretrieval">
    <i id="invoicelist" desp="票据资料列表">
        <![CDATA[
            SELECT
                  CASE
                       WHEN dfv.cfilenum IS NOT NULL AND dfv.cfilenum != '' THEN dfv.cfilenum
                       ELSE di.cfilenum
                  END AS ieastatus_cfilenum,
                  CASE
                       WHEN dfv.ceastatus IS NOT NULL AND dfv.ceastatus != '' THEN dfv.ceastatus
                       ELSE di.ceastatus_name
                  END AS ieastatus_name,
                  CASE
                       WHEN dfv.ieastatus IS NOT NULL AND dfv.ieastatus != '' THEN dfv.ieastatus
                       ELSE di.ceastatus
                  END AS ieastatus,
				  di.*
                FROM
                  da_invoice di
                  left join da_invoiceline dil on dil.cheadguid = di.cguid and dil.corgnid = di.corgnid
                  left join da_fc_voucherinfo dfv on dfv.cOrgnId = di.cOrgnId and dfv.cvoucherid = di.cvoucherid
                where
                  1=1
        ]]>
    </i>
</sql>
</sqls>