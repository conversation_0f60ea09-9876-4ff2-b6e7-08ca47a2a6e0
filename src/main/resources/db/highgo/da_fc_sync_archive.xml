<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls>
<sql group="da_fc_archiveAction">

    <i id="selfAndLowerLevel" desp="获取组织本级以及下级的所有数据id">
        <![CDATA[
		    with recursive temp_table as (
                select cguid,cname,cparentid,istatus from aos_orgn where cparentid = {orgnId}
                union
                select a.cguid, a.cname ,a.cparentid,a.istatus from aos_orgn a,temp_table b where a.cparentid = b.cguid
            ) select * from temp_table where istatus = 1
            union all
            select cguid,cname,cparentid,istatus from aos_orgn where cguid = {orgnId}
		]]>
    </i>

    <i id="selfAndLowerLevelByLevel" desp="获取组织本级以及下级的数据id(只递归一层)">
        <![CDATA[
           with recursive temp_table as (
                select cguid,cname,cparentid,istatus,ilevel,cAdminOrgnID from aos_orgn where cparentid = {orgnId}
                union
                select a.cguid, a.cname ,a.cparentid,a.istatus,a.ilevel,a.cAdminOrgnID from aos_orgn a,temp_table b where a.cparentid = b.cguid
            ) select * from temp_table where istatus = 1 and ilevel <= {ilevel} and cguid!=cAdminOrgnID
            union all
            select cguid,cname,cparentid,istatus,ilevel,cAdminOrgnID from aos_orgn where cguid = {orgnId}
		]]>
    </i>

    <i id="selfAndUpLevel" desp="获取组织本级以及上级的所有数据id">
        <![CDATA[
		    with recursive temp_table as (
                select cguid,cname,cparentid,istatus from aos_orgn where cguid = {orgnId}
                union
                select a.cguid, a.cname,a.cparentid,a.istatus from aos_orgn a,temp_table b where a.cguid = b.cparentid
            ) select * from temp_table
		]]>
    </i>
</sql>
</sqls>
