<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls>
    <sql group="da_fc_template_sql">
        <i id="getFszlUpdateSuaiSql" desp="获取附属分类资料修改四项检测状态sql">
            <![CDATA[
                  UPDATE tableName SET csuaistatus = '未检测',isuaistatus = '0'
                  WHERE sybillid = ? AND ( cvoucherid IS NULL OR (cvoucherid IS NOT NULL and cvoucherid = ?))
            ]]>
        </i>
        <i id="getFsFileData" desp="获取单据关联资料数据">
            <![CDATA[
               SELECT *  FROM tableName
               WHERE sybillid = ? AND ( cvoucherid IS NULL OR (cvoucherid IS NOT NULL and cvoucherid = ?)) and cserialnumber= ?
               AND corgnid = ?
            ]]>
        </i>
        <i id="getFsFileDataByDataSource" desp="获取单据关联资料数据">
            <![CDATA[
               SELECT *  FROM tableName
               WHERE sybillid = ? AND ( cvoucherid IS NULL OR (cvoucherid IS NOT NULL and cvoucherid = ?))  and datasource = ?
               AND corgnid = ?
            ]]>
        </i>
        <i id="getBillListQuerySql" desp="获取单据关联资料数据">
            <![CDATA[
               SELECT *  FROM tableName
               queryItem orderByItem;
            ]]>
        </i>
        <i id="getDeleteFsFileData" desp="删除单据关联资料数据">
            <![CDATA[
                DELETE FROM tableName
                WHERE (sybillid = ? or billid = ?) AND ( cvoucherid IS NULL OR (cvoucherid IS NOT NULL and cvoucherid = ?)) and cserialnumber= ?
                AND corgnid = ?
            ]]>
        </i>
        <i id="getDeleteFsFileDataByDataSource" desp="删除单据关联资料数据">
            <![CDATA[
                DELETE FROM tableName
                WHERE (sybillid = ? or billid = ?) AND ( cvoucherid IS NULL OR (cvoucherid IS NOT NULL and cvoucherid = ?)) and datasource= ?
                AND corgnid = ?
            ]]>
        </i>
        <i id="getUpdateVoucherSql" desp="获取电子档案分类预置子表中数据以及相关页面的访问url">
            <![CDATA[
                UPDATE da_fc_voucherinfo
                SET  csuaistatus = '未检测',isuaistatus = '0'
                WHERE  cvoucherid = ?;
            ]]>
        </i>
        <i id="deleteFile" desp="获取电子档案分类预置子表中数据以及相关页面的访问url">
            <![CDATA[
            DELETE FROM cfilesTableName
            WHERE cbillid=? and ( cvoucherid is null or (cvoucherid is not null and cvoucherid = ?));
            ]]>
        </i>
        <i id="fkDataDelete" desp="获取电子档案分类预置子表中数据以及相关页面的访问url">
            <![CDATA[
            DELETE t1,t2
            FROM tableName t1
            LEFT JOIN childrenTableName t2  ON t2.cheadid = t1.cGuid
            WHERE t1.oldcguid = ? AND ( cvoucherid IS NULL OR (t1.cvoucherid IS NOT NULL and t1.cvoucherid = ?));
                  ]]>
        </i>
        <i id="getDataByVoucherId" desp="根据凭证id获取数据">
            <![CDATA[
            SELECT *
            FROM
            tableName
            WHERE
            cvoucherid = ?
            AND cOrgnId = ?
            AND cserialnumber = ?
            AND biscollectwithvou  = 1
                   ]]>
        </i>
        <i id="getQuerySql" desp="根据凭证id获取数据">
            <![CDATA[
            SELECT *
            FROM
            tableName
            WHERE
            cvoucherid = ?
            AND cOrgnId = ?

                   ]]>
        </i>
        <i id="getDataByVoucherIdDatasource" desp="根据凭证id获取数据">
            <![CDATA[
            SELECT *
            FROM
            tableName
            WHERE
            cvoucherid = ?
            AND cOrgnId = ?
            AND datasource = ?
            AND biscollectwithvou  = 1
                   ]]>
        </i>
        <i id="deleteDataByVoucherId" desp="根据凭证id删除数据">
            <![CDATA[
            DELETE
            FROM
            tableName
            WHERE
            cvoucherid = ?
            AND cOrgnId = ?
            AND datasource = ?
            AND biscollectwithvou  = 1
             ]]>
        </i>
        <i id="deleteDataByVoucherIdNotSource" desp="根据凭证id删除数据">
            <![CDATA[
            DELETE
            FROM
            tableName
            WHERE
            cvoucherid = ?
            AND cOrgnId = ?
                   ]]>
        </i>
        <i id="deleteStatementsbill" desp="根据凭证id删除数据">
            <![CDATA[
            DELETE t1,t2,t3
            FROM
            da_tt_statementsbill t1
            LEFT JOIN da_tt_statementsbill_detail t2 on t1.cguid = t2.cheadid
            LEFT JOIN da_tt_expensebill_share t3 on t1.cguid = t3.cheadid
            WHERE t1.oldcguid = ? AND ( cvoucherid IS NULL OR (t1.cvoucherid IS NOT NULL and t1.cvoucherid = ?))
            AND t1.corgnid = ?
           ]]>
        </i>
        <i id="deletebillChildren" desp="删除子表数据">
            <![CDATA[
            DELETE t1,t2
            FROM
            tableName t1
            LEFT JOIN childrenTableName t2 on t1.cguid = t2.cheadid
            WHERE t1.oldcguid = ? AND ( cvoucherid IS NULL OR (t1.cvoucherid IS NOT NULL and t1.cvoucherid = ?))
            AND t1.corgnid = ?
           ]]>
        </i>

        <i id="getvoucherdata" desp="获取凭证主表数据支持模板打印"><![CDATA[
            SELECT
	            da_fc_md_voucher1.cperioddate AS '会计期间',
	            da_fc_md_voucher1.icredittotal AS '贷方金额合计',
	            da_fc_md_voucher1.idebittotal AS '借方金额合计',
	            da_fc_md_voucher1.cvoucode AS '凭证编号',
	            da_fc_md_voucher1.clistguid AS '列表主键',
	            da_fc_md_voucher1.csourceorganname AS '来源组织',
	            da_fc_md_voucher1.csourceorganid AS '来源组织id',
	            da_fc_md_voucher1.cvoucherid AS '凭证id',
	            da_fc_md_voucher1.cvoucherdate AS '凭证日期',
	            da_fc_md_voucher1.cvoucreatorname AS '制单人',
	            da_fc_md_voucher1.creviewer AS '复核人',
	            da_fc_md_voucher1.cauditorname AS '审核人',
	            da_fc_md_voucher1.cpostername AS '记账人',
	            da_fc_md_voucher1.cvoutypename AS '凭证类型',
	            da_fc_md_voucher1.iaffix AS '附单数',
	            da_fc_md_voucher1.csummary AS '摘要',
	            da_fc_md_voucher1.cpostdate AS '记账日期',
	            da_fc_md_voucher1.cgenpdfstatus AS '版式生成状态(0未生成，1已生成)',
	            da_fc_md_voucher1.cguid AS '主键',
	            da_fc_md_voucher1.ctemplateid AS '页面模板id',
	            da_fc_md_voucher1.ccreatedate AS '创建时间',
	            da_fc_md_voucher1.ctimestamp AS '时间戳',
	            da_fc_md_voucher1.corgnid AS '组织id',
	            da_fc_md_voucher1.ccreatorid AS '创建人',
	            da_fc_md_voucher1.cadminorgnid AS '管理组织id',
	            da_fc_md_voucher1.cpageid AS '页面id',
	            da_fc_md_voucher1.corgnid_name AS '组织名称',
	            da_fc_md_voucher1.cadminorgnid_name AS '管理组织名称',
	            da_fc_md_voucher1.ccreatorid_name AS '创建人名称',
	            da_fc_md_voucher1.cpageid_name AS '页面名称',
	            da_fc_md_voucher1.ctemplateid_name AS '页面模板名称',
	            da_fc_md_voucher1.cstatus AS '单据状态',
	            da_fc_md_voucher1.cbillcode AS '单据编号',
	            da_fc_md_voucher1.cstatus_name AS '单据状态名称',
	            da_fc_md_voucher1.*
            FROM
            	da_fc_md_voucher da_fc_md_voucher1
            WHERE
            	da_fc_md_voucher1.clistguid =?
        ]]></i>
        <i id="getvoucherLinedata" desp="获取凭证子表数据支持模板打印"><![CDATA[
            SELECT
	           da_fc_md_voucherz1.cdescribe3 AS '附加描述3',
	           da_fc_md_voucherz1.cdescribe2 AS '附加描述2',
	           da_fc_md_voucherz1.cdescribe1 AS '附加描述1',
	           da_fc_md_voucherz1.icreditamt AS '贷方金额',
	           da_fc_md_voucherz1.idebitamt AS '借方金额',
	           da_fc_md_voucherz1.iexchangerate AS '汇率',
	           da_fc_md_voucherz1.ccurname AS '币种',
	           da_fc_md_voucherz1.iamt_f AS '原币',
	           da_fc_md_voucherz1.iunitprice AS '单价',
	           da_fc_md_voucherz1.cuomname AS '单位',
	           da_fc_md_voucherz1.iqty AS '数量',
	           da_fc_md_voucherz1.cacctstr AS '科目',
	           da_fc_md_voucherz1.csummary AS '摘要',
	           da_fc_md_voucherz1.cvoucherline AS '分录id',
	           da_fc_md_voucherz1.cvoucherid AS '凭证id',
	           da_fc_md_voucherz1.cheadid AS '主表主键',
	           da_fc_md_voucherz1.clistguid AS '列表主键',
	           da_fc_md_voucherz1.cvouassisitstr AS '辅助类型+辅助值',
	           da_fc_md_voucherz1.cmatchreceiptstatus AS '匹配回单状态',
	           da_fc_md_voucherz1.cmatchreceiptstatus_name AS '匹配回单状态_名称',
	           da_fc_md_voucherz1.cperioddate AS '会计期间',
	           da_fc_md_voucherz1.cvoutypename AS '凭证类型',
	           da_fc_md_voucherz1.cvoucode AS '凭证号',
	           da_fc_md_voucherz1.cvoucherdate AS '凭证日期',
	           da_fc_md_voucherz1.cvoucreatorname AS '制单人',
	           da_fc_md_voucherz1.cauditorname AS '审核人',
	           da_fc_md_voucherz1.cpostername AS '记账人',
	           da_fc_md_voucherz1.creviewer AS '复核人',
	           da_fc_md_voucherz1.cdeptstr AS '部门',
	           da_fc_md_voucherz1.cempstr AS '职员',
	           da_fc_md_voucherz1.cprojectstr AS '项目',
	           da_fc_md_voucherz1.csupstr AS '供应商',
	           da_fc_md_voucherz1.ccuststr AS '客户',
	           da_fc_md_voucherz1.cbankaccountstr AS '银行账号',
	           da_fc_md_voucherz1.cmatestr AS '物料',
	           da_fc_md_voucherz1.cguid AS '主键',
	           da_fc_md_voucherz1.ctemplateid AS '页面模板id',
	           da_fc_md_voucherz1.ccreatedate AS '创建时间',
	           da_fc_md_voucherz1.ctimestamp AS '时间戳',
	           da_fc_md_voucherz1.corgnid AS '组织id',
	           da_fc_md_voucherz1.ccreatorid AS '创建人',
	           da_fc_md_voucherz1.cadminorgnid AS '管理组织id',
	           da_fc_md_voucherz1.cpageid AS '页面id',
	           da_fc_md_voucherz1.corgnid_name AS '组织名称',
	           da_fc_md_voucherz1.cadminorgnid_name AS '管理组织名称',
	           da_fc_md_voucherz1.ccreatorid_name AS '创建人名称',
	           da_fc_md_voucherz1.cpageid_name AS '页面名称',
	           da_fc_md_voucherz1.ctemplateid_name AS '页面模板名称',
	           da_fc_md_voucherz1.cisbgentry AS '是否预算分录',
	           da_fc_md_voucherz1.*
            FROM
            	da_fc_md_voucherz da_fc_md_voucherz1
            WHERE
            	da_fc_md_voucherz1.clistguid =?
        ]]></i>

        <i id="getacctledgerdata" desp="获取总账数据支持模板打印"><![CDATA[
            SELECT
	           v_da_afk_gl_perset_config1.jiefang AS '借方',
	           v_da_afk_gl_perset_config1.daifang AS '贷方',
	           v_da_afk_gl_perset_config1.ciinitbalanceamtdirection AS '期初余额方向',
	           v_da_afk_gl_perset_config1.iperioddebitamt AS '本期借方金额',
	           v_da_afk_gl_perset_config1.iyearaccumulateddebitamt AS '本年累计借方',
	           v_da_afk_gl_perset_config1.clistguid AS '列表主键',
	           v_da_afk_gl_perset_config1.cheadid AS '主表主键',
	           v_da_afk_gl_perset_config1.cbalancedirection AS '余额方向',
	           v_da_afk_gl_perset_config1.ibalanceamt AS '余额金额',
	           v_da_afk_gl_perset_config1.cacctcode AS '科目编码',
	           v_da_afk_gl_perset_config1.cacctname AS '科目名称',
	           v_da_afk_gl_perset_config1.cperioddate AS '期间',
	           v_da_afk_gl_perset_config1.iinitbalanceamt AS '期初余额',
	           v_da_afk_gl_perset_config1.iperiodcreditamt AS '本期贷方金额',
	           v_da_afk_gl_perset_config1.iyearaccumulatedcreditamt AS '本年累计贷方',
	           v_da_afk_gl_perset_config1.ccurname AS '币种',
	           v_da_afk_gl_perset_config1.cyear AS '年度',
	           v_da_afk_gl_perset_config1.cguid AS '主键',
	           v_da_afk_gl_perset_config1.ctemplateid AS '页面模板id',
	           v_da_afk_gl_perset_config1.ccreatedate AS '创建时间',
	           v_da_afk_gl_perset_config1.ctimestamp AS '时间戳',
	           v_da_afk_gl_perset_config1.corgnid AS '组织id',
	           v_da_afk_gl_perset_config1.ccreatorid AS '创建人',
	           v_da_afk_gl_perset_config1.cadminorgnid AS '管理组织id',
	           v_da_afk_gl_perset_config1.cpageid AS '页面id',
	           v_da_afk_gl_perset_config1.corgnid_name AS '组织名称',
	           v_da_afk_gl_perset_config1.cadminorgnid_name AS '管理组织名称',
	           v_da_afk_gl_perset_config1.ccreatorid_name AS '创建人名称',
	           v_da_afk_gl_perset_config1.cpageid_name AS '页面名称',
	           v_da_afk_gl_perset_config1.ctemplateid_name AS '页面模板名称',
	           v_da_afk_gl_perset_config1.csummary AS '摘要'
           FROM
	           v_da_afk_gl_perset_config v_da_afk_gl_perset_config1
           WHERE
	           v_da_afk_gl_perset_config1.clistguid =?
        ]]></i>

        <i id="getLayoutGenLog" desp="查询版式生成日志">
            <![CDATA[
             select * from da_fc_gen_pdf_log l where substr(l.coperatedate,0,10) = ? order by l.l.coperatedate desc
           ]]>
        </i>
        <i id="getvoucherinfodata" desp="查询凭证版式生成状态,电子归档状态,实体组卷状态">
            <![CDATA[
             select l.cgenpdfstatus,l.cguid,l.cdetailclassguid,l.cvoucode as '凭证字号',l.ieastatus,l.centityfilestatus,l.cperioddate as '会计期间',l.cintegritystatus  from da_fc_voucherinfo l where l.cguid = ?
           ]]>
        </i>
        <i id="getacctinfodata" desp="查询总账版式生成状态">
            <![CDATA[
             SELECT
	             l.cgenpdfstatus,
	             l.cguid,
	             k.cdetailclassguid,
	             l.clistguid as '列表主键',
				 ifnull(l.cyear,left(l.cperiod,4)) as '年度',
				 l.cperiod as '期间',
				 k.ieastatus,
				 k.centityfilestatus
             FROM
	             da_fc_md_glbook l
	         LEFT JOIN da_fc_accountbook k ON l.clistguid = k.cGuid
             WHERE l.clistguid = ?
           ]]>
        </i>
        <i id="getctemplatepdfid" desp="查询版式生成状态">
            <![CDATA[
             SELECT
                 	n.ctemplatepdfid,
                 	n.callin,
                 	f.cgenfile
                 FROM
                 	da_fc_gen_pdf f
                 left join da_fc_gen_pdf_line n
                   on f.cGuid = n.cparentid
                 where f.carchivalid = {carchivalid}
                   and f.cAdminOrgnId = {cAdminOrgnId}
                   AND $like(n.capplyorgnid,capplyorgnid)
           ]]>
        </i>
    </sql>
</sqls>
