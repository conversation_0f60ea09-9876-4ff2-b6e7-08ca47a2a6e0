<?xml version="1.0" encoding="UTF-8"?>
<sqls>
    <sql group="da_file_collect_config_sql">
        <i id="getDetailPageIdByCode" desp="根据名字获取页面Id"><![CDATA[
            SELECT
            dc.cguid ,pt.cpageid, dc.details_template
            FROM
            da_bbs_data_class dc
            LEFT JOIN
            aps_page_pagetemplate pt
            ON
            dc.details_template = pt.cguid
            WHERE
            #equal(dc.ccode,code) LIMIT 0,1
        ]]></i>
        <i id="getListPageId" desp="获取列表页面Id"><![CDATA[
            SELECT
            dc.ccode,pt.cguid ,pt.cpageid,dc.cname,false as bleaf,dc.cguid as cid ,null as children
            FROM
            da_bbs_data_class dc
            LEFT JOIN
            aps_page_pagetemplate pt
            ON
            dc.data_collectlist_template = pt.cguid
            WHERE preset = 1
        ]]></i>
    </sql>
</sqls>