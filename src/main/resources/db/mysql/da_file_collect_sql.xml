<?xml version="1.0" encoding="UTF-8"?>
<sqls>
    <sql group="da_invoice_collect_sql">
        <i id="queryByIds" desp="根据id查询票据资料数据"><![CDATA[
            SELECT
            *
            FROM
            da_invoice
            WHERE
            #in(cguid,ids);
        ]]></i>
        <i id="batchDelete" desp="批量删除票据资料主表数据"><![CDATA[
            DELETE
            FROM
            da_invoice
            WHERE
            #in(cguid,ids);
        ]]></i>
        <i id="batchDeleteDetail" desp="批量删除票据资料子表数据"><![CDATA[
            DELETE
            FROM
            da_invoiceline
            WHERE
            #in(cheadguid,ids);
        ]]></i>
        <i id="deleteRelByInvoice" desp="删除关联"><![CDATA[
            DELETE FROM DA_FVIEW_VOUANDBILLRELA WHERE CMEANSID = ? AND (CVOUCHERID = ? or CVOUCHERID is null )  AND  CTYPE = ?
        ]]></i>
        <i id="deleteFileByInvoice" desp="根据票据id删除文件"><![CDATA[
           delete from cfilesTableName  where invoiceid=? and cOrgnId = ? and cvoucherid = ?
        ]]></i>
    </sql>
    <sql group="da_bill_collect_sql">
        <i id="updateSuaiById" desp="根据id查询单据资料数据"><![CDATA[
            UPDATE da_api_fk_file_collection
            SET csuaistatus = '未检测'
            ,isuaistatus = '0'
            WHERE
            #equal(cguid,cguid)
        ]]></i>
        <i id="queryById" desp="根据id查询单据资料数据"><![CDATA[
            SELECT
            *
            FROM
            da_api_fk_file_collection
            WHERE
            #equal(cguid,cguid);
        ]]></i>
        <i id="queryByIds" desp="根据id查询单据资料数据"><![CDATA[
            SELECT
            *
            FROM
            da_api_fk_file_collection
            WHERE
            #in(cguid,ids);
        ]]></i>
        <i id="batchDelete" desp="批量删除单据资料主表数据"><![CDATA[
            DELETE
            FROM
            da_api_fk_file_collection
            WHERE
            #in(cguid,ids);
        ]]></i>
        <i id="batchDeletePayBill" desp="批量删除支付单主表以及子表数据"><![CDATA[
           DELETE pb,pbd
           FROM
	       da_fk_paybill pb
	       LEFT JOIN da_fk_paybill_detail pbd ON pbd.cheadid = pb.cGuid
           WHERE
	       #in(pb.oldguid,ids)
	       AND #equal(pb.corgnid,corgnid);
        ]]></i>
        <i id="batchDeleteRePayBill" desp="批量删除还款单主表以及子表数据"><![CDATA[
           DELETE prb,prbd
           FROM
	       da_fk_repaybill prb
	       LEFT JOIN da_fk_repaybill_detail prbd ON prbd.cheadid = prb.cGuid
           WHERE
	       #in(prb.oldguid,ids)
           AND #equal(prb.corgnid,corgnid);
       ]]></i>
        <i id="batchDeleteExpenseBill" desp="批量删除报销单主表以及子表数据"><![CDATA[
            DELETE eb,ebd
            FROM
	        da_fk_expensebill eb
	        LEFT JOIN da_fk_expensebill_detail ebd ON ebd.cheadid = eb.cGuid
            WHERE
	        #in(eb.oldguid,ids)
            AND #equal(eb.corgnid,corgnid);
        ]]></i>
        <i id="batchDeleteApplyBill" desp="批量删除申请单主表以及子表数据"><![CDATA[
            DELETE ab,abd
            FROM
            da_fk_applybill ab
	        LEFT JOIN da_fk_applybill_detail abd ON abd.cheadid = ab.cGuid
            WHERE
	        #in(ab.oldguid,ids)
            AND #equal(ab.corgnid,corgnid);

        ]]></i>
        <i id="batchDeleteCreditBill" desp="批量删除挂账付款单主表以及子表数据"><![CDATA[
            DELETE cb,cbd
            FROM
	        da_fk_creditbill cb
	        LEFT JOIN da_fk_creditbill_detail cbd ON cbd.cheadid = cb.cGuid
            WHERE
	        #in(cb.oldguid,ids)
            AND #equal(cb.corgnid,corgnid);
        ]]></i>
        <i id="batchDeleteAdvancePayBill" desp="批量删除预付款单主表以及子表数据"><![CDATA[
            DELETE ab,abd
            FROM
	        da_fk_advancepaybill ab
	        LEFT JOIN da_fk_advancepaybill_detail abd ON abd.cheadid = ab.cGuid
            WHERE
	        #in(ab.oldguid,ids)
            AND #equal(ab.corgnid,corgnid);
        ]]></i>
        <i id="batchDeleteLoanBill" desp="批量删除借款单主表以及子表数据"><![CDATA[
            DELETE lb,lbd
            FROM
	        da_fk_loanbill lb
	        LEFT JOIN da_fk_loANDetail lbd ON lbd.cheadid = lb.cGuid
            WHERE
	        #in(lb.oldguid,ids)
            AND #equal(lb.corgnid,corgnid);
        ]]></i>
        <i id="getMainDataByVoucherId" desp="根据凭证id查询主单据"><![CDATA[
            SELECT *
            FROM
            da_api_fk_file_collection
            WHERE
            #equal(cvoucherid,cvoucherid)
            AND #equal(cOrgnId,corgnId)
            AND #equal(datasource,datasource)
            AND ishead = 1
        ]]></i>
        <i id="getBillDataByVoucherId" desp="根据凭证id查询单据"><![CDATA[
            SELECT *
            FROM
            da_api_fk_file_collection
         WHERE
            #equal(cvoucherid,cvoucherid)
            AND #equal(cOrgnId,corgnId)
            AND #equal(datasource,datasource)
            AND biscollectwithvou = 1
        ]]></i>
        <i id="deleteBillAndVoucher" desp="删除关联表"><![CDATA[
           delete  from da_fc_billandvoucherrel where  cvoucherid = ? and cbillid = ? and  cserialnumber = ? AND corgnid =?
        ]]></i>
        <i id="deleteBillAndVoucherNotSerialnumber" desp="删除关联表"><![CDATA[
           delete  from da_fc_billandvoucherrel where  cvoucherid = ? and cbillid = ? and corgnid =?
        ]]></i>
        <i id="deleteBillAndVoucherTemp" desp="删除关联临时表"><![CDATA[
           delete  from  da_fc_billandvoucherrel_temp where  cvoucherid = ? and cbillid = ?
        ]]></i>
        <i id="deleteBillAndVoucherIsNull" desp="删除关联表"><![CDATA[
           delete  from da_fc_billandvoucherrel where  cvoucherid is null  and cbillid = ? and  cserialnumber = ? and corgnid = ?
        ]]></i>
        <i id="deleteBillAndVoucherIsNullNotSerialnumber" desp="删除关联表"><![CDATA[
           delete  from da_fc_billandvoucherrel where  cvoucherid is null  and cbillid = ? and corgnid = ?
        ]]></i>
        <i id="deleteBillAndVoucherTempIsNull" desp="删除关联临时表"><![CDATA[
           delete  from  da_fc_billandvoucherrel_temp where  cvoucherid is null and cbillid = ?
        ]]></i>
        <i id="deleteBillAndVoucherByVoucher" desp="删除关联表"><![CDATA[
           delete  from da_fc_billandvoucherrel where  cvoucherid  = ? and cserialnumber = ?
        ]]></i>
        <i id="deleteBillAndVoucherTempByVoucher" desp="删除关联临时表"><![CDATA[
           delete  from  da_fc_billandvoucherrel_temp where  cvoucherid  = ?
        ]]></i>
    </sql>
    <sql group="da_banksreceipt_collect_sql">
        <i id="queryByIds" desp="根据id查询票据资料数据"><![CDATA[
            SELECT
            *
            FROM
            da_fc_banksreceipt
            WHERE
            #in(cguid,ids);
        ]]></i>
        <i id="batchDelete" desp="批量删除票据资料主表数据"><![CDATA[
            DELETE
            FROM
            da_fc_banksreceipt
            WHERE
            #in(cguid,ids);
        ]]></i>
        <i id="deleteRel" desp="删除关联"><![CDATA[
            delete from da_fview_vouandbillrela where (cmeansid=? or cparent=?) and  cvoucherid=? and cserialnumber = ? AND corgnid = ?
        ]]></i>
        <i id="deleteRelNotSerialnumber" desp="删除关联"><![CDATA[
            delete from da_fview_vouandbillrela where (cmeansid=? or cparent=?) and  cvoucherid=? AND corgnid = ?
        ]]></i>
        <i id="deleteInvoiceRel" desp="删除票据关联"><![CDATA[
            delete from da_fc_voucherandticketrel where  cvoucherid=? and (ismatch <> 1 or ismatch is null) and cserialnumber = ? AND corgnid = ?
        ]]></i>
        <i id="deleteBanksRel" desp="删除银行回单关联"><![CDATA[
            delete from da_fc_voucherandbankrel where  cvoucherid=? and (ismatch <> 1 or ismatch is null) and cserialnumber = ? AND corgnid = ?
        ]]></i>
        <i id="deleteInvoiceRelNotSerialnumber" desp="删除票据关联"><![CDATA[
            delete from da_fc_voucherandticketrel where  cvoucherid=? and (ismatch <> 1 or ismatch is null) AND corgnid = ?
        ]]></i>
        <i id="deleteBanksRelNotSerialnumber" desp="删除银行回单关联"><![CDATA[
            delete from da_fc_voucherandbankrel where  cvoucherid=? and (ismatch <> 1 or ismatch is null) AND corgnid = ?
        ]]></i>
        <i id="deleteRelByVoucherId" desp="根据凭证id删除关联"><![CDATA[
           delete from da_fview_vouandbillrela where cvoucherid=? and cserialnumber = ?
        ]]></i>
        <i id="querykeyPairGuid" desp="自己的系统组织编码"><![CDATA[
            SELECT * FROM da_keypair_config WHERE $in(cguid,cguid)  and csystemcode_name is not null
        ]]></i>



    </sql>
</sqls>