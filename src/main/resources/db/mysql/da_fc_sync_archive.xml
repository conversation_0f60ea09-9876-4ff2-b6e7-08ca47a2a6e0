<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls>
<sql group="da_fc_archiveAction">
    <i id="selfAndLowerLevel" desp="获取组织本级以及下级的所有数据id">
        <![CDATA[
		    select cguid,cname,cparentid,istatus,iShareDept,cShareOrgn
            from aos_orgn,(
                select @ids as _ids,
                (select @ids := group_concat(cguid) from aos_orgn where find_in_set(cparentid,@ids)) as cid
                from aos_orgn,(select @ids:= {orgnId}) b
                where @ids is not null
                ) ta
            where find_in_set(cguid,ta._ids) and iStatus = 1
		]]>
    </i>

    <i id="selfAndLowerLevelByLevel" desp="获取组织本级以及下级的数据id(只递归一层)">
        <![CDATA[
            select cguid,cname,cparentid,istatus,iShareDept,cShareOrgn
            from aos_orgn,(
                select @ids as _ids,
                (select @ids := group_concat(cguid) from aos_orgn where find_in_set(cparentid,@ids)) as cid
                from aos_orgn,(select @ids:= {orgnId}) b
                where @ids is not null
                ) ta
            where find_in_set(cguid,ta._ids) and iStatus = 1 and ilevel <= {ilevel}
			and if(cguid != {orgnId},cguid!=cAdminOrgnID,1=1)
		]]>
    </i>

    <i id="selfAndUpLevel" desp="获取组织本级以及上级的所有数据id">
        <![CDATA[
		    select t2.cname,t2.cguid from
            (select
                @r AS _id,
                (select @r := cParentId from aos_orgn where cguid = _id limit 1 ) AS cParentId,
                @s := @s + 1 AS sort
                from (select @r := {orgnId}, @s := 0) temp,aos_orgn
                where @r > 0 ) t1
            join aos_orgn t2 on t1._id = t2.cguid
		]]>
    </i>
</sql>
</sqls>
