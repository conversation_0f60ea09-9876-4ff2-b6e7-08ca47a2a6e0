<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd" >
<sqls>
    <sql group="da_fc_data_query_sql">

        <i id="queryvoucher" desp="根据组织编码">
            <![CDATA[
                select *
                  from da_fc_md_voucherz v
                 where 1=1
                   and #equal(v.cOrgnId, corgnid)
                   and $between(v.cperioddate,cperioddate1,cperioddate2)
                   and $like(v.cacctstr,cacctstr)
                   and $like(v.cvoutypename,cvoutypename)
                   and $between(convert(v.cvoucode,signed),cvoucode1,cvoucode2)
                   and $between(v.cvoucherdate,cvoucherdate1,cvoucherdate2)
                   and $like(v.csummary,csummary)
                   and $between(convert(v.idebitamt,signed),idebitamt1,idebitamt2)
                   and $between(convert(v.icreditamt,signed),icreditamt1,icreditamt2)
                   and $like(v.cvoucreatorname,cvoucreatorname)
                   and $like(v.cauditorname,cauditorname)
                   and $like(v.cpostername,cpostername)
                   and $like(v.creviewer,creviewer)
                   and $between(convert(v.iqty,signed),iqty1,iqty2)
                   and $like(v.cuomname,cuomname)
                   and $like(v.iunitprice,iunitprice)
                   and $like(v.iamt_f,iamt_f)
                   and $like(v.ccurname,ccurname)
                   and $like(v.iexchangerate,iexchangerate)
                   and $like(v.cvoucodestr,cvoucodestr)
		    ]]>
        </i>

    </sql>
</sqls>
