<?xml version="1.0" encoding="UTF-8"?>
<sqls>
	<sql group="da_fc_bankreceipt_match">
		<!-- 银行回单列表 -->
		<i id="queryBankreceiptList"><![CDATA[
			select * from da_fc_banksreceipt
			where $equal(corgnid,corgnid)
			and $between(ctransactiondate,ctransactiondate_start,ctransactiondate_end)
			and $between(itransactionamount,itransactionamount_start,itransactionamount_end)
			and $like(creceiptnumber,creceiptnumber)
			and $like(bankserial,bankserial)
			and $like(ctransactionnum,ctransactionnum)
			and $in(cbankaccount,cbankaccount)
			and $in(cbankaccount,cbankaccount_name)
			and $like(caccountname,caccountname)
			and $like(ccounteraccount,ccounteraccount)
			and $like(ccounteraccname,ccounteraccname)
			and $like(cremark,cremark)
			and $like(cpurpose,cpurpose)
			and $like(cpostscript,cpostscript)
			and ceastatus_name='收集成功'
			and cflowstatus !='1'
			and (biscollectwithvou ='0' or biscollectwithvou is null or biscollectwithvou = '')
			and (cintegritystatus ='' or cintegritystatus is null or  ( cintegritystatus !='' and cintegritystatus is not null and cintegritystatus !='6'))
			and (cmaincopybankid is null or cmaincopybankid = '')
			and (cautoass ='0' or cautoass is null or cautoass = '')
			order by ctransactiondate desc
		]]></i>
		<i id="queryBankreceiptListYQ"><![CDATA[
			select * from da_fc_banksreceipt
			where $equal(corgnid,corgnid)
			and $between(ctransactiondate,ctransactiondate_start,ctransactiondate_end)
			and $between(itransactionamount,itransactionamount_start,itransactionamount_end)
			and $like(creceiptnumber,creceiptnumber)
			and $like(bankserial,bankserial)
			and $like(ctransactionnum,ctransactionnum)
			and $in(cbankaccount,cbankaccount)
			and $in(cbankaccount,cbankaccount_name)
			and $like(caccountname,caccountname)
			and $like(ccounteraccount,ccounteraccount)
			and $like(ccounteraccname,ccounteraccname)
			and $like(cremark,cremark)
			and $like(cpurpose,cpurpose)
			and $like(cpostscript,cpostscript)
			and ceastatus_name='收集成功'
			and cflowstatus !='1'
			and (datasource != '手工导入'
			or ($equal(ccollectorid, userId) and datasource = '手工导入'))
			and (biscollectwithvou ='0' or biscollectwithvou is null or biscollectwithvou = '')
			and (cmaincopybankid is null or cmaincopybankid = '')
			and (cautoass ='0' or cautoass is null or cautoass = '')
			order by ctransactiondate desc
		]]></i>
		<!-- 银行流水列表 -->
		<i id="queryBankstatementList"><![CDATA[
			select * from da_fc_bankstatement
			where $equal(corgnid,corgnid)
			and $between(ctransactiondate,ctransactiondate_start,ctransactiondate_end)
			and $between(itransactionamount,itransactionamount_start,itransactionamount_end)
			and $like(bankserial,bankserial)
			and $like(ctransactionnum,ctransactionnum)
			and $in(cbankaccount,cbankaccount)
			and $in(cbankaccount_name,cbankaccount_name)
			and $like(caccountname,caccountname)
			and $like(ccounteraccount,ccounteraccount)
			and $like(ccounteraccname,ccounteraccname)
			and $like(cremark,cremark)
			and $like(cpurpose,cpurpose)
			and (#unequal(creceiptstatus,creceiptstatus)
			or #unequal(cvoucherstatus,cvoucherstatus))
			and (cintegritystatus ='' or cintegritystatus is null or  ( cintegritystatus !='' and cintegritystatus is not null and cintegritystatus !='6'))
			and (cmaincopyflowid is null or cmaincopyflowid = '')
			and (cautoass ='0' or cautoass is null or cautoass = '')
			order by ctransactiondate desc
		]]></i>
		<!-- 凭证列表数据 -->
		<i id="queryVoucherList"><![CDATA[
			SELECT distinct
				l.*
			FROM
				da_fc_md_voucherz l
				left join da_fc_voucherinfo vi on l.cvoucherid = vi.cvoucherid and #equal(vi.corgnid,corgnid)
			WHERE
			#equal(l.corgnid,corgnid)
			and $between(l.cvoucherdate,cvoucherdate_start,cvoucherdate_end)
			and $like(l.cacctstr,cacctstr)
			and $like(l.cvoucodestr,cvoucode)
			and $between(l.iamt_f,iamt_f_start,iamt_f_end)
			and $like(l.csummary,csummary)
			and l.cflowstatus !='1'
			and l.cacctstr like '%银行存款%'
			and vi.ieastatus = '0' and vi.centityfilestatus = '0'
			and (vi.cintegritystatus ='' or vi.cintegritystatus is null or  ( vi.cintegritystatus !='' and vi.cintegritystatus is not null and vi.cintegritystatus !='6'))
			and $like(l.cvoutypename,cvoutypename)
			and vi.isuaistatus = 1
			and #equal(vi.corgnid,corgnid)
		]]></i>

		<!-- 当前登录组织拥有的已启用的规则id和规则明细 -->
		<i id="getRule"><![CDATA[
			select r.cguid ruleguid,r.cbankvouchercondition,r.corgnid sourceorgnid,rl.cmateplan
			from da_bbs_bankreceip_rule r
			left join da_bbs_ruledetail rl on rl.cHeadId = r.cgdid
			where #equal(r.orgn,corgnid)
			and r.iStatus=1 and #equal(r.cmatecontent,cmatecontent)
		]]></i>

		<!-- 当前登录组织拥有的规则方案明细 -->
		<i id="getMatchplan"><![CDATA[
			select m.cconditionname,ml.* from da_bbs_bank_mateplanline ml
			left join da_bbs_bank_mateplan m on m.cguid = ml.cHeadId
			left join da_bbs_ruledetail rl on rl.cmateplan = m.cconditionname and #equal(m.corgnid,sourceorgnid)
			left join da_bbs_bankreceip_rule r on rl.cHeadId = r.cgdid
			where #equal(r.cguid,ruleguid) order by rl.inum asc
		]]></i>

		<!-- 银行类记账凭证判断条件 -->
		<i id="getAcctPlan"><![CDATA[
			select col.ccolumncode,bc.* from da_bbs_bankvoucher_condition c
			left join  da_bbs_bankvoucher_condline bc on bc.cHeadId=c.cguid
			left join aps_data_table_column col on col.cguid = bc.centityfield
			where #equal(c.cconditionname,cconditionname) and #equal(c.corgnid,sourceorgnid)
		]]></i>

		<!-- 查某组织的所有上级组织 -->
		<i id="getAllParentOrgn"><![CDATA[
			SELECT _cguid AS cguid, cparentid, lvl
			FROM (
			    SELECT @orgId AS _cguid
				, (
				    SELECT @orgId := cparentid
				    FROM aos_orgn
				    WHERE cguid = _cguid
				) AS cparentid,
				@l := @l + 1 AS lvl
			    FROM (
				SELECT @orgId := ? , @l := 0
			    ) vars, aos_orgn h
			    WHERE @orgId IS NOT NULL
			) T1
			WHERE T1.cparentid IS NOT NULL AND T1.cparentid != 0
			ORDER BY lvl DESC
		]]></i>
		<i id="getBankreceiptFile"><![CDATA[
			SELECT
				cguid,
				receiptimagename,
				primarypdfname,
				uploadpdfname,
				ctransactionnum
			FROM
				da_fc_banksreceipt
			WHERE
				#equal(uploadpdfname,uploadpdfname)
		]]></i>
		<!-- 更新流水匹配凭证状态 -->
		<i id="updateBankstatementMatchVou"><![CDATA[
			update da_fc_bankstatement set cvoucherstatus = {cvoucherstatus},cvoucherstatus_name = {cvoucherstatus_name},
			cmatchvoucher={cmatchvoucher},cmatchvoucherid={cmatchvoucherid},cvoucherkeystr={cvoucherkeystr},
			cmatchmethod={cmatchmethod},cmatchmethod_name={cmatchmethod_name}
			where cguid = {cguid}
		]]></i>
		<!-- 更新凭证子表匹配流水状态 -->
		<i id="updateVouzMatchBankstatement"><![CDATA[
			update da_fc_md_voucherz set cflowstatus = {cflowstatus},cflowstatus_name = {cflowstatus_name},
			bankserial={bankserial},bankstatementid = {bankstatementid}
			where cguid = {cguid}
		]]></i>
		<!-- 更新凭证子表匹配流水回单状态 -->
		<i id="updateVouzMatchBankstatementAndBankreceipt"><![CDATA[
			update da_fc_md_voucherz set cflowstatus = {cflowstatus},cflowstatus_name = {cflowstatus_name},
			bankserial={bankserial},bankstatementid = {bankstatementid} ,cmatchreceiptstatus = {cmatchreceiptstatus},
			cmatchreceiptstatus_name = {cmatchreceiptstatus_name},ctransactionnum = {ctransactionnum},bankid = {bankid}
			where cguid = {cguid}
		]]></i>
		<!-- 更新凭证子表匹配回单状态(通过流水表id) -->
		<i id="updateVouzMatchBankreceiptByBankserial"><![CDATA[
			update da_fc_md_voucherz set cmatchreceiptstatus = {cmatchreceiptstatus},
			cmatchreceiptstatus_name = {cmatchreceiptstatus_name},ctransactionnum = {ctransactionnum}
			where bankstatementid = {bankstatementid}
		]]></i>
		<!-- 更新回单匹配凭证状态（通过回单id） -->
		<i id="updateBankreceiptMatchVou"><![CDATA[
			update da_fc_banksreceipt set cvoucherstatus = {cvoucherstatus},cvoucherstatus_name = {cvoucherstatus_name},
			cmatchvoucher={cmatchvoucher},cmatchvoucherid={cmatchvoucherid},cvoucherkeystr={cvoucherkeystr}
			where cguid = {bankid}
		]]></i>
		<!-- 更新回单匹配流水凭证状态（通过id） -->
		<i id="updateBankreceiptMatchBankstatementAndVou"><![CDATA[
			update da_fc_banksreceipt set cflowstatus = {cflowstatus},cflowstatus_name = {cflowstatus_name},
			ctransactionserialnumber = {ctransactionserialnumber},bankstatementid = {bankstatementid},
			cvoucherstatus = {cvoucherstatus},cvoucherstatus_name = {cvoucherstatus_name},
			cmatchvoucher={cmatchvoucher},cmatchvoucherid={cmatchvoucherid},cvoucherkeystr={cvoucherkeystr},
			ccopyflag={ccopyflag},ccopyflag_name={ccopyflag_name},cmatchmethod={cmatchmethod},cmatchmethod_name={cmatchmethod_name}
			where cguid = {cguid}
		]]></i>
		<!-- 更新回单匹配凭证状态（通过id） -->
		<i id="updateBankreceiptMatchBankstatement"><![CDATA[
			update da_fc_banksreceipt set cflowstatus = {cflowstatus},cflowstatus_name = {cflowstatus_name},
			ctransactionserialnumber = {ctransactionserialnumber},bankstatementid = {bankstatementid},
			ccopyflag={ccopyflag},ccopyflag_name={ccopyflag_name},cmatchmethod={cmatchmethod},cmatchmethod_name={cmatchmethod_name}
			where cguid = {cguid}
		]]></i>
		<!-- 更新凭证收集表匹配流水状态 -->
		<i id="updateVouMatchBankstatement"><![CDATA[
			update da_fc_voucherinfo set cflowstatus = {cflowstatus},cflowstatus_name = {cflowstatus_name}
			where cvoucherid = {cvoucherid}
		]]></i>
		<!-- 更新凭证收集表匹配流水回单状态 -->
		<i id="updateVouMatchBankstatementAndBankreceipt"><![CDATA[
			update da_fc_voucherinfo set cflowstatus = {cflowstatus},cflowstatus_name = {cflowstatus_name},
			cmatchreceiptstatus = {cmatchreceiptstatus},cmatchreceiptstatus_name = {cmatchreceiptstatus_name}
			where cvoucherid = {cvoucherid}
		]]></i>
		<!-- 更新流水匹配回单状态 -->
		<i id="updateBankstatementMatchBankreceipt"><![CDATA[
			update da_fc_bankstatement set creceiptstatus = {creceiptstatus},creceiptstatus_name = {creceiptstatus_name},
			ctransactionnum={ctransactionnum},bankid = {bankid}
			where cguid = {cguid}
		]]></i>
		<!-- 插入银行回单和凭证关系_检索 -->
		<i id="insertVouandbillrela_fview"><![CDATA[
			insert into da_fview_vouandbillrela
			(cvoucherid,ctype,cmeansid,cmeanscode,cmeansname,cparent,cfiletablename,pageurl,cguid,ccreatedate,ctimestamp,corgnid,ccreatorid,cadminorgnid,corgnid_name,cadminorgnid_name,ccreatorid_name)
			values
			({cvoucherid},{ctype},{cmeansid},{cmeanscode},{cmeansname},{cparent},{cfiletablename},{pageurl},{cguid},{ccreatedate},{ctimestamp},{corgnid},{ccreatorid},{cadminorgnid},{corgnid_name},{cadminorgnid_name},{ccreatorid_name})
		]]></i>
		<!-- 插入银行回单和凭证关系_关联 -->
		<i id="insertVouandbillrela_vouandbank"><![CDATA[
			insert into da_fc_voucherandbankrel
			(ctransactionnum,cvoucherid,cvoucode,cguid,ccreatedate,ctimestamp,corgnid,ccreatorid,cadminorgnid,corgnid_name,cadminorgnid_name,ccreatorid_name,ismatch,cvoucherkeystr,bankid,cvoucherlineid)
			values
			({ctransactionnum},{cvoucherid},{cvoucode},{cguid},{ccreatedate},{ctimestamp},{corgnid},{ccreatorid},{cadminorgnid},{corgnid_name},{cadminorgnid_name},{ccreatorid_name},{ismatch},{cvoucherkeystr},{bankid},{cvoucherlineid})
		]]></i>
		<!-- 插入银行流水和凭证关系_关联 -->
		<i id="insertVouandbillrela_vouandbankflow"><![CDATA[
			insert into da_fc_voucherandbankflowrel
			(ccreatedate,cvoucherlineid,bankstatementid,bankserial,cvoucherid,cvoucode,cguid,corgnid,ccreatorid,corgnid_name,ccreatorid_name,ismatch,cvoucherkeystr)
			values
			({ccreatedate},{cvoucherlineid},{bankstatementid},{bankserial},{cvoucherid},{cvoucode},{cguid},{corgnid},{ccreatorid},{corgnid_name},{ccreatorid_name},{ismatch},{cvoucherkeystr})
		]]></i>
		<!-- 取消回单匹配流水凭证状态 -->
		<i id="cancelBankreceiptMatch"><![CDATA[
			update da_fc_banksreceipt set cflowstatus = '0',cflowstatus_name = '未匹配',
			cmatchvoucher=null,ctransactionserialnumber=null,bankstatementid=null,
			cmatchvoucherid=null,cvoucherkeystr=null,
			cvoucherstatus = '0',cvoucherstatus_name = '未匹配',
			ccopyflag=null,ccopyflag_name='否',cmatchmethod=null,cmatchmethod_name=null
			where #equal(cguid,bankid)
		]]></i>
		<!-- 取消流水匹配状态 -->
		<i id="cancelBankstatementMatch"><![CDATA[
			update da_fc_bankstatement set creceiptstatus = '0',creceiptstatus_name = '未匹配',
			ctransactionnum=null,bankid=null
			where #equal(cguid,bankstatementid)
		]]></i>
		<!-- 取消流水匹配状态(通过交易流水号) -->
		<i id="cancelBankstatementMatchByBankserial"><![CDATA[
			update da_fc_bankstatement set creceiptstatus = '0',creceiptstatus_name = '未匹配',
			ctransactionnum=null,bankid=null
			where #equal(bankserial,cancelrelbankserial)
		]]></i>
		<!-- 取消流水匹配凭证状态 -->
		<i id="cancelBankstatementMatchVou"><![CDATA[
			update da_fc_bankstatement set cvoucherstatus = '0',cvoucherstatus_name = '未匹配',
			cmatchvoucher=null,cmatchvoucherid=null,cvoucherkeystr=null,
			ccopyflag=null,ccopyflag_name='否',cmatchmethod=null,cmatchmethod_name=null
			where #equal(cguid,bankstatementid)
		]]></i>
		<!-- 取消流水匹配凭证状态(通过流水号) -->
		<i id="cancelBankstatementMatchVouByBankserial"><![CDATA[
			update da_fc_bankstatement set cvoucherstatus = '0',cvoucherstatus_name = '未匹配',
			cmatchvoucher=null,cmatchvoucherid=null,cvoucherkeystr=null,
			ccopyflag=null,ccopyflag_name='否',cmatchmethod=null,cmatchmethod_name=null
			where #equal(bankserial,cancelrelbankserial)
		]]></i>
		<!-- 取消凭证匹配流水、回单状态 -->
		<i id="cancelVouMatchBankstatement"><![CDATA[
			update da_fc_md_voucherz set cflowstatus = '0',cflowstatus_name = '未匹配',
			bankserial=null,bankstatementid=null,cmatchreceiptstatus = '0',cmatchreceiptstatus_name = '未匹配',
			ctransactionnum=null,bankid=null
			where #equal(bankstatementid,bankstatementid)
		]]></i>
		<!-- 取消凭证子表匹配流水状态 -->
		<i id="cancelVouMatchBankstatementByVoucherlineid"><![CDATA[
			update da_fc_md_voucherz set cflowstatus = '0',cflowstatus_name = '未匹配',
			bankserial=null,bankstatementid=null
			where #equal(cguid,cancelvoucherlineid)
		]]></i>
		<!-- 取消凭证子表匹配回单状态 -->
		<i id="cancelVouMatchBankReceiptByVoucherlineid"><![CDATA[
			update da_fc_md_voucherz set cmatchreceiptstatus = '0',cmatchreceiptstatus_name = '未匹配',
			ctransactionnum=null,bankid=null
			where #equal(cguid,cancelvoucherlineid)
		]]></i>
		<!-- 取消回单匹配凭证状态 -->
		<i id="cancelBankreceiptMatchVou"><![CDATA[
			update da_fc_banksreceipt set cvoucherstatus = '0',cvoucherstatus_name = '未匹配',
			cmatchvoucher=null,cmatchvoucherid=null,cvoucherkeystr=null,
			ccopyflag=null,ccopyflag_name='否'
			where #equal(bankstatementid,bankstatementid)
		]]></i>
		<!-- 取消凭证匹配回单状态 -->
		<i id="cancelVouMatchBankreceipt"><![CDATA[
			update da_fc_md_voucherz set cmatchreceiptstatus = '0',cmatchreceiptstatus_name = '未匹配',
			ctransactionnum=null,bankid=null
			where #equal(bankid,bankid)
		]]></i>
		<!-- 取消流水匹配凭证状态_凭证信息 -->
		<i id="cancelBankstatementMatchVouByVoucherkey"><![CDATA[
			update da_fc_bankstatement set cvoucherstatus = '0',cvoucherstatus_name = '未匹配',
			cmatchvoucher=null,cmatchvoucherid=null,cvoucherkeystr=null,
			ccopyflag=null,ccopyflag_name='否',cmatchmethod=null,cmatchmethod_name=null
			where #equal(cvoucherkeystr,cvoucherkeystr)
		]]></i>
		<!-- 取消回单匹配凭证状态_凭证信息 -->
		<i id="cancelBankreceiptMatchVouByVoucherkey"><![CDATA[
			update da_fc_banksreceipt set cvoucherstatus = '0',cvoucherstatus_name = '未匹配',
			cmatchvoucher=null,cmatchvoucherid=null,cvoucherkeystr=null
			where #equal(cvoucherkeystr,cvoucherkeystr)
		]]></i>
		<i id="cancelBankAndVouByVoucherkey"><![CDATA[
			delete from da_fc_voucherandbankrel
			where #equal(cvoucherkeystr,cvoucherkeystr) and ismatch = '1'
		]]></i>
		<!-- 清除银行回单与记账凭证后台关联关系_资料检索-->
		<i id="deleteFviewRel"><![CDATA[
			delete from da_fview_vouandbillrela where #equal(cmeansid,bankreceiptid)
		]]></i>
		<!-- 清除银行回单与记账凭证后台关联关系_关联检测-->
		<i id="deleteVouAndBankRel"><![CDATA[
			delete from da_fc_voucherandbankrel where #equal(ctransactionnum,ctransactionnum) and ismatch = '1'
		]]></i>
		<!-- 清除银行流水与凭证后台关系-->
		<i id="deleteVouAndBankFlowRel"><![CDATA[
			delete from da_fc_voucherandbankflowrel where #equal(bankstatementid,bankstatementid) and ismatch = '1'
		]]></i>
		<!-- 根据序列号查询凭证信息（未归档） -->
		<i id="getVoucherBySerialnumber"><![CDATA[
			 SELECT CONCAT_WS('',corgnid,cvoucherdate,cvoutypename,ivoucode) cvoucherkeystr,cvoucherid
			 from da_fc_voucherinfo
			 where #in(cserialnumber,cserialnumber)
			 and ceastatus='收集成功' and centityfilestatus_name='收集成功'
		]]></i>
		<i id="getBankData" desp="根据条件查询银行回单Id"><![CDATA[
            SELECT cguid FROM da_fc_banksreceipt t
            WHERE t.datasource != '手工导入' or ( t.ccollectorid = ? and t.datasource = '手工导入' );
        ]]></i>
		<i id="getMatchVoucherLineIdByFlowId" desp="根据流水id获取匹配凭证子表id"><![CDATA[
			select cvoucherlineid from da_fc_voucherandbankflowrel where #in(bankstatementid,bankstatementid)
		]]></i>
		<i id="getMatchVoucherLineIdByReceiptId" desp="根据回单id获取匹配凭证子表id"><![CDATA[
			select cvoucherlineid from da_fc_voucherandbankrel where #in(bankid,bankid)
		]]></i>
		<i id="deletecopyflow" desp="删除复制的流水"><![CDATA[
			delete from da_fc_bankstatement where #equal(cguid,bankstatementid)
		]]></i>
		<i id="deletecopyreceipt" desp="删除复制的回单"><![CDATA[
			delete from da_fc_banksreceipt where #equal(cguid,bankid)
		]]></i>
		<i id="getAllVoucherByBankstatementId" desp="根据流水id获取所有匹配凭证（包括复制的流水）"><![CDATA[
			select r.cvoucherlineid,r.bankstatementid,v.cvoucherid,v.corgnid,v.cvoucherdate,v.cvoutypename,v.cvoucode,v.cvoucodestr
			from da_fc_voucherandbankflowrel r
			left join da_fc_bankstatement t  on t.cGuid = r.bankstatementid
			left join da_fc_md_voucherz v on v.cguid = r.cvoucherlineid
			where #in(t.cguid,bankstatementid) or #in(t.cmaincopyflowid,cmaincopyflowid) or #in(t.cguid,cmaincopyflowid) or #in(t.cmaincopyflowid,bankstatementid)
		]]></i>
	</sql>
</sqls>
