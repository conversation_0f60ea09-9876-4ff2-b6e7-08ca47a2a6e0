<?xml version="1.0" encoding="UTF-8"?>
<sqls>
    <sql group="da_fc_omissioncheckaction_sql">
        <i id="queryAccountBook" desp="获取账簿"><![CDATA[
            select cdetailclassname cname,cdetailclasscode ccode,ieastatus,centityfilestatus,cintegritystatus from da_fc_accountbook where #equal(corgnid,corgnid)
            and cdatasource is not null and #equal(cperioddate,cperioddate) order by  cdetailclasscode
        ]]></i>
        <i id="queryOtherAccountReport" desp="获取其他会计资料"><![CDATA[
            select cdetailclassname cname,cdetailclasscode ccode,ieastatus,centityfilestatus,cintegritystatus from da_fc_otheraccountreport where #equal(corgnid,corgnid)
            and cdatasource is not null and #equal(cperioddate,cperioddate) order by  cdetailclasscode
        ]]></i>
        <i id="queryAccountReport" desp="获取会计资料"><![CDATA[
            select cdetailclassname cname,cdetailclasscode ccode,ieastatus,centityfilestatus,cintegritystatus from da_fc_accountreport where #equal(corgnid,corgnid)
            and cdatasource is not null and #equal(cperioddate,cperioddate) order by  cdetailclasscode
        ]]></i>
    </sql>
</sqls>