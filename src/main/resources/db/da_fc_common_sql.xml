<?xml version="1.0" encoding="UTF-8"?>
<sqls>
    <sql group="da_fc_common_sql">
        <i id="queryDetailClassByMainCode" desp="根据分类预置主表编码获取dev明细分类信息"><![CDATA[
            select b.cguid,b.ccode, b.cname,b.ccustody_year_name,daf.istatus
            from da_bbs_file_class a
            inner join da_bbs_file_class_sublist b on b.cHeadId = a.cGuid
            inner join da_bbs_file_class_sublist_rel daf on b.cguid = daf.cItemGuid
            and #equal(a.ccode,ccode) order by  ccode
        ]]></i>
        <i id="queryFileDownloadConfig" desp="根据分类预置主表编码获取dev明细分类信息(废弃)"><![CDATA[

        ]]></i>
        <i id="queryPreconditions" desp="获取缺件规则的前提条件"><![CDATA[
            select b.cdataitem,b.ccondition,b.cvalue,b.clogic,a.cguid from da_bbs_missingparts_set a
                   left join da_bbs_preconditions b on b.cheadid=a.cguid
            where a.istatus = 1 and #equal(a.cinspectionobject,cinspectionobject) and #equal(a.corgnid,corgnid) order by b.inumber
        ]]></i>
        <i id="queryCheckRules" desp="获取缺件规则的检查维度"><![CDATA[
            select c.ccheckdimensions,c.cvalue,c.ccheckdimensions_name,a.cguid from da_bbs_missingparts_set a
                left join da_bbs_checkrules c on c.cheadid=a.cguid
            where a.istatus = 1 and #equal(a.cinspectionobject,cinspectionobject) and #equal(a.corgnid,corgnid) order by c.inumber
        ]]></i>
    </sql>
</sqls>