<?xml version="1.0" encoding="UTF-8"?>
<sqls>
    <sql group = "da_afk_files_inspection_sql">
        <i id="getOutData" desp="质检出库数据获取"><![CDATA[
        select
			'da_afk_outstkreview_box' cTemplateId,
			'da_afk_outstkreview_box' cPageId,
			'出库信息查看' cPageId_name,
			'出库信息查看' cTemplateid_name,
            e.cqznum,
            e.cOrgnId,
            e.cOrgnId_name,
            e.cAdminOrgnId,
            e.cAdminOrgnId_name,
            e.cguid centfileid,
            e.centfilenum,
            case when length(e.cmonth) = 2 then concat (e.cyear,'-',e.cmonth)
                 when length(e.cmonth) = 1 then concat (e.cyear,'-0',e.cmonth)
                 else e.cyear end as iyear,
            (case char_length(e.cmonth) when 1 then concat('0', e.cmonth) else e.cmonth end)AS imonth,
            e.caccountclassguid cacctclass,
            e.caccountclassname cacctclass_name,
            e.cdetailclassguid cdetailclass,
            e.cdetailclassname cdetailclass_name,
            e.clocationcode,
            '3' ideliveryreason,
            x.cguid centboxid,
            e.centboxnum,
            e.cbustypeguid cbusclass,
            e.cbustypename cbusclass_name,
            li.cguid csourcebilllineid,
            l.cGuid csourcebillid
            from da_afk_entityfiles e
            left join da_afk_files_inspectionline li
            on e.cguid  = li.centfileguid
            left join da_afk_files_inspection l
            on li.cHeadId = l.cguid
            left join da_afk_entitybox x
            on e.centboxnum = x.centboxnum
            where li.cHeadId = ?

        ]]></i>

        <i id="getInData" desp="质检入库数据获取"><![CDATA[
        select
			'da_afk_outstkreview_box' cTemplateId,
			'da_afk_outstkreview_box' cPageId,
			'入库信息确认' cPageId_name,
			'入库信息确认' cTemplateid_name,
            e.cqznum,
            e.cOrgnId,
            e.cOrgnId_name,
            e.cAdminOrgnId,
            e.cAdminOrgnId_name,
            e.cguid centfileid,
            e.centfilenum,
            case when length(e.cmonth) = 2 then concat (e.cyear,'-',e.cmonth)
                 when length(e.cmonth) = 1 then concat (e.cyear,'-0',e.cmonth)
                 else e.cyear end as iyear,
            (case char_length(e.cmonth) when 1 then concat('0', e.cmonth) else e.cmonth end)AS imonth,
            e.caccountclassguid cacctclass,
            e.caccountclassname cacctclass_name,
            e.cdetailclassguid cdetailclass,
            e.cdetailclassname cdetailclass_name,
            e.clocationcode,
            '4' iarrivalreason,
            x.cguid centboxid,
            e.centboxnum,
            e.cbustypeguid cbusclass,
            e.cbustypename cbusclass_name,
            li.cguid csourcebilllineid,
            l.cGuid csourcebillid
            from da_afk_entityfiles e
            left join da_afk_files_inspectionline li
            on e.cguid  = li.centfileguid
            left join da_afk_files_inspection l
            on li.cHeadId = l.cguid
            left join da_afk_entitybox x
            on e.centboxnum = x.centboxnum
            where li.cHeadId = ?

        ]]></i>

        <i id="queryCapplicant" desp="获取当前登录用户信息"><![CDATA[
            select
              u.cguid capplicantid,
              u.ccode capplicantcode,
              u.cname capplicantname,
              dept.cGUID  cdeptguid,
              dept.ccode cdeptcode,
              dept.cname  cdeptname,
              u.cmobilephone capplicantphone
            from
              aos_rms_user u
              left join cm_employee emp on u.cEmp = emp.cguid
              left join aps_bd_empdept_relation r on emp.cguid = r.cempguid
              left join cm_department dept on dept.cguid = r.cdeptguid
            where
              u.istatus = 1
              and u.cguid = ?
        ]]></i>

    </sql>
</sqls>