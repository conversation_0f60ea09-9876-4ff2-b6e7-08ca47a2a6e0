<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqls SYSTEM "sql_definition.dtd">
<sqls>
    <sql group="da_afk_archivesKeeping">
        <i id="queryQZNumCurOrgn" desp="加载当前组织全宗号"> <![CDATA[
            select calldanum from da_param_setting a where #equal(a.corgnid,corgnid)
        ]]></i>
        <i id="queryQZNumDefault" desp="加载默认全宗号"> <![CDATA[
            select calldanum from da_param_setting where ifnull(corgnid,'')  = ''
        ]]></i>
        <i id="queryBoxInfoByBoxNum" desp="盒号在组织内唯一，根据盒号查询盒信息"> <![CDATA[
            select eb.cguid,eb.cqznum,eb.corgnid,eb.corgnid_name,eb.centboxnum,eb.cperiod,
                   eb.caccountclassguid,eb.caccountclasscode,eb.caccountclassname,eb.cfilescount,eb.cstatus
              from da_afk_entitybox eb
             where #equal(eb.centboxnum,centboxnum)
			   and #equal(eb.corgnid,corgnid)
        ]]></i>
        <i id="queryEntityFilesByBoxid" desp="根据盒id查询档案信息"> <![CDATA[
            select ebl.centfileguid,ef.cstatus,ef.centfilenum,
                   ef.ctransfer,ef.cborrow,ef.cqualityctrl,ef.cdestroy,eb.cguid centboxid,eb.centboxnum
              from da_afk_entityboxline ebl
         left join da_afk_entitybox eb
                on eb.cguid = ebl.cheadid
               and eb.corgnid = ebl.corgnid
         left join da_afk_entityfiles ef on ef.cguid=ebl.centfileguid and ebl.corgnid=ef.corgnid
             where #in(eb.cguid,boxids)
			   and #equal(eb.corgnid,corgnid)
        ]]></i>
        <i id="queryBusinessRecord" desp="取消入库校验-查询某盒内某卷是否发生过后续业务"><![CDATA[
			  select distinct dai.centboxnum from da_afk_instkrecordline dai where #in(dai.centboxid,boxids) and dai.iarrivalreason <>1
            union
              select distinct dao.centboxnum from da_afk_outstkrecordline dao where #in(dao.centboxid,boxids)
	    ]]></i>
        <i id="queryGridCapacity" desp="根据库房-格id查询格剩余容量"> <![CDATA[
            select icapacity - ifnull(iusedcapacity,0) as capacity
              from da_bbs_storeroom_gridline eb
             where #equal(eb.cguid,cgrid)
        ]]></i>
        <i id="updateGridCapacity" desp="根据库房-格id更新格已使用容量"> <![CDATA[
            update da_bbs_storeroom_gridline
               set iusedcapacity = ifnull(iusedcapacity,0) + {usedcapacity}
              where #equal(cguid,cstoregrid)
        ]]></i>
        <i id="updateGroupCapacity" desp="根据库房-组柜id更新柜组已使用容量"> <![CDATA[
            update da_bbs_storeroomline
               set iusedgridcapacity = ifnull(iusedgridcapacity,0) + {usedcapacity}
              where #equal(cguid,cstoregroup)
        ]]></i>
        <i id="updateGroupCapacityAndUsedOrgn" desp="根据库房-组柜id更新柜组已使用容量、已使用组织"> <![CDATA[
            update da_bbs_storeroomline
               set iusedgridcapacity = ifnull(iusedgridcapacity,0) + {usedcapacity} ,
                   cusedorgs = {cusedorgs}
              where #equal(cguid,cstoregroup)
        ]]></i>
        <i id="queryNotUsedIdByGroupId" desp="通过库房组柜id查询‘已使用组织’ "><![CDATA[
			  select wl.cguid,wl.cusedorgs
                from da_bbs_storeroomline wl
             where #equal(wl.cguid,cstoregroup)
	    ]]></i>
        <i id="updateBoxMainInfo" desp="更新盒主表信息"><![CDATA[
			  update da_afk_entitybox
               set cstatus = '3',
                   cstatus_name = '已入库在库',
                   groupnum = {cstoregroup},
                   gridcode = {cstoregrid},
                   clocationcode = {clocationcode}
              where #in(cguid,boxIds)
	    ]]></i>
        <i id="queryBoxLineAndEntity" desp="入库单 根据盒id查盒内明细信息 ：明细id、档案id "> <![CDATA[
            select ebl.cguid, ebl.centfileguid, eb.cguid cboxmainid
                    /* ,il.corgnid stkFileOrgnid,il.clocationcode,il.cstoregroup,il.cstoregrid */
              from da_afk_entityboxline ebl
              left join da_afk_entitybox eb
                on eb.cguid = ebl.cheadid
             where #in(eb.cguid,boxIds)
			   and #equal(eb.corgnid,corgnid)
        ]]></i>
        <i id="updateBoxLineInfo" desp="更新盒子表信息：档案状态=已入库在库"><![CDATA[
            update da_afk_entityboxline t
            set t.cstatus = '3', t.cstatus_name = '已入库在库'  where #equal(cGuid,cboxlineid)
        ]]></i>
        <i id="updateEntityFileInfo" desp="更新案卷信息：档案状态=已入库在库、库位码"><![CDATA[
            update da_afk_entityfiles t
            set t.cstatus = '4', t.cstatus_name = '已入库在库' ,t.clocationcode ={clocationcode}  where #equal(cGuid,centityfileid)
        ]]></i>
        <i id="updateBoxMainInfoCancel" desp="取消入库,更新盒主表信息"><![CDATA[
			  update da_afk_entitybox
               set cstatus = '2',
                   cstatus_name = '已装盒',
                   groupnum = null,
                   gridcode = null,
                   clocationcode = null
              where #equal(cguid,cguid)
	    ]]></i>
        <i id="updateBoxLineInfoCancel" desp="取消入库，更新盒子表信息：档案状态=已装盒"><![CDATA[
            update da_afk_entityboxline t
            set t.cstatus = '2', t.cstatus_name = '已装盒'  where #equal(cGuid,cboxlineid)
        ]]></i>
        <i id="updateEntityFileInfoCancel" desp="取消入库，更新案卷信息：档案状态=已装盒、库位码"><![CDATA[
            update da_afk_entityfiles t
            set t.cstatus = '3', t.cstatus_name = '已装盒' ,t.clocationcode = null  where #equal(cGuid,centfileguid)
        ]]></i>
        <i id="updateGridCapacityCancel" desp="取消入库，根据库房-格id更新格已使用容量"> <![CDATA[
            update da_bbs_storeroom_gridline
               set iusedcapacity = ifnull(iusedcapacity,0) - 1
              where #equal(cguid,cstoregrid)
        ]]></i>
        <i id="deleteGridLine" desp="若格明细已使用容量=0，删除格明细记录"> <![CDATA[
            delete from da_bbs_storeroom_gridline
              where #equal(cguid,cstoregrid) and iusedcapacity = 0
        ]]></i>
        <i id="updateGroupCapacityCancel" desp="取消入库，根据库房-组柜id更新柜组已使用容量"> <![CDATA[
            update da_bbs_storeroomline
               set iusedgridcapacity = ifnull(iusedgridcapacity,0) - 1
              where #equal(cguid,cstoregroup)
        ]]></i>

        <i id="insertStoreRoomGridLine" desp="虚拟格数据，需要往格明细表里插入此格相关数据"><![CDATA[
            insert into da_bbs_storeroom_gridline
            (cgroupid,ccolumncode,cgridcode,icapacity,iusedcapacity,cGuid,cOrgnId,cCreateDate,cTimeStamp,cCreatorId)
            values
            ({cgroupid},{ccolumncode},{cgridcode},{icapacity},{iusedcapacity},{cguid},{corgnid},{ccreatedate},{ctimestamp},{ccreatorid})
        ]]></i>

        <!-- 通过入库单ids,查询所有入库单明细上的盒ids -->
        <i id="queryBoxIdsByStkIds"><![CDATA[
			select centboxid from da_afk_instkrecordline where #in(cheadid,cstkguids)
	    ]]></i>
        <i id="queryBoxLineAndEntityOutStk" desp="根据盒id查盒内明细信息 ：明细id、档案id "> <![CDATA[
            select ebl.cguid, ebl.centfileguid,eb.cguid cmainid
              from da_afk_entityboxline ebl
              left join da_afk_entitybox eb
                on eb.cguid = ebl.cheadid
             where #in(eb.cguid,boxids)
        ]]></i>
        <i id="queryBoxInfoByBoxIds" desp="通过盒ids,查询所有盒的id、盒所在库位的柜组id、格id"><![CDATA[
			select cguid,groupnum,gridcode from da_afk_entitybox where #in(cguid,boxids)
	    ]]></i>
        <!-- 通过库房组柜明细ids 查询组柜id中  组织使用记录不为空的组柜-->
        <i id="queryNotNullIdByGroupIds"><![CDATA[
			  select distinct eb.groupnum
                from da_afk_entitybox eb
             where #in(eb.groupnum,groupids)
                   and $equal(eb.corgnid,corgnid)
	    ]]></i>
        <i id="queryFileIdsByEntityIds" desp="根据卷id查询 组卷明细的信息"><![CDATA[
                SELECT DISTINCT
                    eflo.cfileid ,
                    efs.cfiletype,
                    efs.capparat,
                    ef.corgnid,
                    ef.caccountclasscode,
                    ef.clocationcode
                FROM
                    da_afk_entityfiles ef
                    INNER JOIN da_afk_entityfiles_scheme efs ON efs.cGuid = ef.cfilingschemeid AND efs.cOrgnId = ef.cOrgnId
                    INNER JOIN da_afk_entityfilesline eflo ON ef.cguid = eflo.cHeadId AND ef.cOrgnId = eflo.cOrgnId
                where #in(ef.cguid,cguids)
        ]]></i>
        <i id="updatePZFilestateById" desp="更新凭证表 库位码和实体组卷状态"><![CDATA[
			  update da_fc_voucherinfo
               set centityfilestatus = {centityfilestatus},
                   centityfilestatus_name = {centityfilestatus_name},
                   clocationcode = {clocationcode}
              where #equal(cguid,cfileid) and $euqal(corgnid,corgnid)
	    ]]></i>
        <i id="updateBillFilestateById" desp="更新单据表 库位码和实体组卷状态"><![CDATA[
			  update da_api_fk_file_collection
               set centityfilestatus = {centityfilestatus},
                   centityfilestatus_name = {centityfilestatus_name},
                   clocationcode = {clocationcode}
              where #equal(cguid,cfileid) and $euqal(corgnid,corgnid)
	    ]]></i>
        <i id="updatePJFilestateById" desp="更新票据表 库位码和实体组卷状态"><![CDATA[
			  update da_invoice
               set centityfilestatus = {centityfilestatus},
                   centityfilestatus_name = {centityfilestatus_name},
                   clocationcode = {clocationcode}
              where #equal(cguid,cfileid) and $euqal(corgnid,corgnid)
	    ]]></i>
        <i id="updateBankFilestateById" desp="更新银行回单表 库位码和实体组卷状态"><![CDATA[
			  update da_fc_banksreceipt
               set centityfilestatus = {centityfilestatus},
                   centityfilestatus_name = {centityfilestatus_name},
                   clocationcode = {clocationcode}
              where #equal(cguid,cfileid) and $euqal(corgnid,corgnid)
	    ]]></i>
        <i id="updateZBFilestateById" desp="更新账簿表 库位码和实体组卷状态"><![CDATA[
			  update da_fc_accountbook
               set centityfilestatus = {centityfilestatus},
                   centityfilestatus_name = {centityfilestatus_name},
                   clocationcode = {clocationcode}
              where #equal(cguid,cfileid) and $euqal(corgnid,corgnid)
	    ]]></i>
        <i id="updateBGFilestateById" desp="更新报告表 库位码和实体组卷状态"><![CDATA[
			  update da_fc_accountreport
               set centityfilestatus = {centityfilestatus},
                   centityfilestatus_name = {centityfilestatus_name},
                   clocationcode = {clocationcode}
              where #equal(cguid,cfileid) and $euqal(corgnid,corgnid)
	    ]]></i>
        <i id="updateQTFilestateById" desp="更新其他表 库位码和实体组卷状态"><![CDATA[
			  update da_fc_otheraccountreport
               set centityfilestatus = {centityfilestatus},
                   centityfilestatus_name = {centityfilestatus_name},
                   clocationcode = {clocationcode}
              where #equal(cguid,cfileid) and $equal(corgnid,corgnid)
	    ]]></i>
    </sql>
</sqls>