<?xml version="1.0" encoding="UTF-8"?>
<sqls>
	<sql group="da_fc_bill_collection_receipt_sql">
		<!-- 银行回单列表 -->
		<i id="selectBankReceiptList"><![CDATA[
			SELECT
				 t.*
			FROM
				da_fc_banksreceipt t
			WHERE $equal(t.corgnid,corgnid)
				and $between(t.ctransactiondate,ctransactiondate_start,ctransactiondate_end)
				and $like(ctransactionnum,ctransactionnum)
				and $like(t.cbank,cbank)
				and $like(t.caccountname,caccountname)
				and $like(t.ccounteraccname,ccounteraccname)
				AND $equal(t.crelstatus,crelstatus)
				AND $equal(t.ccollectorid,ccollectorid)
				#changedata9999#
				#changedata12345#
			ORDER BY
				t.ctransactiondate DESC,
				t.ctransactionnum DESC
		]]></i>
		<i id="selectInvoiceList"><![CDATA[
			SELECT
				 distinct t.*
			FROM
				da_invoice t
			WHERE $equal(t.corgnid,corgnid)
				and $between(t.ddate,ddate_start,ddate_end)
				and $between(t.itotal,itotal_start,itotal_end)

				and $like(ctype,ctype)
				AND $equal(t.ccode,ccode)
				and $like(t.cnumber,cnumber)
				AND $equal(t.crelstatus,crelstatus)
				AND $equal(t.ccollectorid,ccollectorid)

				#changedccollectorid#
				#changedatacpayeeid#
				#changedatacdareviewerid#

			ORDER BY
				t.ddate desc,
				t.cnumber desc
		]]></i>
		<i id="selectBillCollectionList"><![CDATA[
			SELECT
				distinct t.*
			FROM
				da_api_fk_file_collection t
			WHERE $equal(t.corgnid,corgnid)
				and $between(t.ddate,ddate_start,ddate_end)
				and $like(cbillcode,cbillcode)
				and $like(cvoucherno,cvoucherno)
				AND $equal(t.cdabilltypeid,cdabilltypeid)
				AND $equal(t.cempguid_name,cempguid_name)
				and $like(t.ccreatorname,ccreatorname)
				and $equal(t.cdeptguid_name,cdeptguid_name)
				AND $equal(t.crelstatus,crelstatus)
				AND $equal(t.datasource,datasource)
				AND $equal(t.ccollectorid,ccollectorid)
				AND $equal(t.collstatus,collstatus)
				#changedccollectorid#
				#changedatacdacreatorid#
				#changedatacdadeptguid#
				#changedatacdaempguid#
			ORDER BY
				t.ddate desc,
				t.cbillcode desc

		]]></i>

	</sql>
</sqls>
