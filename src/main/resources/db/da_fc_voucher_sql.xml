<?xml version="1.0" encoding="UTF-8"?>
<sqls>
    <sql group="da_fc_voucher_sql">
        <i id="queryVoucherGuid" desp="获取凭证id"><![CDATA[
            select cguid from da_fc_voucherinfo where #equal(corgnid,corgnid)
            and #equal(cperioddatelist,cperioddatelist)
            and #equal(cvoutypename,cvoutypename)
            and #equal(ivoucode,ivoucode)
            and #equal(cvoucode,cvoucode)
        ]]></i>
        <i id="queryVoucherInfoByMonthAndYear" desp="按年月获取凭证"><![CDATA[
            select cvoucode,ivoucode,cvoutypename from da_fc_voucherinfo where
            #equal(cmonth,month) and #equal(cyear,year) and
            #equal(corgnid,corgnid) order by cvoutypename,(ivoucode + 0) asc
        ]]></i>
        <i id="getVoucherOmissionCheckInfo" desp="获取凭证漏查无数据、断号月份"><![CDATA[
            select distinct cmonth from da_fc_omissioncheckinfo
            where (ctype=0 or ctype = 2) and #equal(corgnid,corgnid) and #equal(cyear,cyear) and cloglistflag ='01'
        ]]></i>
        <i id="queryVoucherInfoByYear" desp="按年获取凭证"><![CDATA[
            select distinct cmonth from da_fc_voucherinfo where #equal(cyear,cyear) and
            #equal(corgnid,corgnid)
        ]]></i>
        <i id="queryVoucherInfoByGUID" desp="按id获取凭证"><![CDATA[
            select cmatchreceiptstatus,cflowstatus,crelstatus,cvoucherid,cvoucode,cdatasource,csourceorganid,csourceorganname,cvoucherdate,cguid clistguid from da_fc_voucherinfo where #equal(cguid,cguid)
        ]]></i>
        <i id="queryBillAndVoucherRel" desp="按id获取凭证单据关联"><![CDATA[
            select a.cbillcode,a.cbillid,'02' cloglistflag from da_fc_billandvoucherrel a where #equal(a.clistguid,clistguid)
        ]]></i>
        <i id="queryTicketAndVoucherRel" desp="按id获取凭证票据关联"><![CDATA[
            select a.ccode,a.cnumber,'08' cloglistflag from  da_fc_voucherandticketrel a where #equal(a.clistguid,clistguid)
        ]]></i>
        <i id="queryMDVoucher" desp="获取元数据id"><![CDATA[
            select * from da_fc_md_voucher where #equal(clistguid,clistguid)
        ]]></i>
    </sql>
</sqls>