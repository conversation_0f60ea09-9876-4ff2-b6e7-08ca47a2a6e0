<?xml version="1.0" encoding="UTF-8"?>
<sqls>
    <sql group="da_fc_retrieval_sql">
        <i id="queryListVoucherDataByBill" desp="根据查询条件查询资料检索-记账凭证（包括单据编号和单据日期）的凭证信息"><![CDATA[
           SELECT
                vi.cvoutypename AS cvoutypeguid_name,
                vi.cvoucode AS cvoucherno,
                vi.cbustypename AS cbustypeguid_name,
                case cgenpdfstatus when '1' then '已生成' when '0' then '未生成' else null end as cgenpdfstatus_name,
                vi.*
            FROM da_fc_voucherinfo vi
            WHERE
                EXISTS (
                    SELECT
                        cvoucherid
                    FROM
                        da_api_fk_file_collection api
                    WHERE
                        #equal(api.cOrgnId, corgnid) and $between(api.ddate, ddate1, ddate2)
                        and $like(api.cbillcode, cbillcode)  and vi.cvoucherid = api.cvoucherid
                )
        ]]></i>
        <i id="queryListVoucherData" desp="根据查询条件查询资料检索-记账凭证（不包括单据编号和单据日期）"><![CDATA[
            SELECT
                vi.cvoutypename AS cvoutypeguid_name,
                vi.cvoucode AS cvoucherno,
                vi.cbustypename AS cbustypeguid_name,
                case cgenpdfstatus when '1' then '已生成' when '0' then '未生成' else null end as cgenpdfstatus_name,
                vi.*
            FROM da_fc_voucherinfo vi
            WHERE 1=1
        ]]></i>
        <i id="queryListVoucherByGuidAndOrgndId" desp="根据cguid和组织id查询凭证类型id"><![CDATA[
            SELECT max(da.cguid) as cguid FROM da_fc_fap_vouchertype da where $in(da.cGuid, guidList) and $equal(da.corgnid, orgnId) GROUP BY da.cName
        ]]></i>
        <i id="queryListVoucherByOrgndId" desp="根据组织id查询凭证类型id"><![CDATA[
            SELECT max(da.cguid) as cguid FROM da_fc_fap_vouchertype da where $equal(da.corgnid, orgnId) GROUP BY da.cName
        ]]></i>
        <i id="queryListVoucherNameByGuidAndOrgndId" desp="根据cguid和组织id查询凭证类型名称"><![CDATA[
            SELECT da.cname FROM da_fc_fap_vouchertype da where $in(da.cGuid, guidList) and $equal(da.corgnid, orgnId) GROUP BY da.cName
        ]]></i>
    </sql>
</sqls>