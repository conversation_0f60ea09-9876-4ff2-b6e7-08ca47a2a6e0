<?xml version="1.0" encoding="UTF-8"?>
<root use-spring-env="true">
	<prop name="base_package">com.aisino</prop>

	<prop name="debug">true</prop>
	<prop name="traceLog">true</prop>
	<prop name="traceSqlLog">false</prop>
	<prop name="checkLicFile">true</prop>
	<!-- springboot时也配置为false -->
        <prop name="servlet3_support">false</prop>
	<!-- 微服务模式 -->
	<prop name="micservice">true</prop>
	
	<prop name="custom.cache_key_generator">com.aisino.aps.common.custom.SPELCacheKeyGenerator</prop>

	<!-- 是否启用加解密 需要跟前端加密配置配合使用-->
	<prop name="custom.decrypt">true</prop>

	<prop name="first_page">/canvas/login3</prop>
	<prop name="home_page">/canvas/index3</prop>
	<!-- <prop name="basePath">http://localhost:9888</prop> -->
	<prop name="RefererWhiteList">http://localhost:9888</prop>
	<prop name="task_scheduling_type">Quartz</prop>
	<!--上传白名单，分号分隔，如：.jpg;.png -->
	<!-- <prop name="upload_white_list">.7Z;.OFD;.XLS;.XLSX;.XLSM;.doc;.docx;.xls;.xlsx;.wps;.txt;.ppt;.pptx;.pdf;.ofd;.rtf;.vsd;.vsdx;.rar;.zip;.bmp;.jpg;.jpeg;.gif;.tif;.png;.pic;.xml;.tar;.7z</prop> -->
	<prop name="upload_white_list">.OFD;.XLS;.XLSX;.doc;.docx;.xls;.xlsx;.ppt;.pptx;.png;.jpg;.jpeg;.bmp;.tif;.tiff;.zip;.rar;.txt;.html;.xml;.pub;.ofd;.pdf;.tar;.TAR;.7z;.7Z;</prop>
	<prop name="upload_limit">51200</prop>
	<!--自定义异常处理类，会在AOS+平台异常处理之前优先执行-->
	<prop name="custom.handler_exception_resolver">com.aisino.aps.common.custom.ApsProcessHandlerException</prop>
	<!-- guid处理类 -->
	<prop name="custom.handler_guid">com.aisino.aps.guid.ApsGuid</prop>

	<aps-guid>
		<!--
            手动指定多个机器id，如: 1,2（最大256）
            指定为 ip 自动获取
            配置为 redis2 自动从 redis 获取2个机器id，当时钟回拨时会切换机器id避免重复
        -->
		<!--<prop name="machineIds">1,2</prop>-->
		<prop name="machineIds">ip</prop>
		<!--<prop name="machineIds">redis</prop>-->
		<!--<prop name="machineIds">redis1</prop>-->
		<!--<prop name="machineIds">redis2</prop>-->
		<!--<prop name="machineIds">redis3</prop>-->
		<!--
            seconds 秒级（默认值），一秒最多262144个ID，
            milliseconds 毫秒级，每毫秒256个ID，每秒256000个ID
        -->
		<prop name="idType">seconds</prop>
	</aps-guid>
	
	<prop name="cache_type">redis</prop>
		<cache-redis>
		<prop name="ip">************</prop>
		<prop name="port">6377</prop>
		<prop name="password">dafk_linux_redis_2023</prop>
		<prop name="timeout">10000</prop>
		</cache-redis>

	<!-- 银行引入数据接口配置：正式环境 -->
	<!-- <aps-data-server> -->
		<!-- 数据服务平台通用设置 -->
		<!-- <prop name="aps.data.url">https://yxapi.aisino.com</prop>
		<prop name="aps.data.tokenUrl">/auth/getAccessToken</prop> -->
		<!-- 数据服务平台 银行同步服务 -->
		<!-- <prop name="bank.bankUrl.appKey">wixn63VH</prop>
		<prop name="bank.bankUrl.appSecret">3d43305822b896f210a4bb260ec5cd7717947082</prop>
		<prop name="bank.bankUrl">/invoiceApi/api/queryBankList</prop>
	</aps-data-server> -->

	<!-- 银行引入数据接口配置：测试环境 -->
	<aps-data-server>
		<!-- 数据服务平台通用设置 -->
		<prop name="aps.data.url">http://************:40009</prop>
		<prop name="aps.data.tokenUrl">/auth/getAccessToken</prop>
		<!-- 数据服务平台 银行同步服务 -->
		<prop name="bank.bankUrl.appKey">fAzgPeNN</prop>
		<prop name="bank.bankUrl.appSecret">d7b54fd4b2095b71f07234caa53ad95972a7f65f</prop>
		<prop name="bank.bankUrl">/invoiceApi/api/queryBankList</prop>
	</aps-data-server>

	<!--档案压力测试资料收集接口地址-->
	<!-- <prop name="call-dayc-zlsj">http://172.19.9.25:9888/da/api/collection/collectionData</prop> -->

	<!-- 是否启动全文检索 -->
	<prop name="enableFTR">false</prop>
	<prop name="elasticsearch.url">172.19.9.25:9218</prop>
	
	<prop name="imm-isSingleNode">1</prop>
	<prop name="imm-bucketType">1</prop>
	<prop name="imm_base_url">http://************:9888</prop>

	<!-- 凭证收集时候，是否需要补充关联单据的凭证id -->
	<prop name="isupdate_voucherid">true</prop>

	<prop name="da_url">http://************:9888/da/api/collection/collectionData</prop>

	<!--文件下载配置-->
	<!-- 是否开启收集消费者 -->
	<prop name="StartCollMQorNot">true</prop>
	<!-- 文件下载线程个数 -->
	<prop name="MQ.eaconsumer.collfile.nums">3</prop>
	<!-- 交换机名称 直接下载文件-->
	<prop name="DAfile-exchange">DAfile-exchange</prop>
	<!--消息队列名称 直接下载文件-->
	<prop name="DAfile-queue">DAfile-queue-local</prop>
	<!--路由密钥 直接下载文件-->
	<prop name="DAfile-key">DAfile-key</prop>
	<!-- 文件包处理数据线程个数 -->
	<prop name="MQ.eaconsumer.colldata.nums">3</prop>
	<!-- 交换机名称 收集-->
	<prop name="DAfile-exchange-collection">DAfile-exchange-collection</prop>
	<!--消息队列名称 收集-->
	<prop name="DAfile-queue-collection">DAfile-queue-collection-local</prop>
	<!--路由密钥 收集-->
	<prop name="DAfile-key-collection">DAfile-key-collection</prop>
	<!-- 是否启用交换机 -->
	<prop name="useExchange">true</prop>
	<!-- 交换机类型 -->
	<prop name="exchangeType">topic</prop>
	<!-- 适配第三方单据mq -->
	<prop name="DAfile-queue-bill-tpapi">DAfile-queue-bill-tpapi-local</prop>
	<!-- 适配第三方回单mq -->
	<prop name="DAfile-queue-banksreceipt-tpapi">DAfile-queue-banks-tpapi-local</prop>
	<!-- 适配第三方流水mq -->
	<prop name="DAfile-queue-bankstatement-tpapi">DAfile-queue-bankstatement-tpapi-local</prop>
	<!-- 适配第三票据mq -->
	<prop name="DAfile-queue-ticket-tpapi">DAfile-queue-ticket-tpapi-local</prop>
	<!-- 收集相关mq -->
	<prop name="rabbitmq.type">rabbitmq</prop>
	<prop name="rabbitmq.url">************</prop>
	<prop name="rabbitmq.port">5673</prop>
	<prop name="rabbitmq.username">admin</prop>
	<prop name="rabbitmq.password">xiiJV1cGMjY</prop>

	<!-- 下载目录名固定为 datmpfiles -->
	<prop name="da_tmpfile_downloadurl">/yxda/daserver/datmpfiles</prop>
	<!-- 银行回单临时存储路径 -->
	<prop name="tempFilePath">/yxda/daserver/datmpfiles</prop>
	<prop name="tempImagePath">/yxda/daserver/datmpfiles</prop>
	
	<!-- 增加启动加载产品的配置，如果启动报错产品初始化失败，请查看是否有配置 -->
	<aps-product>
		<prop name="id">da</prop>
		<prop name="pcurl">da</prop>
		<prop name="name">云档案</prop>
		<prop name="h5url">mda</prop>
		<prop name="isort">1</prop>
		<prop name="cicon">ifont icon-yuncaiwu</prop>
		<prop name="adurl">da/#/guanggaodemo/a/b/c</prop>
		<prop name="baseurl">DAAPI</prop>
		<!--updatePolicy 更新策略，可选值：update 更新，ignore 忽略，不做任何处理，默认 update-->
		<prop name="updatePolicy">ignore</prop>
	</aps-product>
	<!-- 日志服务，需要pom中排除掉aosplus-mis_log -->
	<aps-log>
	   <!-- 日志存储方式，db - 数据库，es - Elasticsearch -->
	   <prop name="store">db</prop>
	   <!-- 是否记录业务日志，默认为true -->
	   <prop name="aosLog">true</prop>
	   <!-- 是否记录追踪日志，默认为false -->
	   <prop name="traceLog">true</prop>
	   <!-- 是否记录追踪SQL日志，默认为false -->
	   <prop name="sqlLog">true</prop>
	   <!-- 业务和追踪日志记录到哪个数据库，不配置时使用默认库，配置后使用指定库 -->
	   <!-- 多租户时默认记录到各个租户库，配置后，会统一到同一个数据库，使用不同表名区分 -->
	   <!-- <prop name="dsId">oracle_local</prop> -->
	   <!-- es 索引名，和日志查询有关 -->
	   <prop name="esIndex">applog*,index1,index2...</prop>
	   <!-- 是否导出备份日志  -->
  	   <prop name="exportLog">true</prop>
  	   <!-- 是否导出备份追踪日志  -->
  	   <prop name="exportTraceLog">true</prop>
  	   <!-- 备份文件存储方式 local oss  -->
  	   <prop name="exportFileType">local</prop>
  	   <!-- 备份文件存储路径  -->
  	   <prop name="exportFilePath">/yxda/daserver/logbak</prop>
	   <!-- 业务日志备份定时任务cron （按月为单位）-->
    	   <prop name="exportLogJobCron">0 0 1 * * ?</prop>
    	   <!-- 追踪日志备份定时任务cron （按月为单位）-->
    	   <prop name="exportTraceLogJobCron">0 0 2 * * ?</prop>
	</aps-log>
	<!-- 是否支持多数据中心 -->
	<prop name="supportDataCenter">true</prop>
	
	<!--libreoffice端口配置-->
	<prop name="libreofficeport">8100,8101,8102,8103,8104</prop>
	<prop name="office_home">/opt/libreoffice7.4/</prop>
	
	<!-- 同步接口配置 -->
	<!-- 接口类型 1为同步 2为异步 -->
	<prop name="API_TYPE">2</prop>
	<!-- 调用费控接口地址 仅在接口类型为1时有效 -->
	<prop name="FK_APIURL">http://************:8089/fk/edoc/archive</prop>

	<!-- 制章配置 -->
	<prop name="ca_url">http://************:8090/da_caservice/caServlet</prop>
	<prop name="ca_key_path">/yxda/daserver/config/ca_key.txt</prop>

	<!-- 电子档案消息队列 -->
	<prop name="MQ.host.type">rabbitmq</prop>
	<prop name="MQ.host.ip">************</prop>
	<prop name="MQ.host.port">5673</prop>
	<prop name="MQ.host.user">admin</prop>
	<prop name="MQ.host.pwd">xiiJV1cGMjY</prop>
	<!-- 实体组卷队列名 -->
	<prop name="EntityFileQueueName">AFKFilingQueue_local</prop>
	<!--是否启动MQ队列实体组卷-->
	<prop name="StartEFMQorNot">false</prop>
	<!--电子归档MQ队列名-->
   	 <prop name="EFileQueueName">EAArchivingQueue_local</prop>
	<!--是否启动MQ队列电子归档-->
	<prop name="StartEAMQorNot">false</prop>
	<!--票据归档MQ-->
	<prop name="InvFileQueueName">InvArchivingQueue_local</prop>
	<!--是否启动MQ队列票据归档-->
	<prop name="StartInvMQorNot">false</prop>

	<!--全文检索MQ服务-->
	<prop name="EsQueueName">ESQueue_local</prop>
	<prop name="MQ.host.ip">************</prop>
	<prop name="MQ.host.port">5673</prop>
	<prop name="MQ.host.user">admin</prop>
	<prop name="MQ.host.pwd">xiiJV1cGMjY</prop>
	<!--是否启动全文检索MQ队列-->
	<prop name="StartEsMQorNot">false</prop>
	<!--es消费者数量-->
	<prop name="MQ.esconsumer.nums">1</prop>

	<!--是否调用签章 默认为true-->
	<prop name="isCallCA">false</prop>	
	
	<!-- 档案读取对象存储类型支持 local aliyun minio tencent s3 huawei -->
	<amazonS3>
		<prop name="endpoint">http://************:8000</prop>
		<prop name="accessKey">da_test</prop>
		<prop name="secretKey">datest123</prop>
		<prop name="bucketName">da31</prop>
	</amazonS3>
	<fastdfs>
		<prop name="fastdfspath"></prop>
	</fastdfs>
	<ftp>
		<prop name="host"></prop>
		<prop name="port"></prop>
		<prop name="username"></prop>
		<prop name="password"></prop>
		<prop name="basePath"></prop>
	</ftp>
	<sftp>
		<prop name="endpoint"></prop>
		<prop name="accessKey"></prop>
		<prop name="secretKey"></prop>
		<prop name="bucketName"></prop>
		<prop name="localFilePath"></prop>
	</sftp>

	<aps-imm>
   		<prop name="type">aliyun</prop>
   		<prop name="endpoint">oss-cn-beijing.aliyuncs.com</prop>
   		<prop name="accessKey">LTAI5tPS2WVcPB5ULJbqM6Ue</prop>
   		<prop name="secretKey">******************************</prop>
   		<prop name="bucketName">xmabcbackoss</prop>
   		<prop name="localFilePath"></prop>
	</aps-imm>
	<local>
		<prop name="type">local</prop>
		<prop name="localFilePath">/yxda/daserver/www</prop>
	</local>



	<!-- 跨域 -->
	<prop name="supportCrossDomain">true</prop>
	<prop name="Access-Control-Allow-Origin">*</prop>
	
	<!-- <prop name="imm-aliyunoss-endpoint">oss-cn-beijing.aliyuncs.com</prop>
	<prop name="imm-aliyunoss-accessKeyId">LTAI5tPS2WVcPB5ULJbqM6Ue</prop>
	<prop name="imm-aliyunoss-accessKeySecret">******************************</prop>
	<prop name="imm-aliyunoss-bucketName">xmabcbackoss</prop>
	<prop name="imm-aliyunoss-filePath"></prop> -->
	 
	<!--阿里云配置 附件-->
	<prop name="imm-fileSysType">aliyunoss</prop>
	<prop name="aliyunoss-endpoint">oss-cn-beijing.aliyuncs.com</prop>
	<prop name="aliyunoss-accessKeyId">LTAI5tPS2WVcPB5ULJbqM6Ue</prop>
	<prop name="aliyunoss-accessKeySecret">******************************</prop>
	<prop name="aliyunoss-bucketName">xmabcbackoss</prop>
	<prop name="aliyunoss-filePath"></prop>
	
	<login>
		<prop name="header">
			<![CDATA[
	                <img id="logo_img" src="" style="margin-top:5px;height:50px;">
	            ]]>
		</prop>
		<prop name="content">
			<![CDATA[
	                <div class="contentText" style="font-size:34px;">APS开发平台</div>
	                <div class="contentText" style="font-size:20px;color:#00A0FF;margin-top:10px;">一个面向开发者的平台</div>
	            ]]>
		</prop>
		<prop name="footer">
			<![CDATA[
	                <div></div>
	            ]]>
		</prop>

	</login>
	<index>
		<prop name="LogoImage"></prop>
		<prop name="LogoText">APS开发平台</prop>
	</index>
	
	<aps-oss>
		<prop name="type">minio</prop>
		<prop name="endpoint">http://************:8000</prop>
		<prop name="accessKey">da_test</prop>
		<prop name="secretKey">datest123</prop>
		<prop name="bucketName">da31</prop>
	</aps-oss>
	<database default="mysql_local">
		<ds id="mysql_local">
			<prop name="desc">档案测试库</prop>
			<prop name="type">mysql</prop>
			<prop name="driver">com.mysql.cj.jdbc.Driver</prop>
			<prop name="url">******************************************************************************************************************</prop>
			<prop name="username">dacfg</prop>
			<prop name="password">Dayz.gxkfb.202306</prop>
			<prop name="decrypt">false</prop>
		</ds>
		<prop name="decrypt">false</prop>
	</database>
	 <aps-quartz type="Quartz">
        <prop name="org.quartz.scheduler.instanceName">aosscheduler</prop>
        <prop name="org.quartz.scheduler.schedulerName">dascheduler</prop>
        <prop name="org.quartz.scheduler.instanceId">AUTO</prop>
        <prop name="org.quartz.threadPool.class">org.quartz.simpl.SimpleThreadPool</prop>
        <prop name="org.quartz.threadPool.threadCount">10</prop>
        <prop name="org.quartz.threadPool.threadPriority">5</prop>
        <prop name="org.quartz.threadPool.threadsInheritContextClassLoaderOfInitializingThread">true</prop>

        <prop name="org.quartz.jobStore.misfireThreshold">120000</prop>
        <prop name="org.quartz.jobStore.class">org.quartz.impl.jdbcjobstore.JobStoreTX</prop>
        <prop name="org.quartz.jobStore.driverDelegateClass">org.quartz.impl.jdbcjobstore.StdJDBCDelegate</prop>
        <prop name="org.quartz.jobStore.useProperties">true</prop>
        <prop name="org.quartz.jobStore.dataSource">myDS</prop>
        <prop name="org.quartz.jobStore.tablePrefix">QRTZ_</prop>
        <prop name="org.quartz.jobStore.isClustered">false </prop>
        <prop name="org.quartz.jobStore.clusterCheckinInterval">15000</prop>
	
	<prop name="org.quartz.enable">true</prop>
	<!-- <prop name="org.quartz.stopaos">true</prop> -->
        <prop name="org.quartz.dataSource.myDS.connectionProvider.class">com.alibaba.druid.support.quartz.DruidQuartzConnectionProvider</prop>
        <!-- <prop name="org.quartz.dataSource.myDS.driverClassName">com.mysql.jdbc.Driver</prop>
        <prop name="org.quartz.dataSource.myDS.Url">***********************************************************</prop>
        <prop name="org.quartz.dataSource.myDS.username">aps</prop>
        <prop name="org.quartz.dataSource.myDS.password">apsdev2021@</prop>
        <prop name="org.quartz.dataSource.myDS.maxActive">30</prop> -->
    </aps-quartz>
	<!-- 本地上传配置 -->
	<!-- fileSysType 文件服务器类型,目前支持的存储方式分别是本地服务器硬盘(local)、数据库(db)、FastDFS文件服务器(fastdfs),默认为local -->
	<prop name="fileSysType">local</prop>
	<prop name="linuxFileSavePath">/yxda/daserver/file/tempuploaddir/upload</prop>
	<!-- <prop name="linuxFileSavePath">www/upload/</prop> -->
	<prop name="imm-fileSysType">aliyunoss</prop>
	<!--    <prop name="linuxFileSavePath">/usr/local/file/tempuploaddir/upload</prop>-->
	<prop name="imm-FileSavePath">www/imm-upload</prop>
	<prop name="custom.handler_invoker">com.aisino.aps.common.aop.ApsHandlerInvoker</prop>
	<prop name="custom.view_resolver">com.aisino.aps.common.aop.ApsViewResolver</prop>

	<druid>
		<prop name="initialSize">3</prop>    <!-- 初始化连接数量 -->
		<prop name="maxActive">200</prop>    <!-- 最大并发连接数 -->
		<prop name="minIdle">1</prop>    <!-- 最小空闲连接数 -->
		<prop name="maxWait">60000</prop>    <!-- 获取连接等待超时的时间 -->
		<prop name="removeAbandanded">true</prop>    <!-- 超过时间限制是否回收，配置removeAbandoned对性能会有一些影响，建议怀疑存在泄漏之后再打开 -->
		<prop name="removeAbandonedTimeout">1800</prop>    <!-- 超时时间；单位为秒。1800秒=30分钟 -->
		<prop name="logAbandoned">true</prop>    <!-- 关闭abanded连接时输出错误日志 -->
		<prop name="timeBetweenEvictionRunsMillis">60000</prop>    <!-- 间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
		<prop name="minEvictableIdleTimeMillis">300000</prop>    <!-- 一个连接在池中最小生存的时间，单位是毫秒 -->
		<!-- <prop name="validationQuery">SELECT 1 FROM DUAL</prop>-->   <!-- oracle下配置这个语句 -->
		<prop name="validationQuery">SELECT 'x'</prop>    <!-- 用来检测连接是否有效的sql，要求是一个查询语句 -->
		<prop name="testWhileIdle">true</prop>    <!-- 申请连接的时候检测 -->
		<prop name="testOnBorrow">true</prop>    <!-- 申请连接时执行validationQuery检测连接是否有效，配置为true会降低性能 -->
		<prop name="testOnReturn">true</prop>    <!-- 归还连接时执行validationQuery检测连接是否有效，配置为true会降低性能 -->
		<!-- sqlite下需要注释下面两句 -->
		<!-- 打开PSCache，并且指定每个连接上PSCache的大小 -->
		<!--<prop name="poolPreparedStatements">true</prop>
        <prop name="maxPoolPreparedStatementPerConnectionSize">20</prop>-->
		<!-- stat,log4j,wall 监控统计stat，log4j日志，防御SQL注入wall，注意，添加wall后，1=1这种会报错 -->
		<prop name="filters">stat,slf4j</prop>
		<prop name="druidServlet.allow">124.127.114.42</prop>
		<!--<prop name="druidServlet.loginUsername">admin</prop>
        <prop name="druidServlet.allow">127.0.0.1;172.19.1.142;124.127.114.42;124.204.76.42</prop>
        <prop name="druidServlet.loginPassword">admin1</prop>-->
		<prop name="druidServlet.resetEnable">false</prop>
	</druid>
</root>
